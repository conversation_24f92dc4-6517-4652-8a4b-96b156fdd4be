<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/common.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .tab-active {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .status-pending {
            background: linear-gradient(45deg, #f59e0b, #d97706);
        }
        .status-processing {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
        .status-completed {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .status-cancelled {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }
        
        /* 提高文字对比度 */
        .text-slate-400 {
            color: #94a3b8 !important;
        }
        
        /* 键盘导航样式 */
        .keyboard-navigation *:focus {
            outline: 2px solid #3b82f6 !important;
            outline-offset: 2px !important;
        }
        
        /* 最小点击区域 */
        button, .nav-item, [role="button"] {
            min-width: 44px;
            min-height: 44px;
        }
        
        /* 加载状态 */
        .loading {
            pointer-events: none;
            opacity: 0.7;
        }
        
        /* 动画减少偏好 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <a href="index.html" class="flex items-center space-x-2 text-slate-300 hover:text-white" aria-label="返回首页">
                    <i data-lucide="arrow-left" class="w-5 h-5" aria-hidden="true"></i>
                    <span>返回</span>
                </a>
                <h1 class="text-lg font-semibold text-white">订单管理</h1>
                <button class="text-slate-300 hover:text-white" data-action="search" aria-label="搜索订单" title="搜索订单">
                    <i data-lucide="search" class="w-5 h-5" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 订单统计 -->
    <div class="max-w-md mx-auto px-4 py-6">
        <div class="bg-slate-800 rounded-xl p-6 mb-6">
            <div class="grid grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-orange-400">3</div>
                    <div class="text-sm text-slate-400 mt-1">待处理</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-blue-400">5</div>
                    <div class="text-sm text-slate-400 mt-1">进行中</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-400">28</div>
                    <div class="text-sm text-slate-400 mt-1">已完成</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-red-400">2</div>
                    <div class="text-sm text-slate-400 mt-1">已取消</div>
                </div>
            </div>
        </div>

        <!-- 标签页切换 -->
        <div class="flex bg-slate-800 rounded-lg p-1 mb-6 overflow-x-auto" role="tablist" aria-label="订单状态筛选">
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium transition-all tab-active" data-tab="all" role="tab" tabindex="0" aria-selected="true" aria-controls="all-panel">
                全部 (38)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="pending" role="tab" tabindex="-1" aria-selected="false" aria-controls="pending-panel">
                待处理 (3)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="processing" role="tab" tabindex="-1" aria-selected="false" aria-controls="processing-panel">
                进行中 (5)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="completed" role="tab" tabindex="-1" aria-selected="false" aria-controls="completed-panel">
                已完成 (28)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="cancelled" role="tab" tabindex="-1" aria-selected="false" aria-controls="cancelled-panel">
                已取消 (2)
            </button>
        </div>

        <!-- 全部订单 -->
        <div id="all" class="tab-content space-y-4">
            <!-- 跑腿订单 - 进行中 -->
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="package" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-200">代取快递服务</h3>
                            <p class="text-sm text-slate-400">订单号：ER202401150001</p>
                        </div>
                    </div>
                    <span class="status-processing text-white text-xs px-3 py-1 rounded-full font-medium">
                        进行中
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-400">取件地址</span>
                        <span class="text-slate-200">菜鸟驿站(朝阳店)</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">送达地址</span>
                        <span class="text-slate-200">朝阳区建国路88号</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">预计送达</span>
                        <span class="text-slate-200">今天 15:30</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">¥8.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-slate-600 hover:bg-slate-500 text-slate-300 px-4 py-2 rounded-lg text-sm transition-colors" data-action="contact-runner" data-order-id="ER202401150001" aria-label="联系订单ER202401150001的跑腿员">
                            联系跑腿员
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" data-action="view-detail" data-order-id="ER202401150001" aria-label="查看订单ER202401150001详情">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>

            <!-- 出行订单 - 待处理 -->
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="car" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-200">机场拼车服务</h3>
                            <p class="text-sm text-slate-400">订单号：TR202401150002</p>
                        </div>
                    </div>
                    <span class="status-pending text-white text-xs px-3 py-1 rounded-full font-medium">
                        待处理
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-400">出发地点</span>
                        <span class="text-slate-200">朝阳区建国路</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">目的地</span>
                        <span class="text-slate-200">首都机场T3航站楼</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">出发时间</span>
                        <span class="text-slate-200">明天 08:00</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">¥45.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" data-action="cancel-order" data-order-id="TR202401150002" aria-label="取消订单TR202401150002">
                            取消订单
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" data-action="view-detail" data-order-id="TR202401150002" aria-label="查看订单TR202401150002详情">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商铺订单 - 已完成 -->
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=32&h=32&fit=crop&crop=center" alt="餐厅" class="w-8 h-8 rounded-lg object-cover">
                        <div>
                            <h3 class="font-semibold text-slate-200">老北京炸酱面馆</h3>
                            <p class="text-sm text-slate-400">订单号：SH202401140003</p>
                        </div>
                    </div>
                    <span class="status-completed text-white text-xs px-3 py-1 rounded-full font-medium">
                        已完成
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="text-sm text-slate-300">
                        <div class="flex justify-between">
                            <span>老北京炸酱面 × 1</span>
                            <span>¥28.00</span>
                        </div>
                        <div class="flex justify-between mt-1">
                            <span>小菜拼盘 × 1</span>
                            <span>¥12.00</span>
                        </div>
                        <div class="flex justify-between mt-1 text-slate-400">
                            <span>配送费</span>
                            <span>¥3.00</span>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">¥43.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-slate-600 hover:bg-slate-500 text-slate-300 px-4 py-2 rounded-lg text-sm transition-colors" data-action="rate-order" data-order-id="SH202401140003" aria-label="评价订单SH202401140003">
                            评价订单
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" data-action="reorder" data-order-id="SH202401140003" aria-label="重新下单SH202401140003">
                            再来一单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 扫码呼叫订单 - 已完成 -->
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="qr-code" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-200">扫码呼叫服务</h3>
                            <p class="text-sm text-slate-400">订单号：QR202401130004</p>
                        </div>
                    </div>
                    <span class="status-completed text-white text-xs px-3 py-1 rounded-full font-medium">
                        已完成
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-400">服务类型</span>
                        <span class="text-slate-200">技术咨询</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">服务时长</span>
                        <span class="text-slate-200">25分钟</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">完成时间</span>
                        <span class="text-slate-200">2024-01-13 14:30</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">免费</div>
                    <div class="flex space-x-2">
                        <button class="bg-slate-600 hover:bg-slate-500 text-slate-300 px-4 py-2 rounded-lg text-sm transition-colors" onclick="rateOrder('QR202401130004')">
                            评价服务
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" onclick="viewOrderDetail('QR202401130004')">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="text-center py-8">
                <button class="bg-slate-700 hover:bg-slate-600 text-slate-300 px-6 py-3 rounded-lg transition-colors" data-action="load-more" aria-label="加载更多订单">
                    <span class="load-text">加载更多订单</span>
                    <span class="loading-text hidden">加载中...</span>
                </button>
            </div>
        </div>

        <!-- 待处理订单 -->
        <div id="pending" class="tab-content space-y-4 hidden">
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="car" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-200">机场拼车服务</h3>
                            <p class="text-sm text-slate-400">订单号：TR202401150002</p>
                        </div>
                    </div>
                    <span class="status-pending text-white text-xs px-3 py-1 rounded-full font-medium">
                        待处理
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-400">出发时间</span>
                        <span class="text-slate-200">明天 08:00</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">路线</span>
                        <span class="text-slate-200">朝阳区 → 首都机场T3</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">¥45.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" onclick="cancelOrder('TR202401150002')">
                            取消订单
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" onclick="viewOrderDetail('TR202401150002')">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进行中订单 -->
        <div id="processing" class="tab-content space-y-4 hidden">
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="package" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-200">代取快递服务</h3>
                            <p class="text-sm text-slate-400">订单号：ER202401150001</p>
                        </div>
                    </div>
                    <span class="status-processing text-white text-xs px-3 py-1 rounded-full font-medium">
                        进行中
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-400">当前状态</span>
                        <span class="text-blue-400">跑腿员已到达取件点</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">预计送达</span>
                        <span class="text-slate-200">今天 15:30</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">¥8.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-slate-600 hover:bg-slate-500 text-slate-300 px-4 py-2 rounded-lg text-sm transition-colors" onclick="contactRunner('ER202401150001')">
                            联系跑腿员
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" onclick="trackOrder('ER202401150001')">
                            实时跟踪
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已完成订单 -->
        <div id="completed" class="tab-content space-y-4 hidden">
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=32&h=32&fit=crop&crop=center" alt="餐厅" class="w-8 h-8 rounded-lg object-cover">
                        <div>
                            <h3 class="font-semibold text-slate-200">老北京炸酱面馆</h3>
                            <p class="text-sm text-slate-400">订单号：SH202401140003</p>
                        </div>
                    </div>
                    <span class="status-completed text-white text-xs px-3 py-1 rounded-full font-medium">
                        已完成
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="text-sm text-slate-300">
                        <div class="flex justify-between">
                            <span>老北京炸酱面 × 1</span>
                            <span>¥28.00</span>
                        </div>
                        <div class="flex justify-between mt-1">
                            <span>小菜拼盘 × 1</span>
                            <span>¥12.00</span>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-green-400">¥43.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-slate-600 hover:bg-slate-500 text-slate-300 px-4 py-2 rounded-lg text-sm transition-colors" onclick="rateOrder('SH202401140003')">
                            评价订单
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" onclick="reorder('SH202401140003')">
                            再来一单
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已取消订单 -->
        <div id="cancelled" class="tab-content space-y-4 hidden">
            <div class="order-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="package" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-200">代购服务</h3>
                            <p class="text-sm text-slate-400">订单号：ER202401120005</p>
                        </div>
                    </div>
                    <span class="status-cancelled text-white text-xs px-3 py-1 rounded-full font-medium">
                        已取消
                    </span>
                </div>
                
                <div class="bg-slate-700 rounded-lg p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-slate-400">取消原因</span>
                        <span class="text-slate-200">用户主动取消</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-slate-400">取消时间</span>
                        <span class="text-slate-200">2024-01-12 16:20</span>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-lg font-bold text-slate-400">¥15.00</div>
                    <div class="flex space-x-2">
                        <button class="bg-slate-600 hover:bg-slate-500 text-slate-300 px-4 py-2 rounded-lg text-sm transition-colors" onclick="viewOrderDetail('ER202401120005')">
                            查看详情
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" onclick="reorder('ER202401120005')">
                            重新下单
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="text-center py-16 hidden">
            <div class="w-24 h-24 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="shopping-bag" class="w-12 h-12 text-slate-500"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-300 mb-2">暂无订单</h3>
            <p class="text-slate-500 mb-6">快去下单体验我们的服务吧</p>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors" onclick="goToHome()">
                去首页看看
            </button>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            if (typeof LocalHelper !== 'undefined') {
                LocalHelper.App.init();
            }
            
            // 初始化 Lucide 图标
            lucide.createIcons();
            
            // 绑定标签页切换事件
            const tabContainer = document.querySelector('[role="tablist"]');
            if (tabContainer) {
                tabContainer.addEventListener('click', function(e) {
                    const tabBtn = e.target.closest('[data-tab]');
                    if (tabBtn) {
                        e.preventDefault();
                        
                        const tab = tabBtn.dataset.tab;
                        
                        // 更新按钮状态
                        document.querySelectorAll('.tab-btn').forEach(b => {
                            b.classList.remove('tab-active');
                            b.classList.add('text-slate-400');
                            b.setAttribute('aria-selected', 'false');
                            b.setAttribute('tabindex', '-1');
                        });
                        tabBtn.classList.add('tab-active');
                        tabBtn.classList.remove('text-slate-400');
                        tabBtn.setAttribute('aria-selected', 'true');
                        tabBtn.setAttribute('tabindex', '0');
                        
                        // 显示对应内容
                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.classList.add('hidden');
                        });
                        document.getElementById(tab).classList.remove('hidden');
                    }
                });
            }
            
            // 绑定订单操作事件
            document.addEventListener('click', function(e) {
                const actionBtn = e.target.closest('[data-action]');
                if (actionBtn) {
                    e.preventDefault();
                    const action = actionBtn.dataset.action;
                    const orderId = actionBtn.dataset.orderId;
                    
                    handleOrderAction(action, orderId, actionBtn);
                }
            });
            
            // 绑定键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    const focusedTab = document.activeElement;
                    if (focusedTab && focusedTab.getAttribute('role') === 'tab') {
                        e.preventDefault();
                        const tabs = Array.from(document.querySelectorAll('[role="tab"]'));
                        const currentIndex = tabs.indexOf(focusedTab);
                        let nextIndex;
                        
                        if (e.key === 'ArrowRight') {
                            nextIndex = (currentIndex + 1) % tabs.length;
                        } else {
                            nextIndex = (currentIndex - 1 + tabs.length) % tabs.length;
                        }
                        
                        tabs[nextIndex].focus();
                        tabs[nextIndex].click();
                    }
                }
            });
        });
        
        // 处理订单操作
        function handleOrderAction(action, orderId, button) {
            switch(action) {
                case 'search':
                    showToast('搜索功能即将上线，敬请期待！', 'info');
                    break;
                case 'view-detail':
                    showToast(`查看订单 ${orderId} 详情功能即将上线`, 'info');
                    break;
                case 'cancel-order':
                    if (confirm(`确定要取消订单 ${orderId} 吗？`)) {
                        showToast(`订单 ${orderId} 已取消`, 'success');
                        // 这里可以添加实际的取消逻辑
                    }
                    break;
                case 'contact-runner':
                    showToast('联系跑腿员功能即将上线', 'info');
                    break;
                case 'track-order':
                    showToast('实时跟踪功能即将上线', 'info');
                    break;
                case 'rate-order':
                    showToast('评价订单功能即将上线', 'info');
                    break;
                case 'reorder':
                    showToast(`重新下单 ${orderId} 功能即将上线`, 'info');
                    break;
                case 'load-more':
                    handleLoadMore(button);
                    break;
            }
        }
        
        // 处理加载更多
        function handleLoadMore(button) {
            const loadText = button.querySelector('.load-text');
            const loadingText = button.querySelector('.loading-text');
            
            // 显示加载状态
            button.classList.add('loading');
            loadText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            
            // 模拟加载
            setTimeout(() => {
                button.classList.remove('loading');
                loadText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                showToast('暂无更多订单', 'info');
            }, 2000);
        }

        // 搜索订单
        function searchOrders() {
            alert('正在打开搜索功能...');
        }

        // 查看订单详情
        function viewOrderDetail(orderId) {
            alert(`正在查看订单详情：${orderId}`);
        }

        // 取消订单
        function cancelOrder(orderId) {
            if (confirm('确定要取消这个订单吗？')) {
                alert(`订单 ${orderId} 已取消`);
            }
        }

        // 联系跑腿员
        function contactRunner(orderId) {
            alert(`正在联系跑腿员，订单：${orderId}`);
        }

        // 实时跟踪
        function trackOrder(orderId) {
            alert(`正在打开实时跟踪，订单：${orderId}`);
        }

        // 评价订单
        function rateOrder(orderId) {
            alert(`正在打开评价页面，订单：${orderId}`);
        }

        // 再来一单
        function reorder(orderId) {
            alert(`正在重新下单，参考订单：${orderId}`);
        }

        // 加载更多
        function loadMore() {
            alert('正在加载更多订单...');
        }

        // 去首页
        function goToHome() {
            window.location.href = '../index.html';
        }
    </script>
</body>
</html>