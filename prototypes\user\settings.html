<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            min-height: 100vh;
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .setting-card {
            background: linear-gradient(135deg, #1e1e3f 0%, #252547 100%);
            border: 1px solid #2a2d4a;
            transition: all 0.3s ease;
        }
        .setting-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
        }
        .setting-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid #374151;
            transition: all 0.3s ease;
        }
        .setting-item:hover {
            background: rgba(59, 130, 246, 0.05);
            border-color: #3b82f6;
        }
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #374151;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .toggle-switch.active {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        .version-info {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
        }
    </style>
</head>
<body class="text-slate-100">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-900/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <button onclick="goBack()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                <h1 class="text-lg font-semibold">设置中心</h1>
            </div>
        </div>
    </div>

    <div class="p-4 pb-20 space-y-6">
        <!-- 通知设置 -->
        <div class="setting-card rounded-lg p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="bell" class="w-5 h-5 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">通知设置</h2>
                    <p class="text-slate-400 text-sm">管理推送通知偏好</p>
                </div>
            </div>

            <div class="space-y-3">
                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="message-circle" class="w-5 h-5 text-blue-400"></i>
                        <div>
                            <h3 class="font-medium text-white">新消息通知</h3>
                            <p class="text-slate-400 text-sm">接收新消息推送</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSwitch(this)" data-setting="message"></div>
                </div>

                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="megaphone" class="w-5 h-5 text-green-400"></i>
                        <div>
                            <h3 class="font-medium text-white">系统公告</h3>
                            <p class="text-slate-400 text-sm">接收平台重要公告</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSwitch(this)" data-setting="announcement"></div>
                </div>

                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="tag" class="w-5 h-5 text-purple-400"></i>
                        <div>
                            <h3 class="font-medium text-white">优惠券提醒</h3>
                            <p class="text-slate-400 text-sm">优惠券到期提醒</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSwitch(this)" data-setting="coupon"></div>
                </div>

                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="star" class="w-5 h-5 text-yellow-400"></i>
                        <div>
                            <h3 class="font-medium text-white">评价提醒</h3>
                            <p class="text-slate-400 text-sm">提醒您对服务进行评价</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSwitch(this)" data-setting="review"></div>
                </div>
            </div>
        </div>

        <!-- 隐私设置 -->
        <div class="setting-card rounded-xl p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="shield" class="w-5 h-5 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">隐私设置</h2>
                    <p class="text-slate-400 text-sm">保护您的个人信息</p>
                </div>
            </div>

            <div class="space-y-3">
                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="map-pin" class="w-5 h-5 text-red-400"></i>
                        <div>
                            <h3 class="font-medium text-white">位置信息</h3>
                            <p class="text-slate-400 text-sm">允许获取位置信息</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSwitch(this)" data-setting="location"></div>
                </div>

                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="phone" class="w-5 h-5 text-blue-400"></i>
                        <div>
                            <h3 class="font-medium text-white">手机号显示</h3>
                            <p class="text-slate-400 text-sm">在发布信息时显示手机号</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSwitch(this)" data-setting="phone"></div>
                </div>

                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="eye" class="w-5 h-5 text-purple-400"></i>
                        <div>
                            <h3 class="font-medium text-white">在线状态</h3>
                            <p class="text-slate-400 text-sm">显示在线状态给其他用户</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSwitch(this)" data-setting="online"></div>
                </div>

                <button onclick="showPrivacyPolicy()" class="setting-item rounded-lg p-4 flex items-center justify-between w-full text-left">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="file-text" class="w-5 h-5 text-slate-400"></i>
                        <div>
                            <h3 class="font-medium text-white">隐私政策</h3>
                            <p class="text-slate-400 text-sm">查看隐私政策详情</p>
                        </div>
                    </div>
                    <i data-lucide="chevron-right" class="w-5 h-5 text-slate-400"></i>
                </button>
            </div>
        </div>

        <!-- 账号安全 -->
        <div class="setting-card rounded-xl p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="lock" class="w-5 h-5 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">账号安全</h2>
                    <p class="text-slate-400 text-sm">管理账号安全设置</p>
                </div>
            </div>

            <div class="space-y-3">
                <button onclick="changePhone()" class="setting-item rounded-lg p-4 flex items-center justify-between w-full text-left">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="smartphone" class="w-5 h-5 text-blue-400"></i>
                        <div>
                            <h3 class="font-medium text-white">更换手机号</h3>
                            <p class="text-slate-400 text-sm">当前绑定：138****8888</p>
                        </div>
                    </div>
                    <i data-lucide="chevron-right" class="w-5 h-5 text-slate-400"></i>
                </button>

                <button onclick="showAccountInfo()" class="setting-item rounded-lg p-4 flex items-center justify-between w-full text-left">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="user-check" class="w-5 h-5 text-green-400"></i>
                        <div>
                            <h3 class="font-medium text-white">实名认证</h3>
                            <p class="text-slate-400 text-sm">已认证</p>
                        </div>
                    </div>
                    <i data-lucide="chevron-right" class="w-5 h-5 text-slate-400"></i>
                </button>

                <button onclick="confirmLogout()" class="setting-item rounded-lg p-4 flex items-center justify-between w-full text-left border-red-600/30 hover:border-red-500">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="log-out" class="w-5 h-5 text-red-400"></i>
                        <div>
                            <h3 class="font-medium text-red-400">退出登录</h3>
                            <p class="text-slate-400 text-sm">退出当前账号</p>
                        </div>
                    </div>
                    <i data-lucide="chevron-right" class="w-5 h-5 text-red-400"></i>
                </button>
            </div>
        </div>

        <!-- 发布设置 -->
        <div class="setting-card rounded-xl p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="edit" class="w-5 h-5 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">发布设置</h2>
                    <p class="text-slate-400 text-sm">管理发布偏好设置</p>
                </div>
            </div>

            <div class="space-y-3">
                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="trending-up" class="w-5 h-5 text-purple-400"></i>
                        <div>
                            <h3 class="font-medium text-white">自动置顶</h3>
                            <p class="text-slate-400 text-sm">发布时自动选择置顶</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSwitch(this)" data-setting="autoTop"></div>
                </div>
            </div>
        </div>

        <!-- 服务设置 -->
        <div class="setting-card rounded-xl p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-cyan-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="headphones" class="w-5 h-5 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">服务设置</h2>
                    <p class="text-slate-400 text-sm">管理服务相关设置</p>
                </div>
            </div>

            <div class="space-y-3">
                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="message-square" class="w-5 h-5 text-blue-400"></i>
                        <div>
                            <h3 class="font-medium text-white">自动回复</h3>
                            <p class="text-slate-400 text-sm">开启自动回复消息</p>
                        </div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSwitch(this)" data-setting="autoReply"></div>
                </div>
            </div>
        </div>

        <!-- 其他设置 -->
        <div class="setting-card rounded-xl p-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="settings" class="w-5 h-5 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">其他设置</h2>
                    <p class="text-slate-400 text-sm">应用偏好设置</p>
                </div>
            </div>

            <div class="space-y-3">
                <div class="setting-item rounded-lg p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="moon" class="w-5 h-5 text-indigo-400"></i>
                        <div>
                            <h3 class="font-medium text-white">深色模式</h3>
                            <p class="text-slate-400 text-sm">护眼深色主题</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSwitch(this)" data-setting="darkmode"></div>
                </div>

                <button onclick="clearCache()" class="setting-item rounded-lg p-4 flex items-center justify-between w-full text-left">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="trash-2" class="w-5 h-5 text-orange-400"></i>
                        <div>
                            <h3 class="font-medium text-white">清除缓存</h3>
                            <p class="text-slate-400 text-sm">清理应用缓存数据</p>
                        </div>
                    </div>
                    <span class="text-slate-400 text-sm">23.5MB</span>
                </button>

                <button onclick="checkUpdate()" class="setting-item rounded-lg p-4 flex items-center justify-between w-full text-left">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="download" class="w-5 h-5 text-green-400"></i>
                        <div>
                            <h3 class="font-medium text-white">检查更新</h3>
                            <p class="text-slate-400 text-sm">检查应用更新</p>
                        </div>
                    </div>
                    <i data-lucide="chevron-right" class="w-5 h-5 text-slate-400"></i>
                </button>
            </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info rounded-lg p-4 text-center">
            <div class="flex items-center justify-center space-x-2 mb-2">
                <i data-lucide="smartphone" class="w-5 h-5 text-slate-400"></i>
                <span class="text-slate-300 font-medium">本地助手</span>
            </div>
            <p class="text-slate-400 text-sm">版本 1.2.0 (Build 20240220)</p>
            <p class="text-slate-500 text-xs mt-1">© 2024 本地助手团队</p>
        </div>
    </div>

    <!-- 退出登录确认弹窗 -->
    <div id="logout-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-sm">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="log-out" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">确认退出登录</h3>
                    <p class="text-slate-400 text-sm">退出后需要重新登录才能使用完整功能</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="closeLogoutModal()" class="flex-1 bg-slate-700 hover:bg-slate-600 text-white py-3 rounded-lg font-medium transition-colors">
                        取消
                    </button>
                    <button onclick="logout()" class="flex-1 bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-medium transition-colors">
                        退出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 设置状态存储
        const settings = {
            message: true,
            announcement: true,
            coupon: true,
            review: false,
            location: true,
            phone: false,
            online: true,
            darkmode: true
        };

        // 切换开关
        function toggleSwitch(element) {
            const setting = element.getAttribute('data-setting');
            const isActive = element.classList.contains('active');
            
            if (isActive) {
                element.classList.remove('active');
                settings[setting] = false;
            } else {
                element.classList.add('active');
                settings[setting] = true;
            }
            
            console.log(`设置 ${setting} 已${settings[setting] ? '开启' : '关闭'}`);
            
            // 特殊处理某些设置
            if (setting === 'location' && !settings[setting]) {
                alert('关闭位置信息可能影响附近商家和服务的推荐');
            }
            
            if (setting === 'darkmode') {
                // 这里可以实际切换主题
                console.log('切换主题模式');
            }
        }

        // 显示隐私政策
        function showPrivacyPolicy() {
            // 跳转到隐私政策页面
            window.location.href = 'privacy-policy.html';
        }

        // 更换手机号
        function changePhone() {
            alert('跳转到手机号更换页面');
        }

        // 显示账号信息
        function showAccountInfo() {
            // 跳转到实名认证页面
            window.location.href = 'verification.html';
        }

        // 确认退出登录
        function confirmLogout() {
            document.getElementById('logout-modal').classList.remove('hidden');
        }

        // 关闭退出登录弹窗
        function closeLogoutModal() {
            document.getElementById('logout-modal').classList.add('hidden');
        }

        // 退出登录
        function logout() {
            console.log('用户退出登录');
            // 清除本地存储的用户信息
            localStorage.clear();
            // 跳转到登录页面
            window.location.href = 'login.html';
        }

        // 清除缓存
        function clearCache() {
            if (confirm('确定要清除缓存吗？这将删除所有本地数据。')) {
                console.log('清除缓存');
                // 清除缓存逻辑
                alert('缓存清除成功');
            }
        }

        // 检查更新
        function checkUpdate() {
            console.log('检查更新');
            alert('当前已是最新版本');
        }

        // 发布设置相关函数
        function setDefaultPrice() {
            const price = prompt('请输入默认价格（元）：', '10');
            if (price && !isNaN(price)) {
                console.log('设置默认价格：', price);
                alert(`默认价格已设置为 ${price} 元`);
            }
        }

        function setDefaultDuration() {
            const duration = prompt('请输入默认有效期（天）：', '7');
            if (duration && !isNaN(duration)) {
                console.log('设置默认有效期：', duration);
                alert(`默认有效期已设置为 ${duration} 天`);
            }
        }

        function setTopSettings() {
            alert('跳转到置顶设置页面');
        }

        // 服务设置相关函数
        function setReviewReceiver() {
            const options = ['仅自己接收', '指定人员接收', '所有管理员接收'];
            const choice = prompt(`请选择评价接收人：\n1. ${options[0]}\n2. ${options[1]}\n3. ${options[2]}\n\n请输入数字 1-3：`);
            if (choice >= 1 && choice <= 3) {
                console.log('设置评价接收人：', options[choice-1]);
                alert(`评价接收人已设置为：${options[choice-1]}`);
            }
        }

        function setAutoReply() {
            const reply = prompt('请输入自动回复内容：', '感谢您的评价，我们会持续改进服务质量！');
            if (reply) {
                console.log('设置自动回复：', reply);
                alert('自动回复内容已保存');
            }
        }

        function manageCallService() {
            // 跳转到呼叫服务管理页面
            window.location.href = 'my-call-service.html';
        }

        // 返回
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.getElementById('logout-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLogoutModal();
            }
        });
    </script>
</body>
</html>