/**
 * 日志管理系统
 * 统一管理系统日志，根据环境自动调整日志级别，并支持日志收集
 */

class Logger {
    constructor() {
        // 日志级别定义
        this.LEVELS = {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3,
            NONE: 4
        };
        
        // 当前日志级别，默认根据环境设置
        this.currentLevel = this.getDefaultLevel();
        
        // 是否启用详细输出
        this.verbose = false;
        
        // 是否在生产环境报告错误
        this.reportErrors = APP_CONFIG && APP_CONFIG.ENVIRONMENT === 'production';
        
        // 错误报告URL
        this.errorReportUrl = APP_CONFIG && APP_CONFIG.ERROR ? APP_CONFIG.ERROR.REPORT_URL : null;
    }
    
    /**
     * 根据环境获取默认日志级别
     */
    getDefaultLevel() {
        if (APP_CONFIG && APP_CONFIG.ENVIRONMENT) {
            switch (APP_CONFIG.ENVIRONMENT) {
                case 'production':
                    return this.LEVELS.ERROR; // 生产环境只显示错误
                case 'staging':
                    return this.LEVELS.WARN; // 预发布环境显示警告及以上
                case 'development':
                    return this.LEVELS.DEBUG; // 开发环境显示所有日志
                default:
                    return this.LEVELS.INFO; // 默认显示信息及以上
            }
        }
        
        // 如果没有配置，根据URL判断
        const isProduction = !window.location.hostname.includes('localhost') && 
                           !window.location.hostname.includes('127.0.0.1') &&
                           !window.location.hostname.includes('test');
                           
        return isProduction ? this.LEVELS.ERROR : this.LEVELS.DEBUG;
    }
    
    /**
     * 初始化日志系统
     */
    init(options = {}) {
        if (options.level !== undefined) {
            this.currentLevel = options.level;
        }
        
        if (options.verbose !== undefined) {
            this.verbose = options.verbose;
        }
        
        if (options.reportErrors !== undefined) {
            this.reportErrors = options.reportErrors;
        }
        
        if (options.errorReportUrl) {
            this.errorReportUrl = options.errorReportUrl;
        }
        
        // 捕获全局错误
        this.setupErrorHandling();
        
        this.info('日志系统初始化完成', { 
            level: this.getLevelName(this.currentLevel),
            verbose: this.verbose,
            reportErrors: this.reportErrors
        });
    }
    
    /**
     * 获取日志级别名称
     */
    getLevelName(level) {
        for (const [name, value] of Object.entries(this.LEVELS)) {
            if (value === level) return name;
        }
        return 'UNKNOWN';
    }
    
    /**
     * 设置日志级别
     */
    setLevel(level) {
        this.currentLevel = level;
        this.debug(`日志级别已设置为: ${this.getLevelName(level)}`);
    }
    
    /**
     * 格式化日志信息
     */
    formatLog(message, data) {
        if (!this.verbose || !data) return message;
        
        try {
            if (typeof data === 'object') {
                return `${message} ${JSON.stringify(data)}`;
            }
            return `${message} ${data}`;
        } catch (e) {
            return `${message} [无法序列化的数据]`;
        }
    }
    
    /**
     * 调试级别日志
     */
    debug(message, data) {
        if (this.currentLevel <= this.LEVELS.DEBUG) {
            console.debug(this.formatLog(message, data));
        }
    }
    
    /**
     * 信息级别日志
     */
    info(message, data) {
        if (this.currentLevel <= this.LEVELS.INFO) {
            console.info(this.formatLog(message, data));
        }
    }
    
    /**
     * 警告级别日志
     */
    warn(message, data) {
        if (this.currentLevel <= this.LEVELS.WARN) {
            console.warn(this.formatLog(message, data));
        }
    }
    
    /**
     * 错误级别日志
     */
    error(message, error, data) {
        if (this.currentLevel <= this.LEVELS.ERROR) {
            console.error(this.formatLog(message, data), error);
        }
        
        // 报告错误到服务器
        if (this.reportErrors && this.errorReportUrl) {
            this.reportError(message, error, data);
        }
    }
    
    /**
     * 将错误报告到服务器
     */
    reportError(message, error, data = {}) {
        try {
            const errorData = {
                message,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                data
            };
            
            // 处理Error对象
            if (error instanceof Error) {
                errorData.error = {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                };
            } else if (error) {
                errorData.error = error;
            }
            
            // 发送到服务器
            if (navigator.sendBeacon && this.errorReportUrl) {
                navigator.sendBeacon(
                    this.errorReportUrl, 
                    JSON.stringify(errorData)
                );
            } else {
                // 降级为异步请求
                fetch(this.errorReportUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(errorData),
                    keepalive: true
                }).catch(() => {
                    // 静默失败
                });
            }
        } catch (e) {
            // 确保错误报告不会导致更多问题
            console.error('错误报告失败', e);
        }
    }
    
    /**
     * 设置全局错误处理
     */
    setupErrorHandling() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', event => {
            this.error('未处理的Promise错误', event.reason, {
                promise: event.promise
            });
        });
        
        // 捕获全局错误
        window.addEventListener('error', event => {
            this.error('全局JS错误', event.error, {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });
    }
    
    /**
     * 打印分组日志开始
     */
    groupStart(label) {
        if (this.currentLevel <= this.LEVELS.DEBUG) {
            console.group(label);
        }
    }
    
    /**
     * 打印分组日志结束
     */
    groupEnd() {
        if (this.currentLevel <= this.LEVELS.DEBUG) {
            console.groupEnd();
        }
    }
    
    /**
     * 打印执行时间
     */
    time(label) {
        if (this.currentLevel <= this.LEVELS.DEBUG) {
            console.time(label);
        }
    }
    
    /**
     * 结束时间测量并打印结果
     */
    timeEnd(label) {
        if (this.currentLevel <= this.LEVELS.DEBUG) {
            console.timeEnd(label);
        }
    }
}

// 创建全局单例
const logger = new Logger();

// 向后兼容API
const log = {
    debug: (...args) => logger.debug(...args),
    info: (...args) => logger.info(...args),
    warn: (...args) => logger.warn(...args),
    error: (...args) => logger.error(...args)
}; 