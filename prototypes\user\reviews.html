<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的评价 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            min-height: 100vh;
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .review-card {
            background: linear-gradient(135deg, #1e1e3f 0%, #252547 100%);
            border: 1px solid #2a2d4a;
            transition: all 0.3s ease;
        }
        .review-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
        .star-rating {
            display: flex;
            gap: 2px;
        }
        .star {
            width: 16px;
            height: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .star.filled {
            color: #fbbf24;
        }
        .star.empty {
            color: #6b7280;
        }
        .star:hover {
            color: #fbbf24;
        }
        .tab-active {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }
        .tab-inactive {
            background: rgba(255, 255, 255, 0.1);
            color: #94a3b8;
        }
        .image-preview {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .image-preview:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body class="text-slate-100">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-900/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <button onclick="goBack()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                <h1 class="text-lg font-semibold">我的评价</h1>
            </div>
            <div class="flex items-center space-x-2">
                <button onclick="showHelp()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 标签页导航 -->
    <div class="p-4">
        <div class="flex bg-slate-800/50 rounded-lg p-1">
            <button onclick="switchTab('my-reviews')" id="tab-my-reviews" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-active">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="star" class="w-4 h-4"></i>
                    <span>我的评价</span>
                    <span id="my-reviews-count" class="bg-white/20 px-2 py-0.5 rounded-full text-xs">5</span>
                </div>
            </button>
            <button onclick="switchTab('pending-reviews')" id="tab-pending-reviews" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-inactive">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="clock" class="w-4 h-4"></i>
                    <span>待评价</span>
                    <span id="pending-reviews-count" class="bg-white/20 px-2 py-0.5 rounded-full text-xs">2</span>
                </div>
            </button>
        </div>
    </div>

    <!-- 评价列表 -->
    <div class="px-4 pb-20">
        <!-- 我的评价 -->
        <div id="my-reviews" class="space-y-4">
            <!-- 商家评价 -->
            <div class="review-card rounded-lg p-4">
                <div class="flex items-start space-x-3 mb-3">
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=60&h=60&fit=crop&crop=center" alt="川香小厨" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-white">川香小厨</h3>
                            <span class="text-xs text-slate-500">2024-02-15</span>
                        </div>
                        <div class="flex items-center space-x-2 mb-2">
                            <div class="star-rating">
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                            </div>
                            <span class="text-sm text-slate-400">5.0分</span>
                        </div>
                        <p class="text-slate-300 text-sm mb-3">菜品味道很棒，服务态度也很好，环境干净整洁。特别推荐他们家的麻婆豆腐和回锅肉，非常正宗的川菜味道！</p>
                        <div class="flex space-x-2 mb-3">
                            <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=60&h=60&fit=crop&crop=center" alt="菜品1" class="image-preview" onclick="previewImage(this.src)">
                            <img src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=60&h=60&fit=crop&crop=center" alt="菜品2" class="image-preview" onclick="previewImage(this.src)">
                            <img src="https://images.unsplash.com/photo-1574484284002-952d92456975?w=60&h=60&fit=crop&crop=center" alt="菜品3" class="image-preview" onclick="previewImage(this.src)">
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-slate-500">通过优惠券消费后评价</span>
                            <div class="flex space-x-2">
                                <button onclick="editReview('1')" class="text-blue-400 hover:text-blue-300 text-sm transition-colors">
                                    编辑
                                </button>
                                <button onclick="deleteReview('1')" class="text-red-400 hover:text-red-300 text-sm transition-colors">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 商家回复 -->
                <div class="bg-slate-700/30 rounded-lg p-3 ml-15">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-sm font-medium text-orange-400">商家回复</span>
                        <span class="text-xs text-slate-500">2024-02-16</span>
                    </div>
                    <p class="text-sm text-slate-300">感谢您的好评！我们会继续努力为顾客提供更好的服务和美味的川菜。欢迎您再次光临！</p>
                </div>
            </div>

            <!-- 服务评价 -->
            <div class="review-card rounded-xl p-4">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=60&h=60&fit=crop&crop=center" alt="快递代收" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-white">快递代收服务</h3>
                            <span class="text-xs text-slate-500">2024-02-10</span>
                        </div>
                        <div class="flex items-center space-x-2 mb-2">
                            <div class="star-rating">
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star filled"></i>
                                <i data-lucide="star" class="star empty"></i>
                            </div>
                            <span class="text-sm text-slate-400">4.0分</span>
                        </div>
                        <p class="text-slate-300 text-sm mb-3">服务很及时，师傅人很好，帮忙代收快递很方便。就是价格稍微有点贵，不过总体还是满意的。</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-slate-500">跑腿服务评价</span>
                            <div class="flex space-x-2">
                                <button onclick="editReview('2')" class="text-blue-400 hover:text-blue-300 text-sm transition-colors">
                                    编辑
                                </button>
                                <button onclick="deleteReview('2')" class="text-red-400 hover:text-red-300 text-sm transition-colors">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待评价 -->
        <div id="pending-reviews" class="space-y-4 hidden">
            <!-- 待评价商家 -->
            <div class="review-card rounded-xl p-4">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1521017432531-fbd92d768814?w=60&h=60&fit=crop&crop=center" alt="时尚理发店" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">时尚理发店</h3>
                            <span class="bg-orange-500 text-white px-2 py-1 rounded text-xs">待评价</span>
                        </div>
                        <p class="text-slate-400 text-sm mb-3">您于2024-02-20使用了8.5折优惠券，快来评价一下服务体验吧！</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-slate-500">优惠券已核销</span>
                            <button onclick="showReviewModal('时尚理发店', 'shop')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                立即评价
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 待评价服务 -->
            <div class="review-card rounded-xl p-4">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=60&h=60&fit=crop&crop=center" alt="搬家服务" class="w-12 h-12 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">搬家服务</h3>
                            <span class="bg-orange-500 text-white px-2 py-1 rounded text-xs">待评价</span>
                        </div>
                        <p class="text-slate-400 text-sm mb-3">您联系的搬家服务已完成，快来评价一下服务质量吧！</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-slate-500">服务已完成</span>
                            <button onclick="showReviewModal('搬家服务', 'service')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                立即评价
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="text-center py-16 hidden">
            <div class="w-24 h-24 mx-auto mb-4 bg-slate-700 rounded-full flex items-center justify-center">
                <i data-lucide="star" class="w-12 h-12 text-slate-500"></i>
            </div>
            <h3 class="text-lg font-medium text-slate-300 mb-2">暂无评价记录</h3>
            <p class="text-slate-500 mb-6">使用优惠券消费后可以进行评价</p>
            <button onclick="goToShops()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                去逛逛商家
            </button>
        </div>
    </div>

    <!-- 评价弹窗 -->
    <div id="review-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">评价服务</h3>
                    <button onclick="closeReviewModal()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <!-- 商家信息 -->
                    <div class="flex items-center space-x-3 p-3 bg-slate-700/30 rounded-lg">
                        <div class="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="store" class="w-6 h-6 text-slate-400"></i>
                        </div>
                        <div>
                            <h4 id="review-target-name" class="font-medium text-white">商家名称</h4>
                            <p class="text-sm text-slate-400">请为本次服务体验打分</p>
                        </div>
                    </div>

                    <!-- 评分 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">服务评分</label>
                        <div class="flex items-center space-x-2">
                            <div class="star-rating" id="rating-stars">
                                <i data-lucide="star" class="star empty" data-rating="1" onclick="setRating(1)"></i>
                                <i data-lucide="star" class="star empty" data-rating="2" onclick="setRating(2)"></i>
                                <i data-lucide="star" class="star empty" data-rating="3" onclick="setRating(3)"></i>
                                <i data-lucide="star" class="star empty" data-rating="4" onclick="setRating(4)"></i>
                                <i data-lucide="star" class="star empty" data-rating="5" onclick="setRating(5)"></i>
                            </div>
                            <span id="rating-text" class="text-sm text-slate-400">请选择评分</span>
                        </div>
                    </div>

                    <!-- 评价内容 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">评价内容</label>
                        <textarea id="review-content" placeholder="分享您的服务体验..." class="w-full h-24 bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"></textarea>
                    </div>

                    <!-- 图片上传 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">上传图片（可选）</label>
                        <div class="flex space-x-2">
                            <div class="w-16 h-16 bg-slate-700 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center cursor-pointer hover:border-blue-500 transition-colors" onclick="uploadImage()">
                                <i data-lucide="plus" class="w-6 h-6 text-slate-400"></i>
                            </div>
                        </div>
                        <p class="text-xs text-slate-500 mt-1">最多上传3张图片</p>
                    </div>

                    <!-- 按钮 -->
                    <div class="flex space-x-3 pt-4">
                        <button onclick="closeReviewModal()" class="flex-1 bg-slate-600 hover:bg-slate-700 text-white py-3 rounded-lg font-medium transition-colors">
                            取消
                        </button>
                        <button onclick="submitReview()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                            提交评价
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览弹窗 -->
    <div id="image-preview-modal" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="relative">
                <img id="preview-image" src="" alt="预览图片" class="max-w-full max-h-[80vh] rounded-lg">
                <button onclick="closeImagePreview()" class="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-full transition-colors">
                    <i data-lucide="x" class="w-6 h-6 text-white"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">评价说明</h3>
                    <button onclick="closeHelpModal()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                <div class="space-y-4 text-sm text-slate-300">
                    <div>
                        <h4 class="font-medium text-white mb-2">什么时候可以评价？</h4>
                        <p>• 使用优惠券消费后可以评价商家</p>
                        <p>• 完成跑腿服务后可以评价服务者</p>
                        <p>• 通过平台联系完成交易后可以评价</p>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-2">评价规则</h4>
                        <p>• 每次消费只能评价一次</p>
                        <p>• 评价后24小时内可以修改</p>
                        <p>• 恶意差评将被系统识别并处理</p>
                        <p>• 评价内容需真实客观</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        let currentTab = 'my-reviews';
        let currentRating = 0;
        let currentReviewTarget = '';
        let currentReviewType = '';

        // 标签页切换
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签页样式
            document.querySelectorAll('[id^="tab-"]').forEach(btn => {
                btn.className = btn.className.replace('tab-active', 'tab-inactive');
            });
            document.getElementById(`tab-${tab}`).className = 
                document.getElementById(`tab-${tab}`).className.replace('tab-inactive', 'tab-active');
            
            // 显示对应内容
            document.getElementById('my-reviews').classList.add('hidden');
            document.getElementById('pending-reviews').classList.add('hidden');
            document.getElementById('empty-state').classList.add('hidden');
            
            const targetElement = document.getElementById(tab);
            if (targetElement) {
                targetElement.classList.remove('hidden');
            }
        }

        // 显示评价弹窗
        function showReviewModal(targetName, type) {
            currentReviewTarget = targetName;
            currentReviewType = type;
            document.getElementById('review-target-name').textContent = targetName;
            document.getElementById('review-modal').classList.remove('hidden');
            resetReviewForm();
        }

        // 关闭评价弹窗
        function closeReviewModal() {
            document.getElementById('review-modal').classList.add('hidden');
            resetReviewForm();
        }

        // 重置评价表单
        function resetReviewForm() {
            currentRating = 0;
            document.getElementById('review-content').value = '';
            document.getElementById('rating-text').textContent = '请选择评分';
            
            // 重置星星
            document.querySelectorAll('#rating-stars .star').forEach(star => {
                star.classList.remove('filled');
                star.classList.add('empty');
            });
        }

        // 设置评分
        function setRating(rating) {
            currentRating = rating;
            const ratingTexts = ['', '很差', '一般', '还行', '不错', '很好'];
            document.getElementById('rating-text').textContent = `${rating}.0分 - ${ratingTexts[rating]}`;
            
            // 更新星星显示
            document.querySelectorAll('#rating-stars .star').forEach((star, index) => {
                if (index < rating) {
                    star.classList.remove('empty');
                    star.classList.add('filled');
                } else {
                    star.classList.remove('filled');
                    star.classList.add('empty');
                }
            });
        }

        // 提交评价
        function submitReview() {
            if (currentRating === 0) {
                alert('请选择评分');
                return;
            }
            
            const content = document.getElementById('review-content').value.trim();
            if (!content) {
                alert('请填写评价内容');
                return;
            }
            
            // 这里可以发送评价数据到服务器
            console.log('提交评价:', {
                target: currentReviewTarget,
                type: currentReviewType,
                rating: currentRating,
                content: content
            });
            
            // 模拟提交成功
            alert('评价提交成功！');
            closeReviewModal();
            
            // 刷新页面数据
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // 编辑评价
        function editReview(reviewId) {
            console.log('编辑评价:', reviewId);
            // 这里可以打开编辑弹窗
        }

        // 删除评价
        function deleteReview(reviewId) {
            if (confirm('确定要删除这条评价吗？')) {
                console.log('删除评价:', reviewId);
                // 这里可以发送删除请求
            }
        }

        // 上传图片
        function uploadImage() {
            console.log('上传图片');
            // 这里可以打开图片选择器
        }

        // 预览图片
        function previewImage(src) {
            document.getElementById('preview-image').src = src;
            document.getElementById('image-preview-modal').classList.remove('hidden');
        }

        // 关闭图片预览
        function closeImagePreview() {
            document.getElementById('image-preview-modal').classList.add('hidden');
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('help-modal').classList.remove('hidden');
        }

        // 关闭帮助弹窗
        function closeHelpModal() {
            document.getElementById('help-modal').classList.add('hidden');
        }

        // 去商家页面
        function goToShops() {
            window.location.href = 'shops.html';
        }

        // 返回
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.getElementById('review-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeReviewModal();
            }
        });

        document.getElementById('image-preview-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImagePreview();
            }
        });

        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHelpModal();
            }
        });
    </script>
</body>
</html>