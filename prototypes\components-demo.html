<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件注册系统演示</title>
    
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 引入Lucide图标 -->
    <script src="https://unpkg.com/lucide@latest"></script>
    
    <style>
        /* 基础样式 */
        body {
            font-family: system-ui, -apple-system, "Segoe UI", Roboto, sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
        }
        
        .modal-dialog {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transform: translateY(-20px);
            opacity: 0;
        }
        
        .modal-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 1rem;
            overflow-y: auto;
            flex: 1;
        }
        
        .modal-footer {
            padding: 1rem;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: flex-end;
        }
        
        .modal-close {
            border: none;
            background: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.25rem;
            line-height: 1;
            color: #777;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 40;
            opacity: 0;
        }
        
        /* 轮播图样式 */
        .carousel {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .carousel-slide {
            display: none;
            text-align: center;
        }
        
        .carousel-slide.active {
            display: block;
        }
        
        .carousel-prev, .carousel-next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(255, 255, 255, 0.7);
            color: #333;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .carousel-prev {
            left: 10px;
        }
        
        .carousel-next {
            right: 10px;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }
        
        .carousel-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            border: none;
            cursor: pointer;
        }
        
        .carousel-dot.active {
            background-color: white;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 0.25rem;
        }
        
        .form-control.error {
            border-color: #f56565;
        }
        
        .form-control.success {
            border-color: #48bb78;
        }
        
        .field-error {
            color: #f56565;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <header class="mb-8 text-center">
            <h1 class="text-3xl font-bold mb-2">组件注册系统演示</h1>
            <p class="text-lg text-gray-600">展示基于组件注册系统的组件化开发</p>
        </header>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 模态框演示卡片 -->
            <div class="card">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <i data-lucide="layout-panel-left" class="w-5 h-5 mr-2"></i>
                    模态框组件
                </h2>
                <p class="text-gray-600 mb-4">
                    展示了可重用的模态框组件，支持动画、拖拽、无障碍访问等功能。
                </p>
                <div class="flex flex-wrap gap-2">
                    <button id="openBasicModal" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        基础模态框
                    </button>
                    <button id="openFormModal" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        表单模态框
                    </button>
                    <button id="openDraggableModal" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                        可拖拽模态框
                    </button>
                </div>
            </div>
            
            <!-- 轮播图演示卡片 -->
            <div class="card">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <i data-lucide="images" class="w-5 h-5 mr-2"></i>
                    轮播图组件
                </h2>
                <p class="text-gray-600 mb-4">
                    自动轮播的图片展示组件，支持自定义间隔时间、导航按钮和指示器。
                </p>
                
                <!-- 轮播图组件 -->
                <div data-component="carousel" data-config-interval="3000">
                    <div class="carousel h-48 bg-gray-200">
                        <div class="carousel-slide active flex items-center justify-center h-full bg-blue-100">
                            <span class="text-xl font-bold">幻灯片 1</span>
                        </div>
                        <div class="carousel-slide flex items-center justify-center h-full bg-green-100">
                            <span class="text-xl font-bold">幻灯片 2</span>
                        </div>
                        <div class="carousel-slide flex items-center justify-center h-full bg-yellow-100">
                            <span class="text-xl font-bold">幻灯片 3</span>
                        </div>
                        <button class="carousel-prev">
                            <i data-lucide="chevron-left" class="w-5 h-5"></i>
                        </button>
                        <button class="carousel-next">
                            <i data-lucide="chevron-right" class="w-5 h-5"></i>
                        </button>
                        <div class="carousel-indicators"></div>
                    </div>
                </div>
            </div>
            
            <!-- 表单验证演示卡片 -->
            <div class="card">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <i data-lucide="check-square" class="w-5 h-5 mr-2"></i>
                    表单验证组件
                </h2>
                <p class="text-gray-600 mb-4">
                    实时表单验证组件，支持多种验证规则和自定义错误消息。
                </p>
                
                <!-- 表单验证组件 -->
                <form data-component="formValidator">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" id="username" name="username" class="form-control" 
                               data-validate="required|min:3" placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" id="email" name="email" class="form-control" 
                               data-validate="required|email" placeholder="请输入邮箱">
                    </div>
                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" id="password" name="password" class="form-control" 
                               data-validate="required|min:6" placeholder="请输入密码">
                    </div>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        提交
                    </button>
                </form>
            </div>
            
            <!-- 组件系统说明 -->
            <div class="card">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                    关于组件注册系统
                </h2>
                <p class="text-gray-600 mb-4">
                    组件注册系统是一个统一的组件管理框架，用于规范化组件的注册、初始化和生命周期管理。
                </p>
                <ul class="list-disc pl-5 space-y-2">
                    <li>通过 data-component 属性标记组件</li>
                    <li>使用 data-config-* 属性配置组件选项</li>
                    <li>支持组件生命周期钩子</li>
                    <li>实现动态组件创建和销毁</li>
                    <li>提供统一的组件访问接口</li>
                </ul>
                <div class="mt-4">
                    <a href="组件注册系统使用指南.md" class="text-blue-500 hover:underline flex items-center">
                        <i data-lucide="file-text" class="w-4 h-4 mr-1"></i>
                        查看完整使用指南
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 基础模态框 -->
        <div id="basicModal" class="modal" data-component="modal">
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3 class="text-lg font-bold">基础模态框</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>这是一个基础模态框示例。它展示了模态框组件的基本功能。</p>
                    <p class="mt-4">您可以点击关闭按钮、点击遮罩层或按ESC键关闭模态框。</p>
                </div>
                <div class="modal-footer">
                    <button id="closeBasicModal" class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 mr-2">
                        关闭
                    </button>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        确定
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 表单模态框 -->
        <div id="formModal" class="modal" data-component="modal">
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3 class="text-lg font-bold">表单模态框</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="modalForm">
                        <div class="form-group">
                            <label for="modalName" class="form-label">姓名</label>
                            <input type="text" id="modalName" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="modalMessage" class="form-label">留言</label>
                            <textarea id="modalMessage" name="message" class="form-control" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button id="closeFormModal" class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 mr-2">
                        取消
                    </button>
                    <button id="submitFormModal" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        提交
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 可拖拽模态框 -->
        <div id="draggableModal" class="modal" data-component="modal" data-config-draggable="true">
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3 class="text-lg font-bold">可拖拽模态框</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>这个模态框可以拖动！点击标题栏并拖动来移动模态框。</p>
                    <p class="mt-4">这对于需要同时查看多个模态框的场景非常有用。</p>
                </div>
                <div class="modal-footer">
                    <button id="closeDraggableModal" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入基础脚本 -->
    <script src="scripts/config.js"></script>
    <script src="scripts/logger.js"></script>
    <script src="scripts/common.js"></script>
    
    <!-- 引入组件注册系统 -->
    <script src="scripts/component-registry.js"></script>
    
    <!-- 引入组件 -->
    <script src="scripts/components/modal.js"></script>
    
    <!-- 轮播图组件 -->
    <script>
        // 轮播图组件类
        class Carousel {
            constructor(element, options) {
                this.element = element;
                this.options = {
                    autoPlay: true,
                    interval: 5000,
                    ...options
                };
                
                this.carousel = element.querySelector('.carousel');
                this.slides = element.querySelectorAll('.carousel-slide');
                this.prevButton = element.querySelector('.carousel-prev');
                this.nextButton = element.querySelector('.carousel-next');
                this.indicators = element.querySelector('.carousel-indicators');
                
                this.currentIndex = 0;
                this.interval = null;
                
                this.init();
            }
            
            init() {
                // 创建指示器
                this.createDots();
                
                // 绑定事件
                this.bindEvents();
                
                // 如果设置了自动播放，启动定时器
                if (this.options.autoPlay) {
                    this.startAutoPlay();
                }
            }
            
            createDots() {
                // 创建指示器
                if (this.indicators) {
                    this.indicators.innerHTML = '';
                    
                    for (let i = 0; i < this.slides.length; i++) {
                        const dot = document.createElement('button');
                        dot.className = `carousel-dot ${i === this.currentIndex ? 'active' : ''}`;
                        dot.setAttribute('aria-label', `幻灯片 ${i + 1}`);
                        dot.addEventListener('click', () => this.goToSlide(i));
                        this.indicators.appendChild(dot);
                    }
                }
            }
            
            bindEvents() {
                // 上一张/下一张按钮
                if (this.prevButton) {
                    this.prevButton.addEventListener('click', this.prev.bind(this));
                }
                
                if (this.nextButton) {
                    this.nextButton.addEventListener('click', this.next.bind(this));
                }
                
                // 鼠标悬停暂停自动播放
                this.element.addEventListener('mouseenter', this.stopAutoPlay.bind(this));
                this.element.addEventListener('mouseleave', this.startAutoPlay.bind(this));
            }
            
            goToSlide(index) {
                // 更新当前索引
                this.slides[this.currentIndex].classList.remove('active');
                this.currentIndex = (index + this.slides.length) % this.slides.length;
                this.slides[this.currentIndex].classList.add('active');
                
                // 更新指示器
                if (this.indicators) {
                    const dots = this.indicators.querySelectorAll('.carousel-dot');
                    dots.forEach((dot, i) => {
                        dot.classList.toggle('active', i === this.currentIndex);
                    });
                }
            }
            
            next() {
                this.goToSlide(this.currentIndex + 1);
            }
            
            prev() {
                this.goToSlide(this.currentIndex - 1);
            }
            
            startAutoPlay() {
                if (this.options.autoPlay && !this.interval) {
                    this.interval = setInterval(() => {
                        this.next();
                    }, this.options.interval);
                }
            }
            
            stopAutoPlay() {
                if (this.interval) {
                    clearInterval(this.interval);
                    this.interval = null;
                }
            }
            
            destroy() {
                // 清理定时器
                this.stopAutoPlay();
                
                // 移除事件监听
                if (this.prevButton) {
                    this.prevButton.removeEventListener('click', this.prev);
                }
                
                if (this.nextButton) {
                    this.nextButton.removeEventListener('click', this.next);
                }
                
                this.element.removeEventListener('mouseenter', this.stopAutoPlay);
                this.element.removeEventListener('mouseleave', this.startAutoPlay);
                
                // 清理指示器事件
                if (this.indicators) {
                    const dots = this.indicators.querySelectorAll('.carousel-dot');
                    dots.forEach((dot, i) => {
                        dot.removeEventListener('click', () => this.goToSlide(i));
                    });
                }
            }
        }
        
        // 表单验证组件
        class FormValidator {
            constructor(element, options) {
                this.form = element;
                this.options = {
                    validateOnBlur: true,
                    validateOnSubmit: true,
                    ...options
                };
                
                this.fields = element.querySelectorAll('[data-validate]');
                
                this.init();
            }
            
            init() {
                // 绑定表单事件
                if (this.options.validateOnSubmit) {
                    this.form.addEventListener('submit', this.handleSubmit.bind(this));
                }
                
                // 绑定字段验证事件
                if (this.options.validateOnBlur) {
                    this.form.addEventListener('blur', this.handleBlur.bind(this), true);
                    this.form.addEventListener('input', this.handleInput.bind(this), true);
                }
            }
            
            handleSubmit(event) {
                if (!this.validateAll()) {
                    event.preventDefault();
                } else {
                    alert('表单验证成功！');
                }
            }
            
            handleBlur(event) {
                if (event.target.hasAttribute('data-validate')) {
                    this.validateField(event.target);
                }
            }
            
            handleInput(event) {
                if (event.target.hasAttribute('data-validate') && 
                    event.target.classList.contains('error')) {
                    this.validateField(event.target);
                }
            }
            
            validateAll() {
                let valid = true;
                this.fields.forEach(field => {
                    if (!this.validateField(field)) {
                        valid = false;
                    }
                });
                return valid;
            }
            
            validateField(field) {
                const value = field.value.trim();
                const rules = field.dataset.validate.split('|');
                
                // 清除错误状态
                this.clearFieldError(field);
                
                // 检查验证规则
                for (const rule of rules) {
                    let valid = true;
                    let errorMessage = '';
                    
                    if (rule === 'required') {
                        valid = value !== '';
                        errorMessage = '此字段为必填项';
                    } else if (rule === 'email') {
                        valid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                        errorMessage = '请输入有效的邮箱地址';
                    } else if (rule.startsWith('min:')) {
                        const min = parseInt(rule.split(':')[1]);
                        valid = value.length >= min;
                        errorMessage = `最少需要 ${min} 个字符`;
                    }
                    
                    if (!valid) {
                        this.showFieldError(field, errorMessage);
                        return false;
                    }
                }
                
                // 验证通过
                field.classList.add('success');
                return true;
            }
            
            showFieldError(field, message) {
                field.classList.add('error');
                field.classList.remove('success');
                
                let errorElement = field.parentNode.querySelector('.field-error');
                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'field-error';
                    field.parentNode.appendChild(errorElement);
                }
                
                errorElement.textContent = message;
            }
            
            clearFieldError(field) {
                field.classList.remove('error', 'success');
                
                const errorElement = field.parentNode.querySelector('.field-error');
                if (errorElement) {
                    errorElement.textContent = '';
                }
            }
            
            destroy() {
                // 移除事件监听
                this.form.removeEventListener('submit', this.handleSubmit);
                this.form.removeEventListener('blur', this.handleBlur, true);
                this.form.removeEventListener('input', this.handleInput, true);
            }
        }
        
        // 注册组件
        if (window.componentRegistry) {
            componentRegistry.register('carousel', Carousel);
            componentRegistry.register('formValidator', FormValidator);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图标
            lucide.createIcons();
            
            // 初始化模态框按钮
            const basicModal = document.getElementById('basicModal');
            const formModal = document.getElementById('formModal');
            const draggableModal = document.getElementById('draggableModal');
            
            document.getElementById('openBasicModal').addEventListener('click', function() {
                const modal = componentRegistry.getInstance(basicModal);
                if (modal) modal.open();
            });
            
            document.getElementById('openFormModal').addEventListener('click', function() {
                const modal = componentRegistry.getInstance(formModal);
                if (modal) modal.open();
            });
            
            document.getElementById('openDraggableModal').addEventListener('click', function() {
                const modal = componentRegistry.getInstance(draggableModal);
                if (modal) modal.open();
            });
            
            document.getElementById('closeBasicModal').addEventListener('click', function() {
                const modal = componentRegistry.getInstance(basicModal);
                if (modal) modal.close();
            });
            
            document.getElementById('closeFormModal').addEventListener('click', function() {
                const modal = componentRegistry.getInstance(formModal);
                if (modal) modal.close();
            });
            
            document.getElementById('submitFormModal').addEventListener('click', function() {
                alert('表单已提交！');
                const modal = componentRegistry.getInstance(formModal);
                if (modal) modal.close();
            });
            
            document.getElementById('closeDraggableModal').addEventListener('click', function() {
                const modal = componentRegistry.getInstance(draggableModal);
                if (modal) modal.close();
            });
        });
    </script>
</body>
</html>
