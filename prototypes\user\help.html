<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助中心 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
        }
        .help-card {
            background: rgba(30, 30, 63, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .help-card:hover {
            border-color: rgba(59, 130, 246, 0.6);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="text-white">
    <!-- 顶部导航 -->
    <div class="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <button id="backButton" class="p-2 hover:bg-white/20 rounded-lg transition-colors" data-event="click" data-handler="navigation.goBack">
                <i data-lucide="arrow-left" class="w-6 h-6"></i>
            </button>
            <h1 class="text-lg font-semibold">帮助中心</h1>
            <button id="contactServiceButton" class="p-2 hover:bg-white/20 rounded-lg transition-colors" data-event="click" data-handler="business.contactService">
                <i data-lucide="headphones" class="w-6 h-6"></i>
            </button>
        </div>
    </div>

    <!-- 搜索框 -->
    <div class="p-4">
        <div class="relative">
            <input type="text" placeholder="搜索帮助内容..."
                   class="w-full bg-slate-700/50 border border-slate-600 rounded-lg pl-10 pr-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
            <i data-lucide="search" class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
        </div>
    </div>

    <!-- 常见问题 -->
    <div class="p-4 space-y-4">
        <h2 class="text-xl font-bold mb-4">常见问题</h2>

        <!-- 使用指南 -->
        <div class="help-card rounded-lg p-4 transition-all duration-300" data-component="accordion" data-section-id="guide">
            <div class="flex items-center justify-between toggle-section">
                <div class="flex items-center space-x-3">
                    <i data-lucide="book-open" class="w-6 h-6 text-blue-400"></i>
                    <span class="font-medium">使用指南</span>
                </div>
                <i data-lucide="chevron-down" class="w-5 h-5 text-slate-400 transform transition-transform section-arrow" id="guide-arrow"></i>
            </div>
            <div class="section-content hidden mt-4 space-y-3 text-sm text-slate-300" id="guide-content">
                <div class="border-l-2 border-blue-400 pl-4">
                    <p class="font-medium text-white mb-2">如何发布信息？</p>
                    <p>点击首页的"+"按钮，选择要发布的信息类型，填写详细信息后提交审核即可。</p>
                </div>
                <div class="border-l-2 border-green-400 pl-4">
                    <p class="font-medium text-white mb-2">如何搜索服务？</p>
                    <p>在各个板块页面使用搜索功能，输入关键词或使用筛选条件快速找到需要的服务。</p>
                </div>
                <div class="border-l-2 border-purple-400 pl-4">
                    <p class="font-medium text-white mb-2">如何联系商家？</p>
                    <p>点击商家信息卡片，查看详情页面，可以通过电话、微信等方式直接联系。</p>
                </div>
            </div>
        </div>

        <!-- 账号相关 -->
        <div class="help-card rounded-lg p-4 transition-all duration-300" data-component="accordion" data-section-id="account">
            <div class="flex items-center justify-between toggle-section">
                <div class="flex items-center space-x-3">
                    <i data-lucide="user" class="w-6 h-6 text-green-400"></i>
                    <span class="font-medium">账号相关</span>
                </div>
                <i data-lucide="chevron-down" class="w-5 h-5 text-slate-400 transform transition-transform section-arrow" id="account-arrow"></i>
            </div>
            <div class="section-content hidden mt-4 space-y-3 text-sm text-slate-300" id="account-content">
                <div class="border-l-2 border-green-400 pl-4">
                    <p class="font-medium text-white mb-2">如何实名认证？</p>
                    <p>进入个人中心，点击实名认证，上传身份证照片并填写相关信息即可。</p>
                </div>
                <div class="border-l-2 border-yellow-400 pl-4">
                    <p class="font-medium text-white mb-2">忘记密码怎么办？</p>
                    <p>在登录页面点击"忘记密码"，通过手机验证码重置密码。</p>
                </div>
                <div class="border-l-2 border-red-400 pl-4">
                    <p class="font-medium text-white mb-2">如何注销账号？</p>
                    <p>在个人中心的设置页面找到账号注销选项，注意注销后数据无法恢复。</p>
                </div>
            </div>
        </div>

        <!-- 安全须知 -->
        <div class="help-card rounded-lg p-4 transition-all duration-300" data-component="accordion" data-section-id="safety">
            <div class="flex items-center justify-between toggle-section">
                <div class="flex items-center space-x-3">
                    <i data-lucide="shield" class="w-6 h-6 text-yellow-400"></i>
                    <span class="font-medium">安全须知</span>
                </div>
                <i data-lucide="chevron-down" class="w-5 h-5 text-slate-400 transform transition-transform section-arrow" id="safety-arrow"></i>
            </div>
            <div class="section-content hidden mt-4 space-y-3 text-sm text-slate-300" id="safety-content">
                <div class="border-l-2 border-yellow-400 pl-4">
                    <p class="font-medium text-white mb-2">交易安全提醒</p>
                    <p>平台仅提供信息展示，实际交易请线下进行，注意核实对方身份，避免上当受骗。</p>
                </div>
                <div class="border-l-2 border-red-400 pl-4">
                    <p class="font-medium text-white mb-2">个人信息保护</p>
                    <p>不要在平台上发布过于详细的个人信息，保护好自己的隐私安全。</p>
                </div>
            </div>
        </div>

        <!-- 联系我们 -->
        <div class="help-card rounded-lg p-4">
            <div class="flex items-center space-x-3 mb-4">
                <i data-lucide="phone" class="w-6 h-6 text-blue-400"></i>
                <span class="font-medium">联系我们</span>
            </div>
            <div class="space-y-3 text-sm">
                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <span>客服热线</span>
                    <span class="text-blue-400 font-medium">************</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <span>服务时间</span>
                    <span class="text-green-400">9:00 - 18:00</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <span>在线客服</span>
                    <button id="contactService" class="text-blue-400 hover:text-blue-300" data-event="click" data-handler="business.contactService">立即咨询</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入基础脚本 -->
    <script src="../scripts/config.js"></script>
    <script src="../scripts/logger.js"></script>
    <script src="../scripts/common.js"></script>
    
    <!-- 引入事件处理器 -->
    <script src="../scripts/event-handlers.js"></script>
    
    <!-- 引入组件注册系统 -->
    <script src="../scripts/component-registry.js"></script>
    
    <!-- 引入组件 -->
    <script src="../scripts/components/accordion.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图标
            lucide.createIcons();
        });
    </script>
</body>
</html>