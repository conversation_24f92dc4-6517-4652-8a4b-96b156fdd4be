/**
 * 页面模板系统
 * 用于消除HTML页面中重复的结构代码，如顶部导航栏、底部菜单、模态窗口等
 */

const Templates = {
    /**
     * 生成顶部状态栏
     * @param {Object} options - 配置项
     * @returns {string} HTML字符串
     */
    topStatusBar(options = {}) {
        const city = options.city || '深圳市';
        const time = options.time || new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit'});
        
        return \
        <div class=\
bg-slate-800
text-slate-300
text-xs
px-4
py-1
flex
justify-between
items-center\>
            <div class=\flex
items-center
space-x-2\>
                <div class=\w-2
h-2
bg-green-500
rounded-full\></div>
                <span>在线</span>
            </div>
            <div class=\flex
items-center
space-x-4\>
                <span>\</span>
                <span>\</span>
                <div class=\flex
items-center
space-x-1\>
                    <i data-lucide=\wifi\ class=\w-3
h-3\></i>
                    <i data-lucide=\battery\ class=\w-3
h-3\></i>
                </div>
            </div>
        </div>\;
    },
    
    /**
     * 生成顶部搜索栏
     * @param {Object} options - 配置项
     * @returns {string} HTML字符串
     */
    topSearchBar(options = {}) {
        const city = options.city || '深圳';
        const placeholder = options.placeholder || '搜索商铺服务...';
        
        return \
        <div class=\bg-slate-800
shadow-sm
sticky
top-0
z-50\>
            <div class=\max-w-md
mx-auto
px-4
py-3\>
                <div class=\flex
items-center
space-x-3\>
                    <button class=\flex
items-center
space-x-1
bg-slate-700
px-3
py-2
rounded-lg
text-sm
font-medium
text-slate-300
hover:bg-slate-600
transition-colors\ data-action=\selectCity\>
                        <i data-lucide=\map-pin\ class=\w-4
h-4\></i>
                        <span>\</span>
                        <i data-lucide=\chevron-down\ class=\w-3
h-3\></i>
                    </button>
                    
                    <div class=\flex-1
relative\>
                        <input type=\text\ placeholder=\\\ 
                               class=\w-full
bg-slate-700
border-0
rounded-lg
px-4
py-2
pl-10
pr-4
text-sm
text-slate-300
placeholder-slate-500
focus:ring-2
focus:ring-blue-500
focus:bg-slate-600
transition-all\>
                        <i data-lucide=\search\ class=\w-4
h-4
absolute
left-3
top-1/2
transform
-translate-y-1/2
text-slate-400\></i>
                    </div>
                    
                    <button class=\flex
items-center
justify-center
w-10
h-10
bg-slate-700
rounded-lg
hover:bg-slate-600
transition-colors\ data-action=\openQRScanner\ aria-label=\扫一扫\ title=\扫一扫\>
                        <i data-lucide=\qr-code\ class=\w-5
h-5
text-slate-300\ aria-hidden=\true\></i>
                    </button>
                </div>
            </div>
        </div>\;
    },
    
    /**
     * 生成四大板块导航
     * @param {string} activeModule - 当前激活的模块 (shops|information|transportation|errands)
     * @returns {string} HTML字符串
     */
    moduleNav(activeModule = 'shops') {
        return \
        <div class=\bg-slate-800
border-b
border-slate-700\>
            <div class=\max-w-md
mx-auto
px-4\>
                <div class=\module-nav
flex
items-center
justify-between
py-3\ role=\navigation\ aria-label=\功能模块\>
                    <button class=\nav-item
\
flex
flex-col
items-center
space-y-1
px-4
py-2
rounded-lg\ data-action=\switchModule\ data-params='{\module\:\shops\}' role=\button\ tabindex=\0\ aria-label=\商铺模块\>
                        <i data-lucide=\store\ class=\w-5
h-5\ aria-hidden=\true\></i>
                        <span class=\text-sm
font-medium\>商铺</span>
                    </button>
                    <button class=\nav-item
\
flex
flex-col
items-center
space-y-1
px-4
py-2
rounded-lg\ data-action=\switchModule\ data-params='{\module\:\information\}' role=\button\ tabindex=\0\ aria-label=\信息模块\>
                        <i data-lucide=\file-text\ class=\w-5
h-5\ aria-hidden=\true\></i>
                        <span class=\text-sm
font-medium\>信息</span>
                    </button>
                    <button class=\nav-item
\
flex
flex-col
items-center
space-y-1
px-4
py-2
rounded-lg\ data-action=\switchModule\ data-params='{\module\:\transportation\}' role=\button\ tabindex=\0\ aria-label=\出行模块\>
                        <i data-lucide=\car\ class=\w-5
h-5\ aria-hidden=\true\></i>
                        <span class=\text-sm
font-medium\>出行</span>
                    </button>
                    <button class=\nav-item
\
flex
flex-col
items-center
space-y-1
px-4
py-2
rounded-lg\ data-action=\switchModule\ data-params='{\module\:\errands\}' role=\button\ tabindex=\0\ aria-label=\跑腿模块\>
                        <i data-lucide=\gift\ class=\w-5
h-5\ aria-hidden=\true\></i>
                        <span class=\text-sm
font-medium\>跑腿</span>
                    </button>
                </div>
            </div>
        </div>\;
    }
};

// 自动初始化添加到DOM的图标
const observer = new MutationObserver((mutations) => {
    let hasNewNodes = false;
    mutations.forEach(mutation => {
        if (mutation.addedNodes.length > 0) {
            hasNewNodes = true;
        }
    });
    
    if (hasNewNodes && window.lucide) {
        lucide.createIcons();
    }
});

// 启动DOM观察
observer.observe(document.body, { 
    childList: true, 
    subtree: true 
});

/**
 * 创建发布选择模态窗
 * @returns {string} 模态窗HTML
 */
function createPublishModal() {
    return `
    <div id="publishModal" class="fixed inset-0 z-50 hidden">
        <!-- 遮罩层 -->
        <div class="modal-overlay absolute inset-0 bg-black bg-opacity-50" data-action="closePublishModal"></div>
        
        <!-- 弹窗内容 -->
        <div class="modal-content absolute bottom-0 left-0 right-0 bg-slate-800 rounded-t-3xl p-6">
            <div class="max-w-md mx-auto">
                <!-- 拖拽指示器 -->
                <div class="w-12 h-1 bg-slate-600 rounded-full mx-auto mb-6"></div>
                
                <!-- 标题和返回按钮 -->
                <div class="flex items-center justify-between mb-6">
                    <button data-action="back" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5 text-slate-400"></i>
                    </button>
                    <div class="text-center flex-1">
                        <h2 class="text-2xl font-bold text-white mb-2">发布中心</h2>
                        <p class="text-slate-400 text-sm">统一管理您的发布内容</p>
                    </div>
                    <button data-action="showDrafts" class="p-2 hover:bg-slate-700 rounded-lg transition-colors" title="草稿箱">
                        <i data-lucide="file-text" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                
                <!-- 快速操作区 -->
                <div class="flex space-x-3 mb-6">
                    <button data-action="showHistory" class="flex-1 bg-slate-700 hover:bg-slate-600 rounded-lg p-3 transition-colors">
                        <div class="flex items-center justify-center space-x-2">
                            <i data-lucide="clock" class="w-4 h-4 text-slate-300"></i>
                            <span class="text-sm text-slate-300">发布历史</span>
                        </div>
                    </button>
                    <button data-action="togglePublishMode" id="publishModeBtn" class="flex-1 bg-blue-600 hover:bg-blue-700 rounded-lg p-3 transition-colors">
                        <div class="flex items-center justify-center space-x-2">
                            <i data-lucide="zap" class="w-4 h-4 text-white"></i>
                            <span class="text-sm text-white">快速发布</span>
                        </div>
                    </button>
                </div>
                
                <!-- 四大板块选择 -->
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <!-- 商铺板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="selectShopSection">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="store" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">商铺</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">发布商家信息<br>展示产品服务</p>
                        </div>
                    </div>
                    
                    <!-- 信息板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="publishTo" data-params='{"type":"information"}'>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="info" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">信息</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">招聘求职<br>租房买卖</p>
                        </div>
                    </div>
                    
                    <!-- 出行板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="publishTo" data-params='{"type":"transportation"}'>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="car" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">出行</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">拼车代驾<br>货运服务</p>
                        </div>
                    </div>
                    
                    <!-- 跑腿板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="publishTo" data-params='{"type":"errands"}'>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">跑腿</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">代买代送<br>代办事务</p>
                        </div>
                    </div>
                </div>
                
                <!-- 取消按钮 -->
                <button data-action="closePublishModal" class="w-full py-4 text-slate-400 font-medium hover:text-white transition-colors">
                    取消
                </button>
                
                <!-- 底部安全距离 -->
                <div class="h-4"></div>
            </div>
        </div>
    </div>
    `;
}

/**
 * 创建通用底部导航栏
 * @param {string} activePage - 当前激活的页面("shops"|"message"|"publish"|"help"|"profile")
 * @returns {string} 底部导航栏HTML
 */
function createBottomNav(activePage = '') {
    return `
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button data-action="navigate:shops" class="flex flex-col items-center space-y-1 py-2 ${activePage === 'shops' ? 'text-blue-400' : 'text-slate-400 hover:text-slate-300 transition-colors'}">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs ${activePage === 'shops' ? 'font-medium' : ''}">商铺</span>
                </button>
                <button data-action="navigate:message" class="flex flex-col items-center space-y-1 py-2 ${activePage === 'message' ? 'text-blue-400' : 'text-slate-400 hover:text-slate-300 transition-colors'}">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs ${activePage === 'message' ? 'font-medium' : ''}">消息</span>
                </button>
                <button data-action="showPublishModal" class="flex flex-col items-center space-y-1 py-2 ${activePage === 'publish' ? 'text-blue-400' : 'text-slate-400 hover:text-slate-300 transition-colors'}">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs ${activePage === 'publish' ? 'font-medium' : ''}">发布</span>
                </button>
                <button data-action="navigate:help" class="flex flex-col items-center space-y-1 py-2 ${activePage === 'help' ? 'text-blue-400' : 'text-slate-400 hover:text-slate-300 transition-colors'}">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs ${activePage === 'help' ? 'font-medium' : ''}">帮助</span>
                </button>
                <button data-action="navigate:profile" class="flex flex-col items-center space-y-1 py-2 ${activePage === 'profile' ? 'text-blue-400' : 'text-slate-400 hover:text-slate-300 transition-colors'}">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs ${activePage === 'profile' ? 'font-medium' : ''}">我的</span>
                </button>
            </div>
        </div>
    </div>
    `;
}

/**
 * 创建确认对话框
 * @param {string} id - 对话框ID
 * @param {string} title - 对话框标题
 * @param {string} message - 对话框消息
 * @param {string} confirmText - 确认按钮文本
 * @param {string} cancelText - 取消按钮文本
 * @param {string} confirmAction - 确认按钮的data-action值
 * @param {string} cancelAction - 取消按钮的data-action值
 * @param {string} confirmParams - 确认按钮的data-params值(JSON字符串)
 * @param {string} cancelParams - 取消按钮的data-params值(JSON字符串)
 * @returns {string} 对话框HTML
 */
function createConfirmDialog({
    id = 'confirmDialog',
    title = '确认操作',
    message = '确定要执行此操作吗？',
    confirmText = '确认',
    cancelText = '取消',
    confirmAction = 'confirmDialog',
    cancelAction = 'closeDialog',
    confirmParams = '{}',
    cancelParams = '{}'
} = {}) {
    return `
    <div id="${id}" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay absolute inset-0 bg-black bg-opacity-50" data-action="closeDialog" data-params='${cancelParams}'></div>
        <div class="modal-dialog absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90%] max-w-sm bg-slate-800 rounded-xl p-5 shadow-lg">
            <h3 class="text-xl font-bold text-white mb-3">${title}</h3>
            <p class="text-slate-300 mb-6">${message}</p>
            <div class="flex space-x-3">
                <button data-action="${cancelAction}" data-params='${cancelParams}' class="flex-1 py-2 px-4 rounded-lg bg-slate-700 text-slate-300 hover:bg-slate-600 transition-colors">
                    ${cancelText}
                </button>
                <button data-action="${confirmAction}" data-params='${confirmParams}' class="flex-1 py-2 px-4 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                    ${confirmText}
                </button>
            </div>
        </div>
    </div>
    `;
}

/**
 * 创建加载中指示器
 * @param {string} message - 加载消息
 * @param {string} id - 加载指示器ID
 * @returns {string} 加载指示器HTML
 */
function createLoader(message = '加载中...', id = 'loader') {
    return `
    <div id="${id}" class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-slate-800 rounded-xl p-5 shadow-lg w-[70%] max-w-xs flex flex-col items-center">
            <div class="spinner w-12 h-12 border-4 border-slate-600 border-t-blue-500 rounded-full animate-spin mb-3"></div>
            <p class="text-slate-300 text-center">${message}</p>
        </div>
    </div>
    `;
}

/**
 * 创建通用卡片
 * @param {Object} props - 卡片属性
 * @returns {string} 卡片HTML
 */
function createCard({
    title = '',
    content = '',
    footer = '',
    classes = '',
    dataAction = '',
    dataParams = '{}'
} = {}) {
    const actionAttr = dataAction ? `data-action="${dataAction}"` : '';
    const paramsAttr = dataAction ? `data-params='${dataParams}'` : '';
    
    return `
    <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 ${classes}" ${actionAttr} ${paramsAttr}>
        ${title ? `<div class="card-title mb-3">${title}</div>` : ''}
        <div class="card-content">
            ${content}
        </div>
        ${footer ? `<div class="card-footer mt-3 pt-3 border-t border-slate-700">${footer}</div>` : ''}
    </div>
    `;
}

/**
 * 创建空状态组件
 * @param {string} icon - 图标名称
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {string} actionText - 操作按钮文本
 * @param {string} actionDataAction - 操作按钮的data-action属性
 * @param {string} actionDataParams - 操作按钮的data-params属性
 * @returns {string} 空状态HTML
 */
function createEmptyState({
    icon = 'info',
    title = '没有数据',
    message = '当前没有可显示的内容',
    actionText = '',
    actionDataAction = '',
    actionDataParams = '{}'
} = {}) {
    const actionButton = actionText ? `
        <button data-action="${actionDataAction}" data-params='${actionDataParams}' 
                class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            ${actionText}
        </button>
    ` : '';
    
    return `
    <div class="flex flex-col items-center justify-center py-10 px-4">
        <div class="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mb-4">
            <i data-lucide="${icon}" class="w-8 h-8 text-slate-400"></i>
        </div>
        <h3 class="text-lg font-medium text-white mb-2">${title}</h3>
        <p class="text-slate-400 text-center mb-4 max-w-xs">${message}</p>
        ${actionButton}
    </div>
    `;
}

/**
 * 创建安全提醒组件
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @param {string} id - 组件ID
 * @returns {string} 安全提醒HTML
 */
function createSafetyReminder({ 
    title = '安全提示', 
    content = '注意个人信息安全，不要轻易透露个人联系方式和财产信息。', 
    id = 'safetyReminder' 
} = {}) {
    return `
    <div id="${id}" class="bg-amber-900/30 border border-amber-700/50 rounded-lg p-4 mb-4">
        <div class="flex items-center justify-between mb-2 cursor-pointer" data-action="toggleSafetyContent">
            <div class="flex items-center space-x-2">
                <i data-lucide="shield-alert" class="w-5 h-5 text-amber-400"></i>
                <h3 class="text-amber-400 font-medium">${title}</h3>
            </div>
            <i data-lucide="chevron-down" class="w-4 h-4 text-amber-400" id="${id}-toggle"></i>
        </div>
        
        <div class="text-amber-300/90 text-sm mt-1" id="${id}-content">
            ${content}
            <div class="flex justify-end mt-2">
                <button data-action="closeSafetyReminder" class="text-amber-400 hover:text-amber-300">
                    不再提示
                </button>
            </div>
        </div>
    </div>
    `;
}

/**
 * 添加组件到DOM
 * @param {string} html - 组件HTML
 * @param {string|Element} target - 目标元素或选择器
 * @param {string} position - 插入位置('beforebegin'|'afterbegin'|'beforeend'|'afterend')
 * @returns {Element} 插入的元素
 */
function appendTemplate(html, target, position = 'beforeend') {
    const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
    if (!targetElement) {
        console.error(`目标元素 ${target} 不存在`);
        return null;
    }
    
    targetElement.insertAdjacentHTML(position, html);
    const fragment = document.createDocumentFragment();
    const div = document.createElement('div');
    div.innerHTML = html;
    
    while (div.firstChild) {
        fragment.appendChild(div.firstChild);
    }
    
    const addedNodes = [];
    for (let i = 0; i < fragment.childNodes.length; i++) {
        const node = fragment.childNodes[i];
        if (node.nodeType === 1) { // Element node
            addedNodes.push(node);
        }
    }
    
    // 如果只有一个元素节点，则返回它，否则返回数组
    return addedNodes.length === 1 ? addedNodes[0] : addedNodes;
}

// 导出所有模板函数
window.templates = {
    createPublishModal,
    createBottomNav,
    createConfirmDialog,
    createLoader,
    createCard,
    createEmptyState,
    createSafetyReminder,
    appendTemplate
};
