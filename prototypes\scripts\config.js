/**
 * 本地助手 - 全局配置文件
 * 统一管理系统各模块的配置参数
 */

const APP_CONFIG = {
    // 应用基本信息
    APP: {
        NAME: "本地助手",
        VERSION: "1.0.0",
        ENVIRONMENT: "development", // development, production
        DEBUG: true
    },
    
    // UI配置
    UI: {
        // 防抖延迟
        DEBOUNCE_DELAY: 300,
        // Toast显示时长
        TOAST_DURATION: 3000,
        // 验证码倒计时
        SMS_COUNTDOWN: 60,
        // 动画持续时间
        ANIMATION_DURATION: 300,
        // 最小点击区域（移动端）
        MIN_TOUCH_TARGET: 44,
        // 响应式断点
        BREAKPOINTS: {
            sm: 640,
            md: 768,
            lg: 1024,
            xl: 1280
        },
        // 无障碍配置
        A11Y: {
            FOCUS_VISIBLE_CLASS: 'focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
            SCREEN_READER_ONLY: 'sr-only'
        }
    },
    
    // API配置
    API: {
        BASE_URL: "https://api.example.com/v1",
        TIMEOUT: 10000,
        RETRY_COUNT: 3
    },
    
    // 地图配置
    MAP: {
        DEFAULT_CENTER: {lat: 22.5431, lng: 114.0579}, // 深圳市中心
        DEFAULT_ZOOM: 12,
        API_KEY: "your_api_key_here"
    },
    
    // 存储配置
    STORAGE: {
        USER_TOKEN_KEY: "user_token",
        USER_INFO_KEY: "user_info",
        THEME_KEY: "app_theme",
        SETTINGS_KEY: "app_settings",
        EXPIRES: 7 * 24 * 60 * 60 * 1000 // 7天
    },
    
    // 文件上传配置
    UPLOAD: {
        MAX_SIZE: 5 * 1024 * 1024, // 5MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        IMAGE_QUALITY: 0.8
    },
    
    // 错误处理配置
    ERROR: {
        LOG_LEVEL: 'warn', // debug, info, warn, error
        REPORT_URL: "https://log.example.com/errors"
    }
};

// 保证配置不被修改
Object.freeze(APP_CONFIG);
Object.freeze(APP_CONFIG.APP);
Object.freeze(APP_CONFIG.UI);
Object.freeze(APP_CONFIG.API);
Object.freeze(APP_CONFIG.MAP);
Object.freeze(APP_CONFIG.STORAGE);
Object.freeze(APP_CONFIG.UPLOAD);
Object.freeze(APP_CONFIG.ERROR);
Object.freeze(APP_CONFIG.UI.BREAKPOINTS);
Object.freeze(APP_CONFIG.UI.A11Y); 