/**
 * 事件处理器
 * 
 * 提供统一的事件处理机制，替代内联onclick事件
 */

// 事件处理器对象
const eventHandlers = {
    // 导航相关
    navigation: {
        // 返回上一页
        goBack: function() {
            window.history.back();
        },
        
        // 跳转到指定页面
        goToPage: function(url) {
            window.location.href = url;
        }
    },
    
    // 用户交互
    ui: {
        // 显示警告对话框
        showAlert: function(message) {
            window.alert(message);
        }
    },
    
    // 业务相关
    business: {
        // 联系客服
        contactService: function() {
            window.alert("客服联系方式\n\n• 客服热线：400-123-4567\n• 服务时间：9:00-18:00\n• 微信客服：添加客服微信号\n\n我们将竭诚为您服务！");
        },
        
        // 联系商家
        contactShop: function(shopId) {
            window.alert("正在连接商家 " + shopId + "，请稍候...");
        },
        
        // 显示商家指南
        showMerchantGuide: function() {
            window.alert("商家选择指南\n\n1. 查看商家评分和评价\n2. 确认商家资质和认证状态\n3. 比较多家商家的服务和价格\n4. 优先选择有实名认证的商家");
        },
        
        // 显示投诉指南
        showComplaintGuide: function() {
            window.alert("投诉指南\n\n如遇问题，可通过以下方式投诉：\n1. 在商家页面点击投诉按钮\n2. 联系客服电话：400-123-4567\n3. 发送邮件至：<EMAIL>");
        },
        
        // 显示服务免责声明
        showServiceDisclaimer: function() {
            window.alert("服务免责声明\n\n本平台仅提供信息展示和撮合服务，不参与实际交易过程，不对商家服务质量和用户行为负责。请用户自行判断信息真实性，谨慎交易。");
        },
        
        // 收藏/取消收藏商铺
        toggleFavorite: function(shopId) {
            // 获取收藏状态
            let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
            const isFavorited = favorites.includes(shopId);
            
            if (isFavorited) {
                // 取消收藏
                favorites = favorites.filter(id => id !== shopId);
                window.alert("已取消收藏");
            } else {
                // 添加收藏
                favorites.push(shopId);
                window.alert("已添加到收藏");
            }
            
            // 保存更新的收藏列表
            localStorage.setItem('favorites', JSON.stringify(favorites));
        },
        
        // 报告问题
        reportIssue: function(type, id) {
            const reasons = [
                '1. 虚假信息',
                '2. 服务质量问题', 
                '3. 价格欺诈',
                '4. 违法违规',
                '5. 其他问题'
            ];
            
            const reason = prompt('请选择举报原因：\n\n' + reasons.join('\n') + '\n\n请输入数字1-5：');
            
            if (reason && reason >= 1 && reason <= 5) {
                const detail = prompt('请详细描述问题（可选）：');
                window.alert('举报已提交\n\n我们会在24小时内处理您的举报\n感谢您的反馈');
            }
        },
        
        // 查看服务详情
        viewServiceDetail: function(serviceId) {
            window.location.href = 'product-detail.html?id=' + serviceId;
        },
        
        // 认领商铺
        claimShop: function(shopId) {
            const isLoggedIn = confirm('认领商铺需要登录账号\n\n您是否已登录？');
            
            if (isLoggedIn) {
                const confirmClaim = confirm(`确认认领此商铺？\n\n商铺ID：${shopId}\n\n认领后您将成为此商铺的管理员`);
                
                if (confirmClaim) {
                    const phone = prompt('请输入您的联系电话用于验证：');
                    if (phone && phone.length >= 11) {
                        window.alert('认领申请已提交\n\n我们会在1-3个工作日内联系您进行身份验证\n请保持电话畅通');
                    } else {
                        window.alert('请输入有效的联系电话');
                    }
                }
            } else {
                const shouldLogin = confirm('请先登录账号\n\n是否前往登录页面？');
                if (shouldLogin) {
                    window.location.href = 'login.html';
                }
            }
        }
    }
};

// 导出事件处理器
window.eventHandlers = eventHandlers;

// 页面加载完成后初始化事件绑定
document.addEventListener("DOMContentLoaded", function() {
    // 查找所有带有data-event属性的元素
    const eventElements = document.querySelectorAll("[data-event]");
    
    eventElements.forEach(element => {
        const eventName = element.dataset.event; // 事件名称，如：click
        const handlerPath = element.dataset.handler; // 处理器路径，如：navigation.goBack
        const handlerArgs = element.dataset.args; // 处理器参数，如：shopId=123
        
        if (eventName && handlerPath) {
            // 解析处理器路径
            const pathParts = handlerPath.split(".");
            if (pathParts.length !== 2) return;
            
            const category = pathParts[0];
            const method = pathParts[1];
            
            // 检查处理器是否存在
            if (eventHandlers[category] && typeof eventHandlers[category][method] === "function") {
                // 解析参数
                let args = [];
                if (handlerArgs) {
                    try {
                        // 尝试解析JSON参数
                        args = JSON.parse(handlerArgs);
                        if (!Array.isArray(args)) {
                            args = [args];
                        }
                    } catch (e) {
                        // 如果不是JSON，按逗号分隔
                        args = handlerArgs.split(",").map(arg => arg.trim());
                    }
                }
                
                // 绑定事件
                element.addEventListener(eventName, function(e) {
                    e.preventDefault();
                    eventHandlers[category][method].apply(null, args);
                });
            }
        }
    });
});
