<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no">
    <title>扫码呼叫服务 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=PingFang+SC:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-bg: #0f0f23;
            --secondary-bg: #1e1e3f;
            --card-bg: #252547;
            --accent-blue: #0066ff;
            --accent-purple: #6366f1;
            --accent-cyan: #06b6d4;
            --accent-green: #00ff88;
            --text-primary: #e2e8f0;
            --text-secondary: #94a3b8;
        }
        
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        body {
            font-family: 'Inter', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #16213e 50%, var(--secondary-bg) 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .tech-title {
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 102, 255, 0.3);
        }
        
        .service-card {
            background: linear-gradient(145deg, var(--card-bg), rgba(37, 37, 71, 0.8));
            border: 1px solid rgba(0, 102, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 102, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .service-card:hover::before {
            left: 100%;
        }
        
        .service-card:active {
            transform: translateY(-2px);
        }
        
        .service-card.selected {
            border-color: var(--accent-blue);
            box-shadow: 0 0 16px 2px rgba(0, 102, 255, 0.3), 0 0 8px 1px rgba(99, 102, 241, 0.2);
        }
        
        .call-button {
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
            border: none;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 102, 255, 0.3);
        }
        
        .call-button:hover {
            box-shadow: 0 12px 35px rgba(0, 102, 255, 0.4);
            transform: translateY(-2px);
        }
        
        .call-button:active::before {
            width: 300px;
            height: 300px;
        }
        
        .call-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }
        
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--accent-blue);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        .success-animation {
            animation: successPulse 0.6s ease-out;
        }
        
        @keyframes successPulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-[#0f0f23] via-[#1e1e3f] to-[#252547] text-white relative">
    <!-- 浮动粒子背景 -->
    <div class="floating-particles" id="particles"></div>
    
    <!-- 主容器 -->
    <div class="relative z-10 min-h-screen flex flex-col">
        <!-- 顶部标题区 -->
        <header class="text-center pt-12 pb-8 px-4">
            <div class="max-w-md mx-auto">
                <h1 class="tech-title text-3xl font-bold mb-2">餐桌1号</h1>
                <p class="text-gray-400 text-sm mb-4">欢迎使用我们的服务，请选择您需要的服务项目</p>
                <div class="flex items-center justify-center text-xs text-gray-500">
                    <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                    <span>服务时间：08:00-22:00</span>
                </div>
            </div>
        </header>
        
        <!-- 服务选项区 -->
        <main class="flex-1 px-4 pb-32">
            <div class="max-w-md mx-auto">
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <!-- 点餐服务 -->
                    <div class="service-card rounded-2xl p-6 cursor-pointer" data-service="点餐">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                                <i data-lucide="utensils" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-1">点餐</h3>
                            <p class="text-xs text-gray-400">查看菜单点餐</p>
                        </div>
                    </div>
                    
                    <!-- 加水服务 -->
                    <div class="service-card rounded-2xl p-6 cursor-pointer" data-service="加水">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center">
                                <i data-lucide="droplets" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-1">加水</h3>
                            <p class="text-xs text-gray-400">添加茶水饮品</p>
                        </div>
                    </div>
                    
                    <!-- 结账服务 -->
                    <div class="service-card rounded-2xl p-6 cursor-pointer" data-service="结账">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                                <i data-lucide="credit-card" class="w-6 h-6 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-1">结账</h3>
                            <p class="text-xs text-gray-400">买单结账</p>
                        </div>
                    </div>
                    

                </div>
                
                <!-- 备注输入 -->
                <div class="mb-8">
                    <label class="block text-sm font-medium text-gray-300 mb-2">备注信息（可选）</label>
                    <textarea id="remarkInput" class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none resize-none" rows="3" placeholder="请输入您的特殊需求或备注信息..."></textarea>
                </div>
            </div>
        </main>
        
        <!-- 底部操作区 -->
        <footer class="fixed bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-[#0f0f23] via-[#0f0f23]/90 to-transparent">
            <div class="max-w-md mx-auto">
                <!-- 选中的服务显示 -->
                <div id="selectedServices" class="mb-4 hidden">
                    <p class="text-sm text-gray-400 mb-2">已选择的服务：</p>
                    <div id="serviceList" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- 呼叫按钮 -->
                <button id="callButton" class="call-button w-full py-4 rounded-2xl font-bold text-lg text-white relative z-10 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <span id="buttonText">请选择服务项目</span>
                    <i data-lucide="phone" class="w-5 h-5 ml-2 inline"></i>
                </button>
                
                <!-- 直接评价按钮 -->
                <button id="reviewButton" class="w-full mt-3 py-3 bg-gray-700/50 hover:bg-gray-600/50 rounded-xl font-medium text-white transition-colors">
                    <i data-lucide="star" class="w-4 h-4 mr-2 inline"></i>
                    直接评价服务
                </button>
            </div>
        </footer>
    </div>
    
    <!-- 呼叫成功弹窗 -->
    <div id="successModal" class="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden">
        <div class="bg-gray-900 rounded-2xl p-8 max-w-sm mx-4 text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center success-animation">
                <i data-lucide="check" class="w-8 h-8 text-white"></i>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">呼叫成功！</h3>
            <p class="text-gray-400 mb-6">服务人员已收到您的呼叫，请稍等片刻</p>
            <button onclick="closeSuccessModal()" class="w-full py-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl font-medium text-white">
                确定
            </button>
        </div>
    </div>
    
    <!-- 评价弹窗 -->
    <div id="reviewModal" class="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden">
        <div class="bg-gray-900 rounded-2xl p-6 max-w-sm mx-4 w-full">
            <h3 class="text-lg font-bold text-white mb-4 text-center">服务评价</h3>
            
            <!-- 星级评分 -->
            <div class="text-center mb-4">
                <div class="flex justify-center space-x-1 mb-2">
                    <button class="star-btn text-2xl text-gray-400 hover:text-yellow-400 transition-colors" data-rating="1">
                        <i data-lucide="star" class="w-8 h-8"></i>
                    </button>
                    <button class="star-btn text-2xl text-gray-400 hover:text-yellow-400 transition-colors" data-rating="2">
                        <i data-lucide="star" class="w-8 h-8"></i>
                    </button>
                    <button class="star-btn text-2xl text-gray-400 hover:text-yellow-400 transition-colors" data-rating="3">
                        <i data-lucide="star" class="w-8 h-8"></i>
                    </button>
                    <button class="star-btn text-2xl text-gray-400 hover:text-yellow-400 transition-colors" data-rating="4">
                        <i data-lucide="star" class="w-8 h-8"></i>
                    </button>
                    <button class="star-btn text-2xl text-gray-400 hover:text-yellow-400 transition-colors" data-rating="5">
                        <i data-lucide="star" class="w-8 h-8"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-400">点击星星进行评分</p>
            </div>
            
            <!-- 评价内容 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">评价内容（可选）</label>
                <textarea id="reviewContent" class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none resize-none" rows="3" placeholder="请分享您的服务体验..."></textarea>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex space-x-3">
                <button onclick="closeReviewModal()" class="flex-1 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors">
                    取消
                </button>
                <button id="submitReview" class="flex-1 py-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-medium text-white" disabled>
                    提交评价
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();
        
        // 创建浮动粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        
        // 初始化粒子
        createParticles();
        
        // 服务选择逻辑
        let selectedServices = new Set();
        
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', () => {
                const service = card.dataset.service;
                
                if (selectedServices.has(service)) {
                    selectedServices.delete(service);
                    card.classList.remove('selected');
                } else {
                    selectedServices.add(service);
                    card.classList.add('selected');
                }
                
                updateSelectedServices();
            });
        });
        
        // 更新选中的服务显示
        function updateSelectedServices() {
            const selectedDiv = document.getElementById('selectedServices');
            const serviceList = document.getElementById('serviceList');
            const callButton = document.getElementById('callButton');
            const buttonText = document.getElementById('buttonText');
            
            if (selectedServices.size > 0) {
                selectedDiv.classList.remove('hidden');
                serviceList.innerHTML = '';
                
                selectedServices.forEach(service => {
                    const tag = document.createElement('span');
                    tag.className = 'px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm';
                    tag.textContent = service;
                    serviceList.appendChild(tag);
                });
                
                callButton.disabled = false;
                buttonText.textContent = `呼叫服务 (${selectedServices.size}项)`;
                callButton.classList.add('pulse-animation');
            } else {
                selectedDiv.classList.add('hidden');
                callButton.disabled = true;
                buttonText.textContent = '请选择服务项目';
                callButton.classList.remove('pulse-animation');
            }
        }
        
        // 呼叫按钮点击
        document.getElementById('callButton').addEventListener('click', () => {
            if (selectedServices.size > 0) {
                // 模拟发送呼叫请求
                const remark = document.getElementById('remarkInput').value;
                console.log('呼叫服务:', Array.from(selectedServices), '备注:', remark);
                
                // 显示成功弹窗
                document.getElementById('successModal').classList.remove('hidden');
            }
        });
        
        // 关闭成功弹窗
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            // 重置选择
            selectedServices.clear();
            document.querySelectorAll('.service-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.getElementById('remarkInput').value = '';
            updateSelectedServices();
        }
        
        // 直接评价按钮
        document.getElementById('reviewButton').addEventListener('click', () => {
            document.getElementById('reviewModal').classList.remove('hidden');
        });
        
        // 星级评分逻辑
        let currentRating = 0;
        
        document.querySelectorAll('.star-btn').forEach((star, index) => {
            star.addEventListener('click', () => {
                currentRating = index + 1;
                updateStars();
                document.getElementById('submitReview').disabled = false;
            });
            
            star.addEventListener('mouseenter', () => {
                highlightStars(index + 1);
            });
        });
        
        document.querySelector('#reviewModal .bg-gray-900').addEventListener('mouseleave', () => {
            updateStars();
        });
        
        function highlightStars(rating) {
            document.querySelectorAll('.star-btn').forEach((star, index) => {
                const icon = star.querySelector('i');
                if (index < rating) {
                    icon.classList.add('text-yellow-400');
                    icon.classList.remove('text-gray-400');
                } else {
                    icon.classList.add('text-gray-400');
                    icon.classList.remove('text-yellow-400');
                }
            });
        }
        
        function updateStars() {
            highlightStars(currentRating);
        }
        
        // 提交评价
        document.getElementById('submitReview').addEventListener('click', () => {
            const content = document.getElementById('reviewContent').value;
            console.log('提交评价:', { rating: currentRating, content });
            
            // 显示提交成功提示
            alert('评价提交成功，感谢您的反馈！');
            closeReviewModal();
        });
        
        // 关闭评价弹窗
        function closeReviewModal() {
            document.getElementById('reviewModal').classList.add('hidden');
            currentRating = 0;
            updateStars();
            document.getElementById('reviewContent').value = '';
            document.getElementById('submitReview').disabled = true;
        }
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>