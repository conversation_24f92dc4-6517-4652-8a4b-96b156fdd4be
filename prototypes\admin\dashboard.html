<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理仪表盘 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f',
                        'tech-blue': '#3b82f6',
                        'tech-purple': '#8b5cf6',
                        'tech-cyan': '#06b6d4'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech">
    <!-- 顶部导航栏 -->
    <nav class="bg-dark-card border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 tech-gradient rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">管</span>
                </div>
                <h1 class="text-xl font-bold">管理仪表盘</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-400">最后更新: 2024-01-15 14:30</span>
                <div class="w-8 h-8 bg-tech-purple rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">管</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="p-6">
        <!-- 关键指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- 总用户数 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700 hover:border-tech-blue transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">总用户数</p>
                        <p class="text-3xl font-bold text-white">8,456</p>
                        <p class="text-green-400 text-sm mt-1 flex items-center">
                            <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                            +6.2% 较上月
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-tech-blue rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- 今日活跃用户 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700 hover:border-tech-purple transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">今日活跃用户</p>
                        <p class="text-3xl font-bold text-white">2,148</p>
                        <p class="text-green-400 text-sm mt-1 flex items-center">
                            <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                            +8.5% 较昨日
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-tech-purple rounded-lg flex items-center justify-center">
                        <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- 今日发布内容 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700 hover:border-tech-cyan transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">今日发布内容</p>
                        <p class="text-3xl font-bold text-white">456</p>
                        <p class="text-green-400 text-sm mt-1 flex items-center">
                            <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                            +3.8% 较昨日
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-tech-cyan rounded-lg flex items-center justify-center">
                        <i data-lucide="file-plus" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- 待审核内容 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700 hover:border-yellow-500 transition-all duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">待审核内容</p>
                        <p class="text-3xl font-bold text-yellow-400">23</p>
                        <p class="text-yellow-400 text-sm mt-1 flex items-center">
                            <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                            需要处理
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i data-lucide="alert-circle" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表和详细数据 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 用户增长趋势 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan">用户增长趋势</h3>
                <div class="h-64 bg-gray-800 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                        <svg class="w-16 h-16 text-tech-blue mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <p class="text-gray-400">图表数据展示区域</p>
                        <p class="text-sm text-gray-500 mt-2">实际开发中集成图表库</p>
                    </div>
                </div>
            </div>

            <!-- 内容分布 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan">内容分布统计</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-tech-blue rounded-full"></div>
                            <span class="text-gray-300">商铺信息</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">1,245</span>
                            <span class="text-sm text-gray-400">(35%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-tech-purple rounded-full"></div>
                            <span class="text-gray-300">生活信息</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">892</span>
                            <span class="text-sm text-gray-400">(25%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-tech-cyan rounded-full"></div>
                            <span class="text-gray-300">出行服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">678</span>
                            <span class="text-sm text-gray-400">(19%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-gray-300">跑腿服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">534</span>
                            <span class="text-sm text-gray-400">(21%)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动和快捷操作 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 最近活动 -->
            <div class="lg:col-span-2 bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan">最近活动</h3>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3 p-3 bg-gray-800 rounded-lg">
                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div class="flex-1">
                            <p class="text-sm text-white">用户 张三 发布了新的商铺信息</p>
                            <p class="text-xs text-gray-400 mt-1">2分钟前</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-gray-800 rounded-lg">
                        <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                        <div class="flex-1">
                            <p class="text-sm text-white">内容审核：待审核内容增加</p>
                            <p class="text-xs text-gray-400 mt-1">5分钟前</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-gray-800 rounded-lg">
                        <div class="w-2 h-2 bg-tech-blue rounded-full mt-2"></div>
                        <div class="flex-1">
                            <p class="text-sm text-white">系统：数据备份完成</p>
                            <p class="text-xs text-gray-400 mt-1">10分钟前</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3 p-3 bg-gray-800 rounded-lg">
                        <div class="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                        <div class="flex-1">
                            <p class="text-sm text-white">举报处理：收到新的用户举报</p>
                            <p class="text-xs text-gray-400 mt-1">15分钟前</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan">快捷操作</h3>
                <div class="space-y-3">
                    <button class="w-full p-3 bg-tech-blue bg-opacity-20 border border-tech-blue rounded-lg text-tech-blue hover:bg-opacity-30 transition-all duration-200 text-left">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="check-circle" class="w-5 h-5"></i>
                            <span class="text-sm font-medium">内容审核</span>
                        </div>
                    </button>
                    <button class="w-full p-3 bg-tech-purple bg-opacity-20 border border-tech-purple rounded-lg text-tech-purple hover:bg-opacity-30 transition-all duration-200 text-left">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="users" class="w-5 h-5"></i>
                            <span class="text-sm font-medium">用户管理</span>
                        </div>
                    </button>
                    <button class="w-full p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded-lg text-red-400 hover:bg-opacity-30 transition-all duration-200 text-left">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="alert-triangle" class="w-5 h-5"></i>
                            <span class="text-sm font-medium">举报处理</span>
                        </div>
                    </button>
                    <button class="w-full p-3 bg-gray-600 bg-opacity-20 border border-gray-600 rounded-lg text-gray-400 hover:bg-opacity-30 transition-all duration-200 text-left">
                        <div class="flex items-center space-x-3">
                            <i data-lucide="settings" class="w-5 h-5"></i>
                            <span class="text-sm font-medium">系统设置</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();
        
        // 模拟实时数据更新
        setInterval(() => {
            const timestamp = new Date().toLocaleString('zh-CN');
            document.querySelector('nav span').textContent = `最后更新: ${timestamp}`;
        }, 30000);

        // 快捷操作按钮点击事件
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                const action = this.querySelector('span').textContent;
                alert(`跳转到${action}页面 - 实际开发中将进行页面跳转`);
            });
        });
    </script>
</body>
</html>