<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布商铺信息 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .upload-area {
            border: 2px dashed #475569;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
        }
        .image-preview {
            position: relative;
            overflow: hidden;
        }
        .image-preview img {
            transition: transform 0.3s ease;
        }
        .image-preview:hover img {
            transform: scale(1.05);
        }
        .delete-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .image-preview:hover .delete-btn {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="history.back()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                <h1 class="text-lg font-semibold text-slate-200">发布商铺信息</h1>
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" onclick="saveDraft()">
                    <i data-lucide="save" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布表单 -->
    <div class="max-w-md mx-auto px-4 py-6">
        <form id="publishForm" class="space-y-6">
            <!-- 商铺基本信息 -->
            <div class="bg-slate-800 rounded-xl p-6">

                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">商铺类型 *</label>
                        <div class="grid grid-cols-4 gap-2">
                            <button type="button" class="shop-type-btn bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-300 hover:bg-blue-600 hover:border-blue-500 hover:text-white transition-all" data-value="entity">实体</button>
                            <button type="button" class="shop-type-btn bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-300 hover:bg-blue-600 hover:border-blue-500 hover:text-white transition-all" data-value="online">网店</button>
                            <button type="button" class="shop-type-btn bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-300 hover:bg-blue-600 hover:border-blue-500 hover:text-white transition-all" data-value="vendor">摊贩</button>
                            <button type="button" class="shop-type-btn bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-300 hover:bg-blue-600 hover:border-blue-500 hover:text-white transition-all" data-value="master">师傅</button>
                        </div>
                        <input type="hidden" name="shopType" id="shopTypeValue" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">商铺名称 *</label>
                        <input type="text" name="shopName" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none" placeholder="请输入商铺名称" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">主营范围</label>
                        <textarea name="businessScope" rows="3" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none resize-none" placeholder="请描述您的主要经营范围和服务项目..."></textarea>
                    </div>
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="bg-slate-800 rounded-xl p-6">

                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">详细地址 *</label>
                        <div class="relative">
                            <input type="text" name="address" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 pr-10 text-slate-200 focus:border-blue-500 focus:outline-none" placeholder="请输入详细地址" required>
                            <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300" onclick="selectFromMap()">
                                <i data-lucide="map" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    

                </div>
            </div>

            <!-- 营业信息 -->
            <div class="bg-slate-800 rounded-xl p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">营业时间</label>
                        <div class="flex space-x-2">
                            <input type="time" name="openTime" class="flex-1 bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none" value="09:00">
                            <span class="text-slate-400 self-center">-</span>
                            <input type="time" name="closeTime" class="flex-1 bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none" value="21:00">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">联系方式 *</label>
                <div class="space-y-4">
                    <div>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                                <i data-lucide="phone" class="w-5 h-5 text-slate-400"></i>
                            </div>
                            <input type="tel" id="contact-phone" name="phone" placeholder="请输入手机号码"
                                   class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                                <i data-lucide="message-square" class="w-5 h-5 text-slate-400"></i>
                            </div>
                            <input type="text" id="sms-code" name="smsCode" placeholder="请输入短信验证码"
                                   class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                            <button type="button" id="send-sms-btn" class="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors flex items-center justify-center font-semibold whitespace-nowrap">
                                发送验证码
                            </button>
                        </div>
                    </div>
                </div>
            </div>



            <!-- 优惠券开通 -->
            <div class="bg-slate-800 rounded-xl p-6">

                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-slate-300">开通优惠券功能</h3>
                            <p class="text-xs text-slate-500">为您的商铺开通优惠券营销功能</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="enableCoupon" class="sr-only peer">
                            <div class="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    
                    <div class="bg-slate-700 rounded-lg p-4">
                        <div class="flex items-start space-x-3">
                            <i data-lucide="info" class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0"></i>
                            <div>
                                <h4 class="text-sm font-medium text-slate-300 mb-1">优惠券功能说明</h4>
                                <ul class="text-xs text-slate-400 space-y-1">
                                    <li>• 可创建满减券、折扣券等多种类型</li>
                                    <li>• 支持设置使用条件和有效期</li>
                                    <li>• 提升店铺曝光度和客户转化率</li>
                                    <li>• 首次开通免费使用30天</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex space-x-3 pt-4">
                <button type="button" class="flex-1 bg-slate-600 hover:bg-slate-500 text-slate-300 py-3 rounded-lg transition-colors" onclick="saveDraft()">
                    保存草稿
                </button>
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg transition-colors">
                    立即发布
                </button>
            </div>
        </form>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        let uploadedImages = [];
        let selectedTags = [];

        // 触发文件选择
        function triggerFileInput() {
            if (uploadedImages.length < 9) {
                document.getElementById('imageInput').click();
            } else {
                alert('最多只能上传9张图片');
            }
        }

        // 处理图片上传
        function handleImageUpload(event) {
            const files = Array.from(event.target.files);
            
            files.forEach(file => {
                if (uploadedImages.length >= 9) {
                    alert('最多只能上传9张图片');
                    return;
                }
                
                if (file.size > 5 * 1024 * 1024) {
                    alert('图片大小不能超过5MB');
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    uploadedImages.push({
                        file: file,
                        url: e.target.result
                    });
                    updateImageGrid();
                };
                reader.readAsDataURL(file);
            });
            
            // 清空input
            event.target.value = '';
        }

        // 更新图片网格
        function updateImageGrid() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';
            
            // 添加已上传的图片
            uploadedImages.forEach((image, index) => {
                const div = document.createElement('div');
                div.className = 'image-preview aspect-square rounded-lg overflow-hidden bg-slate-700';
                div.innerHTML = `
                    <img src="${image.url}" alt="商铺图片" class="w-full h-full object-cover">
                    <button class="delete-btn text-white hover:text-red-400" onclick="removeImage(${index})">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                `;
                grid.appendChild(div);
            });
            
            // 添加上传按钮（如果还没达到上限）
            if (uploadedImages.length < 9) {
                const uploadDiv = document.createElement('div');
                uploadDiv.className = 'upload-area aspect-square rounded-lg flex flex-col items-center justify-center cursor-pointer';
                uploadDiv.onclick = triggerFileInput;
                uploadDiv.innerHTML = `
                    <i data-lucide="plus" class="w-8 h-8 text-slate-400 mb-2"></i>
                    <span class="text-xs text-slate-400 text-center">添加图片</span>
                `;
                grid.appendChild(uploadDiv);
            }
            
            // 重新初始化图标
            lucide.createIcons();
        }

        // 删除图片
        function removeImage(index) {
            uploadedImages.splice(index, 1);
            updateImageGrid();
        }

        // 标签选择
        document.querySelectorAll('.tag-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tag = btn.dataset.tag;
                if (selectedTags.includes(tag)) {
                    selectedTags = selectedTags.filter(t => t !== tag);
                    btn.classList.remove('bg-blue-600', 'text-white');
                    btn.classList.add('bg-slate-600', 'text-slate-300');
                } else {
                    selectedTags.push(tag);
                    btn.classList.remove('bg-slate-600', 'text-slate-300');
                    btn.classList.add('bg-blue-600', 'text-white');
                }
            });
        });

        // 添加自定义标签
        function addCustomTag() {
            const input = document.getElementById('customTag');
            const tag = input.value.trim();
            
            if (tag && !selectedTags.includes(tag)) {
                selectedTags.push(tag);
                
                const container = document.getElementById('customTags');
                const tagElement = document.createElement('span');
                tagElement.className = 'bg-blue-600 text-white px-3 py-1 rounded-full text-sm flex items-center space-x-1';
                tagElement.innerHTML = `
                    <span>${tag}</span>
                    <button type="button" onclick="removeCustomTag('${tag}', this.parentElement)" class="hover:text-red-300">
                        <i data-lucide="x" class="w-3 h-3"></i>
                    </button>
                `;
                container.appendChild(tagElement);
                
                input.value = '';
                lucide.createIcons();
            }
        }

        // 删除自定义标签
        function removeCustomTag(tag, element) {
            selectedTags = selectedTags.filter(t => t !== tag);
            element.remove();
        }

        // 商铺类型选择
        document.querySelectorAll('.shop-type-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有按钮的选中状态
                document.querySelectorAll('.shop-type-btn').forEach(b => {
                    b.classList.remove('bg-blue-600', 'border-blue-500', 'text-white');
                    b.classList.add('bg-slate-700', 'border-slate-600', 'text-slate-300');
                });
                
                // 设置当前按钮为选中状态
                btn.classList.remove('bg-slate-700', 'border-slate-600', 'text-slate-300');
                btn.classList.add('bg-blue-600', 'border-blue-500', 'text-white');
                
                // 设置隐藏字段的值
                document.getElementById('shopTypeValue').value = btn.dataset.value;
            });
        });

        // 发送验证码
        let countdown = 0;
        function sendSmsCode() {
            const phoneInput = document.querySelector('[name="phone"]');
            const phone = phoneInput.value.trim();
            const btn = document.getElementById('smsBtn');
            
            // 验证手机号
            if (!phone) {
                alert('请先输入手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                phoneInput.focus();
                return;
            }
            
            if (countdown > 0) {
                return;
            }
            
            // 开始倒计时
            countdown = 60;
            btn.disabled = true;
            btn.classList.add('bg-slate-600', 'cursor-not-allowed');
            btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            
            const timer = setInterval(() => {
                btn.textContent = `${countdown}秒后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                    btn.classList.remove('bg-slate-600', 'cursor-not-allowed');
                    btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                }
            }, 1000);
            
            // 模拟发送验证码
            alert('验证码已发送至您的手机，请注意查收');
        }

        // 从地图选择位置
        function selectFromMap() {
            alert('正在打开地图选择位置...');
        }

        // 保存草稿
        function saveDraft() {
            alert('草稿已保存');
        }

        // 表单提交
        document.getElementById('publishForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证商铺类型
            const shopTypeValue = document.getElementById('shopTypeValue').value;
            if (!shopTypeValue) {
                alert('请选择商铺类型');
                return;
            }
            
            // 验证商铺名称
            const shopName = document.querySelector('[name="shopName"]').value.trim();
            if (!shopName) {
                alert('请输入商铺名称');
                document.querySelector('[name="shopName"]').focus();
                return;
            }
            
            // 验证地址
            const address = document.querySelector('[name="address"]').value.trim();
            if (!address) {
                alert('请输入详细地址');
                document.querySelector('[name="address"]').focus();
                return;
            }
            
            alert('商铺信息发布成功！正在跳转到产品管理页面...');
            
            // 自动跳转到产品管理页面
            setTimeout(() => {
                window.location.href = 'my-shop-products.html';
            }, 1500);
            // 这里可以添加实际的提交逻辑
        });

        // 自定义标签输入框回车事件
        document.getElementById('customTag').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addCustomTag();
            }
        });
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>