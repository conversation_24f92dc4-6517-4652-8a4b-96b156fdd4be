<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跑腿板块 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/common.js"></script>
    <link rel="stylesheet" href="../styles/design-system.css">
    <style>
        /* 页面特定样式 - 使用设计系统变量 */
        .gradient-bg {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
        }
        
        /* 使用设计系统中的标签样式 */
        .errand-tag {
            background: var(--gradient-primary);
        }
        .urgent-tag {
            background: var(--gradient-danger);
        }
        .price-tag {
            background: var(--gradient-success);
        }
        .sensitive-warning {
            background: var(--gradient-warning);
        }
        
        /* 响应式优化 */
        @media (max-width: 640px) {
            .safe-area-pb {
                padding-bottom: env(safe-area-inset-bottom, 0px);
            }
        }
        
        /* 加载状态样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 15, 35, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部搜索区域 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center space-x-3">
                <!-- 城市选择按钮 -->
                <button class="flex items-center space-x-1 bg-slate-700 px-3 py-2 rounded-lg text-sm font-medium text-slate-300 hover:bg-slate-600 transition-colors">
                    <i data-lucide="map-pin" class="w-4 h-4"></i>
                    <span>深圳</span>
                    <i data-lucide="chevron-down" class="w-3 h-3"></i>
                </button>
                
                <!-- 搜索输入框 -->
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索跑腿服务..." 
                           class="w-full bg-slate-700 border-0 rounded-lg px-4 py-2 pl-10 pr-4 text-sm text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:bg-slate-600 transition-all">
                    <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                </div>
                
                <!-- 扫一扫按钮 -->
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" 
                        data-action="scan-qr" 
                        aria-label="扫描二维码" 
                        title="扫描二维码">
                    <i data-lucide="qr-code" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 四大板块导航 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4">
            <nav class="flex items-center justify-between py-3" role="navigation" aria-label="主要板块导航">
                <a href="shops.html" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-slate-200 transition-colors" aria-label="商铺板块">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </a>
                <a href="information.html" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-slate-200 transition-colors" aria-label="信息板块">
                    <i data-lucide="info" class="w-5 h-5"></i>
                    <span class="text-xs">信息</span>
                </a>
                <a href="transportation.html" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-slate-200 transition-colors" aria-label="出行板块">
                    <i data-lucide="car" class="w-5 h-5"></i>
                    <span class="text-xs">出行</span>
                </a>
                <span class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg bg-orange-600 text-white font-medium" aria-current="page" aria-label="跑腿板块（当前页面）">
                    <i data-lucide="package" class="w-5 h-5"></i>
                    <span class="text-xs">跑腿</span>
                </span>
            </nav>
        </div>
    </div>

    <!-- 跑腿分类筛选 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex space-x-2 overflow-x-auto scrollbar-hide" role="tablist" aria-label="跑腿服务分类筛选">
                <button class="flex-shrink-0 px-4 py-2 bg-orange-600 text-white rounded-full text-sm font-medium btn-filter active" 
                        data-category="all" 
                        role="tab" 
                        aria-selected="true" 
                        aria-controls="errands-list" 
                        id="filter-all"
                        tabindex="0">全部</button>
                <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-200 rounded-full text-sm hover:bg-slate-600 transition-colors btn-filter" 
                        data-category="buy" 
                        role="tab" 
                        aria-selected="false" 
                        aria-controls="errands-list" 
                        id="filter-buy"
                        tabindex="-1">代买</button>
                <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-200 rounded-full text-sm hover:bg-slate-600 transition-colors btn-filter" 
                        data-category="delivery" 
                        role="tab" 
                        aria-selected="false" 
                        aria-controls="errands-list" 
                        id="filter-delivery"
                        tabindex="-1">代送</button>
                <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-200 rounded-full text-sm hover:bg-slate-600 transition-colors btn-filter" 
                        data-category="service" 
                        role="tab" 
                        aria-selected="false" 
                        aria-controls="errands-list" 
                        id="filter-service"
                        tabindex="-1">代办</button>
                <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-200 rounded-full text-sm hover:bg-slate-600 transition-colors btn-filter" 
                        data-category="other" 
                        role="tab" 
                        aria-selected="false" 
                        aria-controls="errands-list" 
                        id="filter-other"
                        tabindex="-1">其他</button>
            </div>
        </div>
    </div>

    <main id="main-content" class="max-w-md mx-auto px-4 pt-4 pb-24 space-y-4" role="main">
        <!-- 安全提示和违禁品清单 -->
        <aside id="errands-banner" class="bg-amber-500/10 border border-amber-500/30 rounded-lg p-3 relative" role="banner" aria-labelledby="safety-notice-title">
            <div class="flex items-start space-x-2">
                <i data-lucide="triangle-alert" class="w-5 h-5 text-amber-400 mt-0.5 flex-shrink-0" aria-hidden="true"></i>
                <div class="flex-1">
                    <h4 id="safety-notice-title" class="text-sm font-medium text-amber-400 mb-2">安全提示 & 违禁品说明</h4>
                    <p class="text-xs text-amber-200">
                        请勿委托或接受任何违禁品订单，包括但不限于危险化学品、易燃易爆物品、管制刀具、毒品等。所有交易需符合平台规定和法律法规。
                    </p>
                    <button class="text-xs text-amber-300 hover:text-amber-200 mt-2 underline" 
                            data-action="show-prohibited-items" 
                            aria-label="查看详细违禁品清单">查看违禁品清单</button>
                </div>
                <div class="flex items-center space-x-2 pl-2">
                     <span id="countdown-timer" class="text-xs text-amber-400 font-mono" aria-live="polite" aria-label="自动关闭倒计时">5s</span>
                     <button id="close-banner-btn" class="text-amber-400 hover:text-amber-300" aria-label="关闭安全提示">
                         <i data-lucide="x" class="w-4 h-4" aria-hidden="true"></i>
                     </button>
                 </div>
            </div>
        </aside>

        <!-- 跑腿任务列表 -->
        <section id="errands-list" class="space-y-3" role="tabpanel" aria-labelledby="filter-all">
        <!-- 代买服务卡片 -->
        <article class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-interactive cursor-pointer" 
                 data-category="buy" 
                 data-task-id="buy-001" 
                 data-task-title="代买星巴克咖啡" 
                 data-task-type="buy" 
                 role="button" 
                 tabindex="0" 
                 aria-label="代买星巴克咖啡任务详情">
            
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">代买星巴克咖啡</h3>
                <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-lg font-bold" aria-label="跑腿费用20元">
                    ¥20
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shopping-bag" class="w-3 h-3"></i>
                    <span>代买</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>1.5km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效期：2天</span>
                </span>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center space-x-2 mb-2 text-sm text-blue-400">
                <span class="flex items-center">
                    <i data-lucide="locate" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>星巴克南山店</span>
                </span>
                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                <span class="flex items-center">
                    <i data-lucide="map" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>科技园地铁站</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    需要代买2杯美式咖啡，大杯，无糖。咖啡费用我转账，跑腿费现金支付。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1542838132-92c53300491e?w=140&h=105&fit=crop" 
                     alt="咖啡" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1610832958506-aa56368176cf?w=140&h=105&fit=crop" 
                     alt="星巴克" class="w-24 h-18 rounded-lg object-cover">
            </div>
        </article>

        <!-- 代购服务卡片 -->
        <article class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-interactive cursor-pointer" 
                 data-category="shopping" 
                 data-task-id="shopping-001" 
                 data-task-title="代买星巴克咖啡" 
                 data-task-type="shopping" 
                 role="button" 
                 tabindex="0" 
                 aria-label="代买星巴克咖啡任务详情">
            
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">代买星巴克咖啡</h3>
                <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-lg font-bold" aria-label="跑腿费用20元">
                    ¥20
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shopping-bag" class="w-3 h-3"></i>
                    <span>代购</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>1.5km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效期：1天</span>
                </span>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center space-x-2 mb-2 text-sm text-blue-400">
                <span class="flex items-center">
                    <i data-lucide="locate" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>星巴克南山店</span>
                </span>
                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                <span class="flex items-center">
                    <i data-lucide="map" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>科技园地铁站</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    需要代买2杯美式咖啡，大杯，无糖。咖啡费用我转账，跑腿费现金支付。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1542838132-92c53300491e?w=140&h=105&fit=crop" 
                     alt="咖啡" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1610832958506-aa56368176cf?w=140&h=105&fit=crop" 
                     alt="星巴克" class="w-24 h-18 rounded-lg object-cover">
            </div>
        </article>

        <!-- 产品广告卡片 -->
        <article class="bg-gradient-to-r from-purple-900 to-pink-900 rounded-xl shadow-md border border-purple-700 p-3 relative overflow-hidden" 
                 data-ad-id="ad-001" 
                 data-product="iPhone 15 Pro Max" 
                 role="button" 
                 tabindex="0" 
                 aria-label="iPhone 15 Pro Max 256GB 产品详情">
            
            <!-- 广告标识 -->
            <div class="absolute top-0 right-0 text-yellow-400 text-xs px-2 py-1 font-bold flex items-center space-x-1" aria-label="广告内容">
                <i data-lucide="megaphone" class="w-3 h-3"></i>
                <span>广告</span>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=140&h=105&fit=crop" 
                         alt="iPhone 15 Pro Max" class="w-full h-full object-cover">
                </div>
                
                <div class="flex-1">
                    <h3 class="font-bold text-white text-base mb-1">iPhone 15 Pro Max 256GB</h3>
                    <div class="flex flex-wrap gap-1 mb-1">
                        <span class="text-yellow-400 text-xs font-medium flex items-center space-x-1">
                            <i data-lucide="tag" class="w-3 h-3"></i>
                            <span>特价</span>
                        </span>
                    </div>
                    <p class="text-xs text-pink-200 line-clamp-2">苹果官方授权店，全新正品iPhone 15 Pro Max，256GB大容量，限时特价优惠。</p>
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-yellow-400 text-xs font-bold">¥8888 <span class="text-pink-200 line-through text-xs">¥9999</span></span>
                        <button class="text-xs px-2 py-1 bg-yellow-500 text-slate-900 rounded-lg hover:bg-yellow-600 transition-colors">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>
        </article>

        <!-- 代送服务卡片 -->
        <article class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-interactive cursor-pointer" 
                 data-category="delivery" 
                 data-task-id="delivery-001" 
                 data-task-title="代送文件到公司" 
                 data-task-type="delivery" 
                 role="button" 
                 tabindex="0" 
                 aria-label="代送文件到公司任务详情">
            
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">代送文件到公司</h3>
                <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-lg font-bold" aria-label="跑腿费用15元">
                    ¥15
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-yellow-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="mail" class="w-3 h-3"></i>
                    <span>代送</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>3.5km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效期：1天</span>
                </span>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center space-x-2 mb-2 text-sm text-blue-400">
                <span class="flex items-center">
                    <i data-lucide="locate" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>华强北电子市场</span>
                </span>
                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                <span class="flex items-center">
                    <i data-lucide="map" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>福田CBD写字楼</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    需要代送一份重要合同文件到福田区CBD中心大厦，比较急。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=140&h=105&fit=crop" 
                     alt="文件" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=140&h=105&fit=crop" 
                     alt="办公楼" class="w-24 h-18 rounded-lg object-cover">
            </div>
        </article>

        <!-- 代办服务卡片 -->
        <article class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-interactive cursor-pointer" 
                 data-category="service" 
                 data-task-id="service-001" 
                 data-task-title="代办银行业务" 
                 data-task-type="service" 
                 role="button" 
                 tabindex="0" 
                 aria-label="代办银行业务任务详情">
            
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">代办银行业务</h3>
                <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-lg font-bold" aria-label="跑腿费用50元">
                    ¥50
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-teal-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5" aria-label="任务类型：代办">
                    <i data-lucide="clipboard-list" class="w-3 h-3"></i>
                    <span>代办</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5" aria-label="发布者已认证">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>2.8km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效期：5天</span>
                </span>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center space-x-2 mb-2 text-sm text-blue-400">
                <span class="flex items-center">
                    <i data-lucide="locate" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>招商银行南山支行</span>
                </span>
                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                <span class="flex items-center">
                    <i data-lucide="map" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>南山区政府</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    需要代办银行卡挂失和补办业务，本人因出差无法亲自办理。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1541354329998-f4d9a9f9297f?w=140&h=105&fit=crop" 
                     alt="银行" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=140&h=105&fit=crop" 
                     alt="银行卡" class="w-24 h-18 rounded-lg object-cover">
            </div>
        </article>

        <!-- 其他跑腿服务卡片 -->
        <article class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-interactive cursor-pointer" 
                 data-category="other" 
                 data-task-id="other-001" 
                 data-task-title="帮忙排队取号" 
                 data-task-type="other" 
                 role="button" 
                 tabindex="0" 
                 aria-label="帮忙排队取号任务详情">
            
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">帮忙排队取号</h3>
                <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-lg font-bold" aria-label="跑腿费用40元">
                    ¥40
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5" aria-label="任务类型：其他">
                    <i data-lucide="users" class="w-3 h-3"></i>
                    <span>其他</span>
                </span>
                <span class="text-gray-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5" aria-label="发布者未认证">
                    <i data-lucide="shield" class="w-3 h-3"></i>
                    <span>未认证</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>5.2km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效期：5天</span>
                </span>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center space-x-2 mb-2 text-sm text-blue-400">
                <span class="flex items-center">
                    <i data-lucide="locate" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>深圳市民中心</span>
                </span>
                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                <span class="flex items-center">
                    <i data-lucide="map" class="w-4 h-4 mr-1" aria-hidden="true"></i>
                    <span>福田区行政服务大厅</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    需要帮忙到深圳市人民医院排队取号，挂心内科专家号。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=140&h=105&fit=crop" 
                     alt="医院" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=140&h=105&fit=crop" 
                     alt="排队取号" class="w-24 h-18 rounded-lg object-cover">
            </div>
        </article>
    </section>

    <!-- 免责声明 -->
    <div id="errands-reminder" class="max-w-md mx-auto px-4 mb-4 transition-opacity duration-500 opacity-0">
        <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-3">
            <div class="flex items-start space-x-2">
                <i data-lucide="info" class="w-4 h-4 text-slate-400 mt-0.5 flex-shrink-0"></i>
                <div class="flex-1">
                    <p class="text-xs text-slate-400 leading-relaxed">
                        本平台仅提供信息发布服务，不参与具体交易过程。用户应自行判断信息真实性，交易风险自担。平台不对因使用本服务而产生的任何损失承担责任。
                    </p>
                    <button class="text-xs text-blue-400 hover:text-blue-300 mt-1 underline" onclick="showFullDisclaimer()">查看完整免责声明</button>
                </div>
                <button onclick="document.getElementById('errands-reminder').style.display='none'" class="text-slate-500 hover:text-slate-400 flex-shrink-0">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb" role="navigation" aria-label="主导航">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <a href="shops.html" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors" 
                   data-page="shops" 
                   aria-label="商铺页面">
                    <i data-lucide="store" class="w-5 h-5" aria-hidden="true"></i>
                    <span class="text-xs">商铺</span>
                </a>
                <button class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors" 
                        data-action="show-messages" 
                        aria-label="查看消息">
                    <i data-lucide="message-circle" class="w-5 h-5" aria-hidden="true"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-teal-400" 
                        data-action="show-publish-modal" 
                        aria-label="发布新任务">
                    <i data-lucide="plus-circle" class="w-6 h-6" aria-hidden="true"></i>
                    <span class="text-xs font-medium">发布</span>
                </button>
                <button class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors" 
                        data-action="show-help" 
                        aria-label="帮助中心">
                    <i data-lucide="help-circle" class="w-5 h-5" aria-hidden="true"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <a href="profile.html" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors" 
                   data-page="profile" 
                   aria-label="个人中心">
                    <i data-lucide="user" class="w-5 h-5" aria-hidden="true"></i>
                    <span class="text-xs">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="publish-modal-title">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-xl max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 id="publish-modal-title" class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6" role="group" aria-label="发布类型选择">
                    <button data-action="navigate-to-publish" data-type="shops" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors" aria-label="发布店铺信息">
                        <i data-lucide="store" class="w-8 h-8 text-white" aria-hidden="true"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button data-action="navigate-to-publish" data-type="information" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors" aria-label="发布本地信息">
                        <i data-lucide="info" class="w-8 h-8 text-white" aria-hidden="true"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布本地信息</span>
                    </button>
                    
                    <button data-action="navigate-to-publish" data-type="transportation" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-colors" aria-label="发布出行信息">
                        <i data-lucide="car" class="w-8 h-8 text-white" aria-hidden="true"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button data-action="navigate-to-publish" data-type="errands" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl hover:from-orange-600 hover:to-amber-600 transition-colors" aria-label="发布跑腿任务">
                        <i data-lucide="package" class="w-8 h-8 text-white" aria-hidden="true"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿任务</span>
                    </button>
                </div>
                
                <div class="flex justify-center">
                    <button onclick="closePublishModal()" class="flex items-center space-x-2 px-6 py-2 bg-slate-700 text-white rounded-lg hover:bg-slate-600 transition-colors" aria-label="关闭发布选择弹窗">
                        <i data-lucide="x" class="w-4 h-4" aria-hidden="true"></i>
                        <span>取消</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        // 初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            // 添加跑腿卡片点击事件处理
            document.querySelectorAll('.card-interactive').forEach(card => {
                card.addEventListener('click', function() {
                    const taskId = this.dataset.taskId;
                    const taskTitle = this.dataset.taskTitle;
                    window.location.href = 'errands-detail.html?id=' + taskId + '&title=' + encodeURIComponent(taskTitle);
                });
            });
            
            // 筛选功能
            document.querySelectorAll('.btn-filter').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 更新按钮样式
                    document.querySelectorAll('.btn-filter').forEach(b => {
                        b.classList.remove('bg-orange-600', 'text-white');
                        b.classList.add('bg-slate-700', 'text-slate-200');
                        b.setAttribute('aria-selected', 'false');
                        b.tabIndex = -1;
                    });
                    
                    this.classList.remove('bg-slate-700', 'text-slate-200');
                    this.classList.add('bg-orange-600', 'text-white');
                    this.setAttribute('aria-selected', 'true');
                    this.tabIndex = 0;
                    
                    // 筛选卡片
                    const category = this.dataset.category;
                    const cards = document.querySelectorAll('#errands-list article');
                    
                    cards.forEach(card => {
                        if (category === 'all' || card.dataset.category === category) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
        
        // 显示发布弹窗
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }
        
        // 关闭发布弹窗
        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }
        
        // 显示完整免责声明
        function showFullDisclaimer() {
            alert('免责声明\n\n1. 本平台仅提供信息发布渠道，不参与用户间的交易过程。\n2. 用户应自行核实信息真实性，平台不对信息准确性负责。\n3. 用户间交易产生的纠纷由交易双方自行解决，平台不承担责任。\n4. 严禁发布违法违规信息，一经发现立即删除并追究责任。\n5. 平台有权对可能存在风险的信息进行审核或下架。');
        }
    </script>
</body>
</html>