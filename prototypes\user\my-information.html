<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的信息 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .info-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
            border-color: rgba(99, 102, 241, 0.3);
        }
        .status-badge {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white"></i>
            </button>
            <h1 class="text-xl font-semibold text-white">我的信息</h1>
            <div class="flex-1"></div>
            <button onclick="window.location.href='publish-select.html'" class="p-2 hover:bg-white/20 rounded-lg transition-colors" title="发布新信息">
                <i data-lucide="plus" class="w-6 h-6 text-white"></i>
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="max-w-md mx-auto p-4">
        <div class="grid grid-cols-4 gap-3 mb-6">
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-blue-400">5</div>
                <div class="text-xs text-slate-400">全部</div>
            </div>
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-green-400">3</div>
                <div class="text-xs text-slate-400">进行中</div>
            </div>
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-yellow-400">1</div>
                <div class="text-xs text-slate-400">已完成</div>
            </div>
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-red-400">1</div>
                <div class="text-xs text-slate-400">已过期</div>
            </div>
        </div>

        <!-- 筛选标签 -->
        <div class="flex space-x-2 mb-4 overflow-x-auto">
            <button onclick="filterByType('all')" class="filter-btn bg-blue-600 text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">全部</button>
            <button onclick="filterByType('recruitment')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">招聘</button>
            <button onclick="filterByType('housing')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">租房</button>
            <button onclick="filterByType('secondhand')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">二手</button>
            <button onclick="filterByType('other')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">其他</button>
        </div>

        <!-- 信息列表 -->
        <div class="space-y-4" id="informationList">
            <!-- 招聘信息 -->
            <div class="info-card bg-slate-800 rounded-xl p-4" data-type="recruitment">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=60&h=60&fit=crop" 
                         alt="招聘" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1560472354-b33ff0c44a43 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">咖啡师招聘</h3>
                            <span class="status-badge bg-green-500 text-white px-2 py-1 rounded-full text-xs">进行中</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">薪资：5000-8000元/月，有经验优先，包食宿</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="eye" class="w-3 h-3"></i>
                                    <span>128</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="message-circle" class="w-3 h-3"></i>
                                    <span>12</span>
                                </span>
                                <span>2天前</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="editInformation('1')" class="text-blue-400 hover:text-blue-300 transition-colors" title="编辑">
                                    <i data-lucide="edit-2" class="w-4 h-4"></i>
                                </button>
                                <button onclick="toggleStatus('1')" class="text-red-400 hover:text-red-300 transition-colors" title="下架">
                                    <i data-lucide="eye-off" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteInformation('1')" class="text-red-400 hover:text-red-300 transition-colors" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 租房信息 -->
            <div class="info-card bg-slate-800 rounded-xl p-4" data-type="housing">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=60&h=60&fit=crop" 
                         alt="租房" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1560448204-e02f11c3d0e2 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">市中心单间出租</h3>
                            <span class="status-badge bg-green-500 text-white px-2 py-1 rounded-full text-xs">进行中</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">1200元/月，精装修，家具齐全，地铁口附近</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="eye" class="w-3 h-3"></i>
                                    <span>89</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="message-circle" class="w-3 h-3"></i>
                                    <span>8</span>
                                </span>
                                <span>1天前</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="editInformation('2')" class="text-blue-400 hover:text-blue-300 transition-colors" title="编辑">
                                    <i data-lucide="edit-2" class="w-4 h-4"></i>
                                </button>
                                <button onclick="toggleStatus('2')" class="text-yellow-400 hover:text-yellow-300 transition-colors" title="暂停/恢复">
                                    <i data-lucide="pause" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteInformation('2')" class="text-red-400 hover:text-red-300 transition-colors" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 二手物品 -->
            <div class="info-card bg-slate-800 rounded-xl p-4" data-type="secondhand">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=60&h=60&fit=crop" 
                         alt="二手笔记本" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1517336714731-489689fd1ca8 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">MacBook Pro 出售</h3>
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">已完成</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">8500元，2021款，9成新，配件齐全</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="eye" class="w-3 h-3"></i>
                                    <span>156</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="message-circle" class="w-3 h-3"></i>
                                    <span>23</span>
                                </span>
                                <span>5天前</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="viewDetails('3')" class="text-slate-400 hover:text-slate-300 transition-colors" title="查看详情">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteInformation('3')" class="text-red-400 hover:text-red-300 transition-colors" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息 -->
            <div class="info-card bg-slate-800 rounded-xl p-4" data-type="other">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=60&h=60&fit=crop" 
                         alt="宠物寄养" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">宠物寄养服务</h3>
                            <span class="status-badge bg-green-500 text-white px-2 py-1 rounded-full text-xs">进行中</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">专业宠物寄养，环境优良，价格合理</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="eye" class="w-3 h-3"></i>
                                    <span>67</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="message-circle" class="w-3 h-3"></i>
                                    <span>5</span>
                                </span>
                                <span>3天前</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="editInformation('4')" class="text-blue-400 hover:text-blue-300 transition-colors" title="编辑">
                                    <i data-lucide="edit-2" class="w-4 h-4"></i>
                                </button>
                                <button onclick="toggleStatus('4')" class="text-yellow-400 hover:text-yellow-300 transition-colors" title="暂停/恢复">
                                    <i data-lucide="pause" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteInformation('4')" class="text-red-400 hover:text-red-300 transition-colors" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 过期信息 -->
            <div class="info-card bg-slate-800 rounded-xl p-4 opacity-60" data-type="recruitment">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop" 
                         alt="过期招聘" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">服务员招聘</h3>
                            <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">已过期</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">3000-4000元/月，工作轻松，环境好</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="eye" class="w-3 h-3"></i>
                                    <span>45</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="message-circle" class="w-3 h-3"></i>
                                    <span>3</span>
                                </span>
                                <span>1周前</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="renewInformation('5')" class="text-green-400 hover:text-green-300 transition-colors" title="重新发布">
                                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteInformation('5')" class="text-red-400 hover:text-red-300 transition-colors" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="text-center py-12 hidden">
            <i data-lucide="inbox" class="w-16 h-16 text-slate-600 mx-auto mb-4"></i>
            <p class="text-slate-400 mb-4">暂无相关信息</p>
            <button onclick="window.location.href='publish-select.html'" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                发布信息
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布各类信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿需求</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 筛选功能
        function filterByType(type) {
            const cards = document.querySelectorAll('.info-card');
            const buttons = document.querySelectorAll('.filter-btn');
            const emptyState = document.getElementById('emptyState');
            
            // 更新按钮状态
            buttons.forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            event.target.classList.remove('bg-slate-700', 'text-slate-300');
            event.target.classList.add('bg-blue-600', 'text-white');
            
            let visibleCount = 0;
            
            cards.forEach(card => {
                if (type === 'all' || card.dataset.type === type) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 显示/隐藏空状态
            if (visibleCount === 0) {
                emptyState.classList.remove('hidden');
            } else {
                emptyState.classList.add('hidden');
            }
        }

        // 编辑信息
        function editInformation(id) {
            console.log('编辑信息:', id);
            alert('跳转到编辑页面');
            // 实际应用中跳转到编辑页面
        }

        // 切换状态
        function toggleStatus(id) {
            console.log('切换状态:', id);
            const card = document.querySelector(`[data-id="${id}"]`);
            alert('信息状态已切换');
            // 实际应用中更新状态
        }

        // 删除信息
        function deleteInformation(id) {
            if (confirm('确定要删除这条信息吗？删除后无法恢复。')) {
                console.log('删除信息:', id);
                alert('信息已删除');
                // 实际应用中调用删除API
                location.reload();
            }
        }

        // 查看详情
        function viewDetails(id) {
            console.log('查看详情:', id);
            alert('跳转到详情页面');
            // 实际应用中跳转到详情页面
        }

        // 重新发布
        function renewInformation(id) {
            if (confirm('确定要重新发布这条信息吗？')) {
                console.log('重新发布:', id);
                alert('信息已重新发布');
                // 实际应用中调用重新发布API
                location.reload();
            }
        }

        // 全局函数
        // 发布弹窗功能
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }

        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }

        function navigateToPublish(type) {
            closePublishModal();
            switch(type) {
                case 'shops':
                    window.location.href = 'publish-shop.html';
                    break;
                case 'information':
                    window.location.href = 'publish-information.html';
                    break;
                case 'transportation':
                    window.location.href = 'publish-transportation.html';
                    break;
                case 'errands':
                    window.location.href = 'publish-errands.html';
                    break;
            }
        }

        window.filterByType = filterByType;
        window.editInformation = editInformation;
        window.toggleStatus = toggleStatus;
        window.deleteInformation = deleteInformation;
        window.viewDetails = viewDetails;
        window.renewInformation = renewInformation;
        window.showPublishModal = showPublishModal;
        window.closePublishModal = closePublishModal;
        window.navigateToPublish = navigateToPublish;
    </script>
</body>
</html>