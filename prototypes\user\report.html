<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>举报 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            min-height: 100vh;
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .report-card {
            background: linear-gradient(135deg, #1e1e3f 0%, #252547 100%);
            border: 1px solid #2a2d4a;
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
        .reason-option {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid #374151;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .reason-option:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }
        .reason-option.selected {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-color: #3b82f6;
            color: white;
        }
        .tab-active {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }
        .tab-inactive {
            background: rgba(255, 255, 255, 0.1);
            color: #94a3b8;
        }
        .status-pending {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .status-processing {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .status-resolved {
            background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        }
        .status-rejected {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
    </style>
</head>
<body class="text-slate-100">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-900/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <button onclick="goBack()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                <h1 class="text-lg font-semibold">举报中心</h1>
            </div>
            <div class="flex items-center space-x-2">
                <button onclick="showHelp()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 标签页导航 -->
    <div class="p-4">
        <div class="flex bg-slate-800/50 rounded-xl p-1">
            <button onclick="switchTab('new-report')" id="tab-new-report" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-active">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="flag" class="w-4 h-4"></i>
                    <span>新建举报</span>
                </div>
            </button>
            <button onclick="switchTab('my-reports')" id="tab-my-reports" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-inactive">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="list" class="w-4 h-4"></i>
                    <span>我的举报</span>
                    <span id="reports-count" class="bg-white/20 px-2 py-0.5 rounded-full text-xs">3</span>
                </div>
            </button>
        </div>
    </div>

    <!-- 新建举报 -->
    <div id="new-report" class="px-4 pb-20">
        <div class="report-card rounded-xl p-6">
            <div class="flex items-center space-x-3 mb-6">
                <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                    <i data-lucide="shield-alert" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-white">举报违规内容</h2>
                    <p class="text-slate-400 text-sm">帮助我们维护社区环境</p>
                </div>
            </div>

            <form id="report-form" class="space-y-6">
                <!-- 举报类型 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-3">举报类型 *</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="reason-option rounded-lg p-3" onclick="selectReason('spam')">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="spam" class="w-5 h-5"></i>
                                <span class="text-sm font-medium">垃圾信息</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">广告、刷屏等</p>
                        </div>
                        <div class="reason-option rounded-lg p-3" onclick="selectReason('fraud')">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="triangle-alert" class="w-5 h-5"></i>
                                <span class="text-sm font-medium">欺诈行为</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">虚假信息、诈骗</p>
                        </div>
                        <div class="reason-option rounded-lg p-3" onclick="selectReason('inappropriate')">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="eye-off" class="w-5 h-5"></i>
                                <span class="text-sm font-medium">不当内容</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">色情、暴力等</p>
                        </div>
                        <div class="reason-option rounded-lg p-3" onclick="selectReason('harassment')">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="user-minus" class="w-5 h-5"></i>
                                <span class="text-sm font-medium">骚扰他人</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">恶意骚扰、辱骂</p>
                        </div>
                        <div class="reason-option rounded-lg p-3" onclick="selectReason('copyright')">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="copyright" class="w-5 h-5"></i>
                                <span class="text-sm font-medium">侵权内容</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">盗用图片、文字</p>
                        </div>
                        <div class="reason-option rounded-lg p-3" onclick="selectReason('other')">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="more-horizontal" class="w-5 h-5"></i>
                                <span class="text-sm font-medium">其他违规</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">其他违规行为</p>
                        </div>
                    </div>
                </div>

                <!-- 举报对象 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">举报对象 *</label>
                    <select id="report-target-type" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none" onchange="updateTargetOptions()">
                        <option value="">请选择举报对象类型</option>
                        <option value="shop">商家/店铺</option>
                        <option value="user">用户</option>
                        <option value="information">信息发布</option>
                        <option value="service">服务发布</option>
                    </select>
                </div>

                <!-- 具体对象 -->
                <div id="target-details" class="hidden">
                    <label class="block text-sm font-medium text-slate-300 mb-2">具体对象 *</label>
                    <input type="text" id="target-name" placeholder="请输入具体的举报对象" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                    <p class="text-xs text-slate-500 mt-1">例如：商家名称、用户昵称、信息标题等</p>
                </div>

                <!-- 详细描述 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">详细描述 *</label>
                    <textarea id="report-description" placeholder="请详细描述违规行为，包括时间、地点、具体情况等..." class="w-full h-32 bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"></textarea>
                    <div class="flex justify-between items-center mt-1">
                        <p class="text-xs text-slate-500">请提供详细信息以便我们快速处理</p>
                        <span id="description-count" class="text-xs text-slate-500">0/500</span>
                    </div>
                </div>

                <!-- 证据上传 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">上传证据（可选）</label>
                    <div class="grid grid-cols-4 gap-3">
                        <div class="aspect-square bg-slate-700 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center cursor-pointer hover:border-blue-500 transition-colors" onclick="uploadEvidence()">
                            <i data-lucide="plus" class="w-6 h-6 text-slate-400"></i>
                        </div>
                    </div>
                    <p class="text-xs text-slate-500 mt-2">支持图片、截图等证据材料，最多上传5张</p>
                </div>

                <!-- 联系方式 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">联系方式（可选）</label>
                    <input type="text" id="contact-info" placeholder="手机号，便于我们联系您了解详情" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                    <p class="text-xs text-slate-500 mt-1">仅用于举报处理，不会泄露给第三方</p>
                </div>

                <!-- 提交按钮 -->
                <div class="pt-4">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
                        <i data-lucide="send" class="w-5 h-5"></i>
                        <span>提交举报</span>
                    </button>
                    <p class="text-xs text-slate-500 text-center mt-2">我们将在24小时内处理您的举报</p>
                </div>
            </form>
        </div>
    </div>

    <!-- 我的举报 -->
    <div id="my-reports" class="px-4 pb-20 hidden">
        <div class="space-y-4">
            <!-- 处理中的举报 -->
            <div class="report-card rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="flag" class="w-5 h-5 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white mb-1">举报垃圾信息</h3>
                            <p class="text-slate-400 text-sm mb-2">举报对象：某商家发布的重复广告信息</p>
                            <p class="text-slate-300 text-sm">该商家在多个板块重复发布相同的广告内容，严重影响用户体验...</p>
                        </div>
                    </div>
                    <span class="status-processing text-white px-3 py-1 rounded-full text-xs font-medium">处理中</span>
                </div>
                <div class="flex items-center justify-between text-xs text-slate-500">
                    <span>举报时间：2024-02-20 14:30</span>
                    <span>举报编号：R202402200001</span>
                </div>
            </div>

            <!-- 已处理的举报 -->
            <div class="report-card rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="circle-check" class="w-5 h-5 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white mb-1">举报欺诈行为</h3>
                            <p class="text-slate-400 text-sm mb-2">举报对象：虚假商家信息</p>
                            <p class="text-slate-300 text-sm mb-2">该商家发布虚假地址和联系方式，涉嫌欺诈行为...</p>
                            <div class="bg-green-900/30 border border-green-700 rounded-lg p-3">
                                <p class="text-green-300 text-sm"><strong>处理结果：</strong>经核实，该商家确实存在虚假信息，已对其进行封号处理。感谢您的举报！</p>
                            </div>
                        </div>
                    </div>
                    <span class="status-resolved text-white px-3 py-1 rounded-full text-xs font-medium">已处理</span>
                </div>
                <div class="flex items-center justify-between text-xs text-slate-500">
                    <span>处理时间：2024-02-18 16:45</span>
                    <span>举报编号：R202402180001</span>
                </div>
            </div>

            <!-- 被驳回的举报 -->
            <div class="report-card rounded-xl p-4">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="circle-x" class="w-5 h-5 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white mb-1">举报不当内容</h3>
                            <p class="text-slate-400 text-sm mb-2">举报对象：某用户发布的信息</p>
                            <p class="text-slate-300 text-sm mb-2">认为该用户发布的内容不合适...</p>
                            <div class="bg-red-900/30 border border-red-700 rounded-lg p-3">
                                <p class="text-red-300 text-sm"><strong>驳回原因：</strong>经审核，该内容未违反社区规定。如有疑问，请提供更详细的证据。</p>
                            </div>
                        </div>
                    </div>
                    <span class="status-rejected text-white px-3 py-1 rounded-full text-xs font-medium">已驳回</span>
                </div>
                <div class="flex items-center justify-between text-xs text-slate-500">
                    <span>处理时间：2024-02-15 10:20</span>
                    <span>举报编号：R202402150001</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">举报说明</h3>
                    <button onclick="closeHelpModal()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                <div class="space-y-4 text-sm text-slate-300">
                    <div>
                        <h4 class="font-medium text-white mb-2">什么情况下可以举报？</h4>
                        <p>• 发布垃圾信息、广告刷屏</p>
                        <p>• 虚假信息、欺诈行为</p>
                        <p>• 不当内容（色情、暴力等）</p>
                        <p>• 恶意骚扰、辱骂他人</p>
                        <p>• 侵犯他人权益</p>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-2">举报处理流程</h4>
                        <p>1. 提交举报信息</p>
                        <p>2. 管理员审核调查</p>
                        <p>3. 24小时内给出处理结果</p>
                        <p>4. 通知举报者处理结果</p>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-2">注意事项</h4>
                        <p>• 请提供真实有效的举报信息</p>
                        <p>• 恶意举报将被追究责任</p>
                        <p>• 举报信息将严格保密</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        let currentTab = 'new-report';
        let selectedReason = '';

        // 标签页切换
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签页样式
            document.querySelectorAll('[id^="tab-"]').forEach(btn => {
                btn.className = btn.className.replace('tab-active', 'tab-inactive');
            });
            document.getElementById(`tab-${tab}`).className = 
                document.getElementById(`tab-${tab}`).className.replace('tab-inactive', 'tab-active');
            
            // 显示对应内容
            document.getElementById('new-report').classList.add('hidden');
            document.getElementById('my-reports').classList.add('hidden');
            
            const targetElement = document.getElementById(tab);
            if (targetElement) {
                targetElement.classList.remove('hidden');
            }
        }

        // 选择举报原因
        function selectReason(reason) {
            selectedReason = reason;
            
            // 更新选中状态
            document.querySelectorAll('.reason-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
        }

        // 更新举报对象选项
        function updateTargetOptions() {
            const targetType = document.getElementById('report-target-type').value;
            const targetDetails = document.getElementById('target-details');
            const targetName = document.getElementById('target-name');
            
            if (targetType) {
                targetDetails.classList.remove('hidden');
                
                // 更新占位符
                const placeholders = {
                    'shop': '请输入商家名称',
                    'user': '请输入用户昵称',
                    'information': '请输入信息标题',
                    'service': '请输入服务标题'
                };
                targetName.placeholder = placeholders[targetType] || '请输入具体的举报对象';
            } else {
                targetDetails.classList.add('hidden');
            }
        }

        // 上传证据
        function uploadEvidence() {
            console.log('上传证据');
            // 这里可以打开文件选择器
        }

        // 字符计数
        document.getElementById('report-description').addEventListener('input', function() {
            const count = this.value.length;
            document.getElementById('description-count').textContent = `${count}/500`;
            
            if (count > 500) {
                this.value = this.value.substring(0, 500);
                document.getElementById('description-count').textContent = '500/500';
            }
        });

        // 表单提交
        document.getElementById('report-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证必填项
            if (!selectedReason) {
                alert('请选择举报类型');
                return;
            }
            
            const targetType = document.getElementById('report-target-type').value;
            if (!targetType) {
                alert('请选择举报对象类型');
                return;
            }
            
            const targetName = document.getElementById('target-name').value.trim();
            if (!targetName) {
                alert('请输入具体的举报对象');
                return;
            }
            
            const description = document.getElementById('report-description').value.trim();
            if (!description) {
                alert('请填写详细描述');
                return;
            }
            
            // 收集表单数据
            const formData = {
                reason: selectedReason,
                targetType: targetType,
                targetName: targetName,
                description: description,
                contactInfo: document.getElementById('contact-info').value.trim()
            };
            
            console.log('提交举报:', formData);
            
            // 模拟提交成功
            const submitButton = document.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = '提交中...';
            
            // 模拟API提交
            setTimeout(() => {
                // 添加时间戳
                formData.timestamp = new Date().toISOString();
                formData.reportId = 'RPT' + Date.now();
                
                console.log('举报已提交:', formData);
                
                // 显示成功提示
                alert('举报提交成功！我们将在24小时内处理您的举报。');
                
                // 重置表单
                document.getElementById('report-form').reset();
                selectedReason = null;
                document.querySelectorAll('.reason-option').forEach(option => {
                    option.classList.remove('selected');
                });
                document.getElementById('description-count').textContent = '0/500';
                
                // 恢复按钮状态
                submitButton.disabled = false;
                submitButton.textContent = '提交举报';
            }, 1500);
            
            // 重置表单
            this.reset();
            selectedReason = '';
            document.querySelectorAll('.reason-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('target-details').classList.add('hidden');
            document.getElementById('description-count').textContent = '0/500';
        });

        // 显示帮助
        function showHelp() {
            document.getElementById('help-modal').classList.remove('hidden');
        }

        // 关闭帮助弹窗
        function closeHelpModal() {
            document.getElementById('help-modal').classList.add('hidden');
        }

        // 返回
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHelpModal();
            }
        });
    </script>
</body>
</html>