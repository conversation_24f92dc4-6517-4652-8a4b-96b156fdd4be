<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据统计分析 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .metric-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 顶部导航 -->
    <nav class="gradient-bg p-4 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i data-lucide="layout-dashboard" class="w-8 h-8"></i>
                <h1 class="text-xl font-bold">本地助手管理后台</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm">管理员</span>
                <i data-lucide="user" class="w-6 h-6"></i>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold mb-2">数据统计分析</h2>
            <p class="text-gray-400">全面了解平台运营数据，支持科学决策</p>
        </div>

        <!-- 时间筛选 -->
        <div class="bg-gray-800 rounded-lg p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex space-x-4">
                    <button class="px-4 py-2 bg-blue-600 rounded-lg text-sm">今天</button>
                    <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm transition-colors">近7天</button>
                    <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm transition-colors">近30天</button>
                    <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm transition-colors">自定义</button>
                </div>
                
                <div class="flex items-center space-x-4">
                    <input type="date" class="px-3 py-2 bg-gray-700 rounded-lg text-sm">
                    <span class="text-gray-400">至</span>
                    <input type="date" class="px-3 py-2 bg-gray-700 rounded-lg text-sm">
                    <button class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-sm transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        导出报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 核心指标概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="metric-card p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-blue-600 p-3 rounded-lg">
                        <i data-lucide="users" class="w-6 h-6"></i>
                    </div>
                    <span class="text-xs bg-green-600 px-2 py-1 rounded">+12.5%</span>
                </div>
                <h3 class="text-2xl font-bold mb-1">15,234</h3>
                <p class="text-gray-400 text-sm">总用户数</p>
                <div class="flex items-center mt-2">
                    <i data-lucide="trending-up" class="w-4 h-4 trend-up mr-1"></i>
                    <span class="text-sm trend-up">较上月增长 12.5%</span>
                </div>
            </div>
            
            <div class="metric-card p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-green-600 p-3 rounded-lg">
                        <i data-lucide="activity" class="w-6 h-6"></i>
                    </div>
                    <span class="text-xs bg-green-600 px-2 py-1 rounded">+8.3%</span>
                </div>
                <h3 class="text-2xl font-bold mb-1">8,567</h3>
                <p class="text-gray-400 text-sm">活跃用户</p>
                <div class="flex items-center mt-2">
                    <i data-lucide="trending-up" class="w-4 h-4 trend-up mr-1"></i>
                    <span class="text-sm trend-up">较上月增长 8.3%</span>
                </div>
            </div>
            
            <div class="metric-card p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-purple-600 p-3 rounded-lg">
                        <i data-lucide="file-text" class="w-6 h-6"></i>
                    </div>
                    <span class="text-xs bg-red-600 px-2 py-1 rounded">-2.1%</span>
                </div>
                <h3 class="text-2xl font-bold mb-1">45,892</h3>
                <p class="text-gray-400 text-sm">内容发布量</p>
                <div class="flex items-center mt-2">
                    <i data-lucide="trending-down" class="w-4 h-4 trend-down mr-1"></i>
                    <span class="text-sm trend-down">较上月下降 2.1%</span>
                </div>
            </div>
            
            <div class="metric-card p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-yellow-600 p-3 rounded-lg">
                        <i data-lucide="store" class="w-6 h-6"></i>
                    </div>
                    <span class="text-xs bg-green-600 px-2 py-1 rounded">+15.7%</span>
                </div>
                <h3 class="text-2xl font-bold mb-1">1,234</h3>
                <p class="text-gray-400 text-sm">认证商家</p>
                <div class="flex items-center mt-2">
                    <i data-lucide="trending-up" class="w-4 h-4 trend-up mr-1"></i>
                    <span class="text-sm trend-up">较上月增长 15.7%</span>
                </div>
            </div>
        </div>

        <!-- 图表分析区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 用户增长趋势 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">用户增长趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-600 rounded text-xs">新增</button>
                        <button class="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-xs transition-colors">活跃</button>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="userGrowthChart"></canvas>
                </div>
            </div>
            
            <!-- 内容分布 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">内容分布统计</h3>
                    <button class="text-blue-400 hover:text-blue-300 text-sm">
                        <i data-lucide="external-link" class="w-4 h-4 inline mr-1"></i>
                        详细分析
                    </button>
                </div>
                <div class="h-64">
                    <canvas id="contentDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 四大板块数据分析 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">四大板块数据分析</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="bg-blue-600 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="store" class="w-6 h-6"></i>
                    </div>
                    <h4 class="font-semibold mb-2">商铺板块</h4>
                    <p class="text-2xl font-bold text-blue-400 mb-1">12,456</p>
                    <p class="text-sm text-gray-400">发布数量</p>
                    <div class="flex items-center justify-center mt-2">
                        <i data-lucide="trending-up" class="w-4 h-4 text-green-400 mr-1"></i>
                        <span class="text-xs text-green-400">+5.2%</span>
                    </div>
                </div>
                
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="bg-green-600 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="info" class="w-6 h-6"></i>
                    </div>
                    <h4 class="font-semibold mb-2">信息板块</h4>
                    <p class="text-2xl font-bold text-green-400 mb-1">8,923</p>
                    <p class="text-sm text-gray-400">发布数量</p>
                    <div class="flex items-center justify-center mt-2">
                        <i data-lucide="trending-up" class="w-4 h-4 text-green-400 mr-1"></i>
                        <span class="text-xs text-green-400">+3.8%</span>
                    </div>
                </div>
                
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="bg-purple-600 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="car" class="w-6 h-6"></i>
                    </div>
                    <h4 class="font-semibold mb-2">出行板块</h4>
                    <p class="text-2xl font-bold text-purple-400 mb-1">15,678</p>
                    <p class="text-sm text-gray-400">发布数量</p>
                    <div class="flex items-center justify-center mt-2">
                        <i data-lucide="trending-down" class="w-4 h-4 text-red-400 mr-1"></i>
                        <span class="text-xs text-red-400">-1.2%</span>
                    </div>
                </div>
                
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="bg-yellow-600 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="package" class="w-6 h-6"></i>
                    </div>
                    <h4 class="font-semibold mb-2">跑腿板块</h4>
                    <p class="text-2xl font-bold text-yellow-400 mb-1">9,835</p>
                    <p class="text-sm text-gray-400">发布数量</p>
                    <div class="flex items-center justify-center mt-2">
                        <i data-lucide="trending-up" class="w-4 h-4 text-green-400 mr-1"></i>
                        <span class="text-xs text-green-400">+8.9%</span>
                    </div>
                </div>
            </div>
            
            <div class="h-80">
                <canvas id="sectionComparisonChart"></canvas>
            </div>
        </div>

        <!-- 用户行为分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 用户活跃度热力图 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-4">用户活跃度热力图</h3>
                <div class="grid grid-cols-7 gap-1 mb-4">
                    <div class="text-xs text-gray-400 text-center py-2">周一</div>
                    <div class="text-xs text-gray-400 text-center py-2">周二</div>
                    <div class="text-xs text-gray-400 text-center py-2">周三</div>
                    <div class="text-xs text-gray-400 text-center py-2">周四</div>
                    <div class="text-xs text-gray-400 text-center py-2">周五</div>
                    <div class="text-xs text-gray-400 text-center py-2">周六</div>
                    <div class="text-xs text-gray-400 text-center py-2">周日</div>
                </div>
                
                <div class="grid grid-cols-7 gap-1" id="heatmapGrid">
                    <!-- 热力图数据将通过JavaScript生成 -->
                </div>
                
                <div class="flex items-center justify-between mt-4 text-xs text-gray-400">
                    <span>低活跃</span>
                    <div class="flex space-x-1">
                        <div class="w-3 h-3 bg-gray-700 rounded"></div>
                        <div class="w-3 h-3 bg-blue-800 rounded"></div>
                        <div class="w-3 h-3 bg-blue-600 rounded"></div>
                        <div class="w-3 h-3 bg-blue-400 rounded"></div>
                        <div class="w-3 h-3 bg-blue-300 rounded"></div>
                    </div>
                    <span>高活跃</span>
                </div>
            </div>
            
            <!-- 地域分布 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-4">用户地域分布</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span class="text-sm">北京市</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <span class="text-sm text-gray-400 w-12 text-right">3,245</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-green-500 rounded"></div>
                            <span class="text-sm">上海市</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 72%"></div>
                            </div>
                            <span class="text-sm text-gray-400 w-12 text-right">2,876</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-purple-500 rounded"></div>
                            <span class="text-sm">广州市</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 65%"></div>
                            </div>
                            <span class="text-sm text-gray-400 w-12 text-right">2,456</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                            <span class="text-sm">深圳市</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 58%"></div>
                            </div>
                            <span class="text-sm text-gray-400 w-12 text-right">2,123</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-red-500 rounded"></div>
                            <span class="text-sm">杭州市</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                            <span class="text-sm text-gray-400 w-12 text-right">1,789</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-gray-500 rounded"></div>
                            <span class="text-sm">其他城市</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-gray-500 h-2 rounded-full" style="width: 35%"></div>
                            </div>
                            <span class="text-sm text-gray-400 w-12 text-right">2,745</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时数据监控 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">实时数据监控</h3>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm text-gray-400">实时更新</span>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold text-blue-400" id="onlineUsers">1,234</p>
                    <p class="text-sm text-gray-400">在线用户</p>
                </div>
                
                <div class="text-center">
                    <p class="text-2xl font-bold text-green-400" id="todayPosts">456</p>
                    <p class="text-sm text-gray-400">今日发布</p>
                </div>
                
                <div class="text-center">
                    <p class="text-2xl font-bold text-purple-400" id="todayViews">12,345</p>
                    <p class="text-sm text-gray-400">今日浏览</p>
                </div>
                
                <div class="text-center">
                    <p class="text-2xl font-bold text-yellow-400" id="newUsers">89</p>
                    <p class="text-sm text-gray-400">新增用户</p>
                </div>
                
                <div class="text-center">
                    <p class="text-2xl font-bold text-red-400" id="matchingSuccess">567</p>
                    <p class="text-sm text-gray-400">成功撮合</p>
                </div>
                
                <div class="text-center">
                    <p class="text-2xl font-bold text-indigo-400" id="systemLoad">65%</p>
                    <p class="text-sm text-gray-400">系统负载</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 用户增长趋势图
        const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
        new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [{
                    label: '新增用户',
                    data: [1200, 1900, 3000, 5000, 2000, 3000, 4500, 3200, 4100, 3800, 4200, 4800],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e5e7eb'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: '#374151'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: '#374151'
                        }
                    }
                }
            }
        });

        // 内容分布饼图
        const contentDistCtx = document.getElementById('contentDistributionChart').getContext('2d');
        new Chart(contentDistCtx, {
            type: 'doughnut',
            data: {
                labels: ['商铺', '信息', '出行', '跑腿'],
                datasets: [{
                    data: [12456, 8923, 15678, 9835],
                    backgroundColor: [
                        '#3b82f6',
                        '#10b981',
                        '#8b5cf6',
                        '#f59e0b'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#e5e7eb',
                            padding: 20
                        }
                    }
                }
            }
        });

        // 四大板块对比图
        const sectionCompCtx = document.getElementById('sectionComparisonChart').getContext('2d');
        new Chart(sectionCompCtx, {
            type: 'bar',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '商铺',
                        data: [2100, 2300, 2200, 2400, 2350, 2456],
                        backgroundColor: '#3b82f6'
                    },
                    {
                        label: '信息',
                        data: [1800, 1900, 1850, 1950, 1900, 1923],
                        backgroundColor: '#10b981'
                    },
                    {
                        label: '出行',
                        data: [2800, 3000, 2900, 3100, 3050, 3167],
                        backgroundColor: '#8b5cf6'
                    },
                    {
                        label: '跑腿',
                        data: [1600, 1700, 1650, 1750, 1800, 1835],
                        backgroundColor: '#f59e0b'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e5e7eb'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: '#374151'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: '#374151'
                        }
                    }
                }
            }
        });

        // 生成热力图
        function generateHeatmap() {
            const heatmapGrid = document.getElementById('heatmapGrid');
            const colors = ['bg-gray-700', 'bg-blue-800', 'bg-blue-600', 'bg-blue-400', 'bg-blue-300'];
            
            for (let week = 0; week < 12; week++) {
                for (let day = 0; day < 7; day++) {
                    const intensity = Math.floor(Math.random() * 5);
                    const cell = document.createElement('div');
                    cell.className = `w-4 h-4 rounded ${colors[intensity]} cursor-pointer hover:opacity-80 transition-opacity`;
                    cell.title = `活跃度: ${intensity + 1}/5`;
                    heatmapGrid.appendChild(cell);
                }
            }
        }

        // 实时数据更新
        function updateRealTimeData() {
            const elements = {
                onlineUsers: document.getElementById('onlineUsers'),
                todayPosts: document.getElementById('todayPosts'),
                todayViews: document.getElementById('todayViews'),
                newUsers: document.getElementById('newUsers'),
                reportedContent: document.getElementById('reportedContent'),
                systemLoad: document.getElementById('systemLoad')
            };
            
            setInterval(() => {
                // 模拟实时数据变化
                const currentOnline = parseInt(elements.onlineUsers.textContent.replace(',', ''));
                const change = Math.floor(Math.random() * 21) - 10; // -10 到 +10 的随机变化
                const newValue = Math.max(0, currentOnline + change);
                elements.onlineUsers.textContent = newValue.toLocaleString();
                
                // 更新系统负载
                const currentLoad = parseInt(elements.systemLoad.textContent.replace('%', ''));
                const loadChange = Math.floor(Math.random() * 11) - 5; // -5 到 +5 的随机变化
                const newLoad = Math.max(0, Math.min(100, currentLoad + loadChange));
                elements.systemLoad.textContent = newLoad + '%';
                
                // 根据负载调整颜色
                if (newLoad > 80) {
                    elements.systemLoad.className = 'text-2xl font-bold text-red-400';
                } else if (newLoad > 60) {
                    elements.systemLoad.className = 'text-2xl font-bold text-yellow-400';
                } else {
                    elements.systemLoad.className = 'text-2xl font-bold text-green-400';
                }
            }, 3000); // 每3秒更新一次
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateHeatmap();
            updateRealTimeData();
        });
    </script>
</body>
</html>