<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的商铺产品 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .product-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
            border-color: rgba(99, 102, 241, 0.3);
        }
        .action-btn {
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            transform: scale(1.05);
        }
        .category-tag {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
        }
        .status-online {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .status-offline {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        .product-card.expanded {
            border-color: rgba(99, 102, 241, 0.5);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.25);
            transform: translateY(-4px);
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white"></i>
            </button>
            <h1 class="text-xl font-semibold text-white">我的商铺产品</h1>
            <div class="flex-1"></div>
            <button onclick="window.location.href='shops.html'" class="p-2 hover:bg-white/20 rounded-lg transition-colors" title="返回商铺列表">
                <i data-lucide="store" class="w-6 h-6 text-white"></i>
            </button>
            <button onclick="window.location.href='shop-detail.html'" class="p-2 hover:bg-white/20 rounded-lg transition-colors" title="预览商铺">
                <i data-lucide="eye" class="w-6 h-6 text-white"></i>
            </button>
            <button onclick="showCategoryManager()" class="p-2 hover:bg-white/20 rounded-lg transition-colors" title="分类管理">
                <i data-lucide="folder" class="w-6 h-6 text-white"></i>
            </button>
        </div>
    </div>

    <!-- 商铺信息概览 -->
    <div class="max-w-md mx-auto p-4">
        <div class="bg-slate-800 rounded-xl p-4 mb-4">
            <div class="flex items-center space-x-3">
                <img src="https://images.unsplash.com/photo-1559925393-8be0ec4767c8?w=60&h=60&fit=crop&crop=face" 
                     alt="商铺头像" class="w-12 h-12 rounded-lg">
                <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1559925393-8be0ec4767c8 -->
                <div class="flex-1">
                    <h2 class="font-semibold text-white">星巴克咖啡</h2>
                    <div class="flex items-center space-x-2 text-sm">
                        <span class="status-online text-white px-2 py-1 rounded-full text-xs">营业中</span>
                        <span class="text-slate-400">产品总数: 12</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类筛选和操作栏 -->
    <div class="max-w-md mx-auto px-4 mb-4">
        <div class="bg-slate-800 rounded-xl p-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="font-medium text-white">产品分类</h3>
                <button onclick="addNewCategory()" class="text-blue-400 hover:text-blue-300 text-sm">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-1"></i>新增分类
                </button>
            </div>
            <div class="flex flex-wrap gap-2" id="categoryFilter">
                <button class="category-tag text-white px-3 py-1 rounded-full text-sm font-medium" data-category="all">全部 (12)</button>
                <button class="bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-sm hover:bg-slate-600 transition-colors" data-category="coffee">咖啡 (8)</button>
                <button class="bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-sm hover:bg-slate-600 transition-colors" data-category="food">轻食 (3)</button>
                <button class="bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-sm hover:bg-slate-600 transition-colors" data-category="dessert">甜品 (1)</button>
            </div>
        </div>
    </div>

    <!-- 快速操作栏 -->
    <div class="max-w-md mx-auto px-4 mb-4">
        <div class="flex space-x-3">
            <button onclick="addNewProduct()" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all">
                <i data-lucide="plus" class="w-5 h-5 inline mr-2"></i>添加产品
            </button>
            <button onclick="batchManage()" class="bg-slate-700 text-slate-300 px-4 py-3 rounded-xl hover:bg-slate-600 transition-colors">
                <i data-lucide="check-square" class="w-5 h-5"></i>
            </button>
            <button onclick="sortProducts()" class="bg-slate-700 text-slate-300 px-4 py-3 rounded-xl hover:bg-slate-600 transition-colors">
                <i data-lucide="arrow-up-down" class="w-5 h-5"></i>
            </button>
        </div>
    </div>

    <!-- 产品列表 -->
    <div class="max-w-md mx-auto px-4 pb-24" id="productList">
        <!-- 产品卡片1 -->
        <div class="product-card bg-slate-800 rounded-xl p-4 mb-3" data-category="coffee">
            <div class="flex space-x-3">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=80&h=80&fit=crop" 
                         alt="产品图片" class="w-20 h-20 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1560472354-b33ff0c44a43 -->
                    <span class="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">上架</span>
                </div>
                <div class="flex-1">
                    <div class="flex items-start justify-between">
                        <div>
                            <h3 class="font-medium text-white mb-1">精品手工咖啡豆</h3>
                            <p class="text-sm text-slate-400 mb-2">埃塞俄比亚耶加雪菲</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-blue-400 font-semibold">¥128</span>
                                    <span class="text-xs text-slate-500 line-through">¥168</span>
                                </div>
                                <span class="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full">咖啡</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-1">
                            <button onclick="editProduct('1')" class="action-btn p-1.5 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-white"></i>
                            </button>
                            
                            <button onclick="deleteProduct('1')" class="action-btn p-1.5 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-white"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-2 text-xs text-slate-400">
                        <span>浏览: 156次</span>
                        <span>更新: 2024-01-15</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品卡片2 -->
        <div class="product-card bg-slate-800 rounded-xl p-4 mb-3" data-category="food">
            <div class="flex space-x-3">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=80&h=80&fit=crop" 
                         alt="产品图片" class="w-20 h-20 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1546833999-b9f581a1996d -->
                    <span class="absolute -top-1 -right-1 bg-gray-500 text-white text-xs px-1.5 py-0.5 rounded-full">下架</span>
                </div>
                <div class="flex-1">
                    <div class="flex items-start justify-between">
                        <div>
                            <h3 class="font-medium text-white mb-1">经典牛角包</h3>
                            <p class="text-sm text-slate-400 mb-2">法式传统工艺制作</p>
                            <div class="flex items-center justify-between">
                                <span class="text-blue-400 font-semibold">¥18</span>
                                <span class="bg-green-600 text-white text-xs px-2 py-0.5 rounded-full">轻食</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-1">
                            <button onclick="editProduct('2')" class="action-btn p-1.5 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-white"></i>
                            </button>
                            
                            <button onclick="deleteProduct('2')" class="action-btn p-1.5 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-white"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-2 text-xs text-slate-400">
                        <span>浏览: 89次</span>
                        <span>更新: 2024-01-12</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品卡片3 -->
        <div class="product-card bg-slate-800 rounded-xl p-4 mb-3" data-category="coffee">
            <div class="flex space-x-3">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=80&h=80&fit=crop" 
                         alt="产品图片" class="w-20 h-20 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1509042239860-f550ce710b93 -->
                    <span class="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">上架</span>
                </div>
                <div class="flex-1">
                    <div class="flex items-start justify-between">
                        <div>
                            <h3 class="font-medium text-white mb-1">美式咖啡</h3>
                            <p class="text-sm text-slate-400 mb-2">经典黑咖啡，浓郁香醇</p>
                            <div class="flex items-center justify-between">
                                <span class="text-blue-400 font-semibold">¥25</span>
                                <span class="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full">咖啡</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-1">
                            <button onclick="editProduct('3')" class="action-btn p-1.5 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-white"></i>
                            </button>
                            
                            <button onclick="deleteProduct('3')" class="action-btn p-1.5 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-white"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-2 text-xs text-slate-400">
                        <span>浏览: 234次</span>
                        <span>更新: 2024-01-14</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态提示 -->
        <div id="emptyState" class="text-center py-12 hidden">
            <div class="w-20 h-20 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="package" class="w-10 h-10 text-slate-400"></i>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">暂无产品</h3>
            <p class="text-slate-400 mb-6">快来添加您的第一个产品吧</p>
            <button onclick="addNewProduct()" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all">
                <i data-lucide="plus" class="w-5 h-5 inline mr-2"></i>添加产品
            </button>
        </div>
    </div>

    <!-- 分类管理弹窗 -->
    <div id="categoryModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-xl p-6 w-full max-w-sm">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">分类管理</h3>
                    <button onclick="closeCategoryModal()" class="p-1 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                <div class="space-y-3 mb-4">
                    <div class="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                        <span class="text-white">咖啡</span>
                        <div class="flex space-x-2">
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-slate-300"></i>
                            </button>
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-red-400"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                        <span class="text-white">轻食</span>
                        <div class="flex space-x-2">
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-slate-300"></i>
                            </button>
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-red-400"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                        <span class="text-white">甜品</span>
                        <div class="flex space-x-2">
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-slate-300"></i>
                            </button>
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-red-400"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button onclick="addNewCategory()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-2"></i>添加新分类
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布各类信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿需求</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 分类筛选功能
        function filterByCategory(category) {
            const products = document.querySelectorAll('.product-card');
            const buttons = document.querySelectorAll('[data-category]');
            
            // 更新按钮状态
            buttons.forEach(btn => {
                if (btn.dataset.category === category) {
                    btn.className = 'category-tag text-white px-3 py-1 rounded-full text-sm font-medium';
                } else {
                    btn.className = 'bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-sm hover:bg-slate-600 transition-colors';
                }
            });
            
            // 筛选产品
            products.forEach(product => {
                if (category === 'all' || product.dataset.category === category) {
                    product.style.display = 'block';
                } else {
                    product.style.display = 'none';
                }
            });
        }

        // 绑定分类筛选事件
        document.addEventListener('DOMContentLoaded', function() {
            const categoryButtons = document.querySelectorAll('[data-category]');
            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterByCategory(this.dataset.category);
                });
            });
        });

        // 产品卡片点击效果
        document.addEventListener('DOMContentLoaded', function() {
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.addEventListener('click', function(event) {
                    // 防止点击卡片内部按钮时触发
                    if (event.target.closest('button')) {
                        return;
                    }

                    // 检查当前卡片是否已经是 expanded 状态
                    const isExpanded = card.classList.contains('expanded');

                    // 移除所有卡片的 'expanded' 状态
                    productCards.forEach(c => {
                        c.classList.remove('expanded');
                    });

                    // 如果当前卡片之前不是 expanded, 则添加
                    if (!isExpanded) {
                        card.classList.add('expanded');
                    }
                });
            });
        });

        // 添加新产品
        function addNewProduct() {
            window.location.href = 'edit-product.html';
        }

        // 切换产品菜单
        function toggleProductMenu(productId) {
            // 显示更多操作菜单
            const actions = [
                '复制产品',
                '设为推荐',
                '上架/下架',
                '查看数据'
            ];
            
            const action = prompt('选择操作:\n1. 复制产品\n2. 设为推荐\n3. 上架/下架\n4. 查看数据\n\n请输入数字:');
            
            switch(action) {
                case '1':
                    alert('产品已复制');
                    break;
                case '2':
                    alert('已设为推荐产品');
                    break;
                case '3':
                    alert('产品状态已切换');
                    break;
                case '4':
                    alert('跳转到产品数据页面');
                    break;
                default:
                    break;
            }
        }

        // 编辑产品
        function editProduct(productId) {
            console.log('编辑产品:', productId);
            window.location.href = 'edit-product.html?id=' + productId;
        }

        // 切换产品状态（上架/下架）
        function toggleProductStatus(productId) {
            console.log('切换产品状态:', productId);
            const product = document.querySelector(`[data-category]:nth-child(${productId})`);
            const statusBadge = product.querySelector('.absolute span');
            const toggleBtn = product.querySelector('[onclick*="toggleProductStatus"] i');
            
            if (statusBadge.textContent === '上架') {
                statusBadge.textContent = '下架';
                statusBadge.className = 'absolute -top-1 -right-1 bg-gray-500 text-white text-xs px-1.5 py-0.5 rounded-full';
                toggleBtn.setAttribute('data-lucide', 'eye');
            } else {
                statusBadge.textContent = '上架';
                statusBadge.className = 'absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full';
                toggleBtn.setAttribute('data-lucide', 'eye-off');
            }
            lucide.createIcons();
        }

        // 删除产品
        function deleteProduct(productId) {
            if (confirm('确定要删除这个产品吗？删除后无法恢复。')) {
                console.log('删除产品:', productId);
                // 实际应用中调用删除API
                alert('产品已删除');
                // 刷新页面或移除DOM元素
                location.reload();
            }
        }

        // 批量管理
        function batchManage() {
            console.log('批量管理');
            alert('进入批量管理模式');
        }

        // 排序产品
        function sortProducts() {
            console.log('排序产品');
            alert('选择排序方式：时间、价格、浏览量');
        }

        // 显示分类管理弹窗
        function showCategoryManager() {
            document.getElementById('categoryModal').classList.remove('hidden');
        }

        // 关闭分类管理弹窗
        function closeCategoryModal() {
            document.getElementById('categoryModal').classList.add('hidden');
        }

        // 添加新分类
        function addNewCategory() {
            const categoryName = prompt('请输入新分类名称:');
            if (categoryName && categoryName.trim()) {
                const newCategory = categoryName.trim();
                console.log('添加新分类:', newCategory);

                // 添加到筛选区域
                const categoryFilterContainer = document.getElementById('categoryFilter');
                const newFilterButton = document.createElement('button');
                newFilterButton.className = 'bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-sm hover:bg-slate-600 transition-colors';
                newFilterButton.dataset.category = newCategory.toLowerCase();
                newFilterButton.textContent = `${newCategory} (0)`;
                newFilterButton.addEventListener('click', function() {
                    filterByCategory(this.dataset.category);
                });
                categoryFilterContainer.appendChild(newFilterButton);

                // 添加到分类管理弹窗
                const categoryModalContainer = document.querySelector('#categoryModal .space-y-3');
                const newCategoryItem = document.createElement('div');
                newCategoryItem.className = 'flex items-center justify-between p-3 bg-slate-700 rounded-lg';
                newCategoryItem.innerHTML = `
                        <span class="text-white">${newCategory}</span>
                        <div class="flex space-x-2">
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="edit" class="w-4 h-4 text-slate-300"></i>
                            </button>
                            <button class="p-1 hover:bg-slate-600 rounded transition-colors">
                                <i data-lucide="trash-2" class="w-4 h-4 text-red-400"></i>
                            </button>
                        </div>
                `;
                categoryModalContainer.appendChild(newCategoryItem);
                
                lucide.createIcons();

                alert(`分类 "${newCategory}" 已添加`);
            }
        }

        // 全局函数
        // 发布弹窗功能
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }

        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }

        function navigateToPublish(type) {
            closePublishModal();
            switch(type) {
                case 'shops':
                    window.location.href = 'publish-shop.html';
                    break;
                case 'information':
                    window.location.href = 'publish-information.html';
                    break;
                case 'transportation':
                    window.location.href = 'publish-transportation.html';
                    break;
                case 'errands':
                    window.location.href = 'publish-errands.html';
                    break;
            }
        }

        window.filterByCategory = filterByCategory;
        window.addNewProduct = addNewProduct;
        window.editProduct = editProduct;
        window.toggleProductStatus = toggleProductStatus;
        window.deleteProduct = deleteProduct;
        window.batchManage = batchManage;
        window.sortProducts = sortProducts;
        window.showCategoryManager = showCategoryManager;
        window.closeCategoryModal = closeCategoryModal;
        window.addNewCategory = addNewCategory;
        window.toggleProductMenu = toggleProductMenu;
        window.showPublishModal = showPublishModal;
        window.closePublishModal = closePublishModal;
        window.navigateToPublish = navigateToPublish;
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>