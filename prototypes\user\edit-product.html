<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑产品 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .form-input {
            transition: all 0.3s ease;
        }
        .form-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .upload-area {
            border: 2px dashed #475569;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        .image-preview {
            position: relative;
            overflow: hidden;
        }
        .image-preview:hover .delete-btn {
            opacity: 1;
        }
        .delete-btn {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white"></i>
            </button>
            <h1 class="text-xl font-semibold text-white">编辑产品</h1>
            <div class="flex-1"></div>
            <button onclick="previewProduct()" class="p-2 hover:bg-white/20 rounded-lg transition-colors" title="预览">
                <i data-lucide="eye" class="w-6 h-6 text-white"></i>
            </button>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-md mx-auto p-4 pb-24">
        <form id="productForm" class="space-y-6">
            <!-- 产品图片 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">产品图片 *</label>
                
                <!-- 已上传图片预览 -->
                <div class="grid grid-cols-3 gap-3 mb-4" id="imagePreview">
                    <div class="image-preview relative">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop" 
                             alt="产品图片" class="w-full h-20 object-cover rounded-lg">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1560472354-b33ff0c44a43 -->
                        <button type="button" class="delete-btn absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600" onclick="removeImage(this)">
                            <i data-lucide="x" class="w-3 h-3"></i>
                        </button>
                    </div>
                    <div class="image-preview relative">
                        <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=100&h=100&fit=crop" 
                             alt="产品图片" class="w-full h-20 object-cover rounded-lg">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1509042239860-f550ce710b93 -->
                        <button type="button" class="delete-btn absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600" onclick="removeImage(this)">
                            <i data-lucide="x" class="w-3 h-3"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 上传区域 -->
                <div class="upload-area rounded-lg p-6 text-center cursor-pointer" onclick="document.getElementById('imageInput').click()">
                    <i data-lucide="camera" class="w-8 h-8 text-slate-400 mx-auto mb-2"></i>
                    <p class="text-sm text-slate-400">点击添加图片</p>
                    <p class="text-xs text-slate-500 mt-1">最多9张，建议尺寸1:1</p>
                </div>
                <input type="file" id="imageInput" accept="image/*" multiple class="hidden" onchange="handleImageUpload(event)">
            </div>

            <!-- 产品名称 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">产品名称 *</label>
                <input type="text" name="productName" value="精品手工咖啡豆" placeholder="请输入产品名称" 
                       class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
            </div>

            <!-- 产品分类 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">产品分类 *</label>
                <select name="category" class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none">
                    <option value="">请选择分类</option>
                    <option value="coffee" selected>咖啡</option>
                    <option value="food">轻食</option>
                    <option value="dessert">甜品</option>
                    <option value="drink">饮品</option>
                </select>
                <button type="button" onclick="addNewCategory()" class="mt-2 text-blue-400 hover:text-blue-300 text-sm">
                    <i data-lucide="plus" class="w-4 h-4 inline mr-1"></i>添加新分类
                </button>
            </div>

            <!-- 产品描述 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">产品描述</label>
                <textarea name="description" rows="4" placeholder="请输入产品描述" 
                          class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none">埃塞俄比亚耶加雪菲，口感醇厚，香气浓郁，精选优质咖啡豆，手工烘焙，品质保证。</textarea>
                <div class="flex justify-between items-center mt-2">
                    <span class="text-xs text-slate-500">0/500字</span>
                    <button type="button" onclick="aiOptimize()" class="text-blue-400 hover:text-blue-300 text-xs">
                        <i data-lucide="sparkles" class="w-3 h-3 inline mr-1"></i>AI优化
                    </button>
                </div>
            </div>

            <!-- 价格设置 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">价格设置 *</label>
                
                <!-- 价格类型选择 -->
                <div class="flex items-center space-x-6 mb-4">
                    <div class="flex items-center space-x-2">
                        <input type="radio" name="priceType" value="fixed" id="fixedPrice" checked class="text-blue-500 focus:ring-blue-500">
                        <label for="fixedPrice" class="text-sm text-white">固定价格</label>
                    </div>
                    <div class="flex items-center space-x-2">
                        <input type="radio" name="priceType" value="negotiable" id="negotiablePrice" class="text-blue-500 focus:ring-blue-500">
                        <label for="negotiablePrice" class="text-sm text-white">面议</label>
                    </div>
                </div>
                
                <!-- 价格输入区域 -->
                <div id="priceInputs" class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-xs text-slate-400 mb-1">现价 *</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">¥</span>
                            <input type="number" name="currentPrice" value="128" placeholder="0.00" 
                                   class="form-input w-full pl-8 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <label class="block text-xs text-slate-400 mb-1">原价（可选）</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">¥</span>
                            <input type="number" name="originalPrice" value="168" placeholder="0.00" 
                                   class="form-input w-full pl-8 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">联系方式 *</label>
                <div class="space-y-4">
                    <div>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                                <i data-lucide="phone" class="w-5 h-5 text-slate-400"></i>
                            </div>
                            <input type="tel" id="contact-phone" name="phone" placeholder="请输入手机号码"
                                   class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                                <i data-lucide="message-square" class="w-5 h-5 text-slate-400"></i>
                            </div>
                            <input type="text" id="sms-code" name="smsCode" placeholder="请输入短信验证码"
                                   class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                            <button type="button" id="send-sms-btn" class="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors flex items-center justify-center font-semibold whitespace-nowrap">
                                发送验证码
                            </button>
                        </div>
                    </div>
                </div>
            </div>





            <!-- 操作按钮 -->
            <div class="flex space-x-3 pt-4">
                <button type="button" onclick="saveDraft()" class="flex-1 bg-slate-700 text-slate-300 py-3 rounded-xl font-medium hover:bg-slate-600 transition-colors">
                    保存草稿
                </button>
                <button type="submit" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all">
                    保存并发布
                </button>
            </div>
        </form>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布各类信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿需求</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 处理图片上传
        function handleImageUpload(event) {
            const files = event.target.files;
            const previewContainer = document.getElementById('imagePreview');
            
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'image-preview relative';
                        imageDiv.innerHTML = `
                            <img src="${e.target.result}" alt="产品图片" class="w-full h-20 object-cover rounded-lg">
                            <button type="button" class="delete-btn absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600" onclick="removeImage(this)">
                                <i data-lucide="x" class="w-3 h-3"></i>
                            </button>
                        `;
                        previewContainer.appendChild(imageDiv);
                        // 图标将在页面初始化时统一加载
                    };
                    reader.readAsDataURL(file);
                }
            }
        }

        // 删除图片
        function removeImage(button) {
            button.parentElement.remove();
        }

        // 添加标签
        function addTag() {
            const input = document.getElementById('tagInput');
            const tagName = input.value.trim();
            
            if (tagName) {
                addPresetTag(tagName);
                input.value = '';
            }
        }

        // 添加预设标签
        function addPresetTag(tagName) {
            const container = document.getElementById('tagContainer');
            
            // 检查是否已存在
            const existingTags = container.querySelectorAll('span');
            for (let tag of existingTags) {
                if (tag.textContent.trim().replace('×', '').trim() === tagName) {
                    return;
                }
            }
            
            const tagElement = document.createElement('span');
            tagElement.className = 'bg-blue-500 text-white px-2 py-1 rounded-full text-xs flex items-center';
            tagElement.innerHTML = `
                ${tagName}
                <button type="button" onclick="removeTag(this)" class="ml-1 hover:bg-blue-600 rounded-full w-4 h-4 flex items-center justify-center">
                    <i data-lucide="x" class="w-2 h-2"></i>
                </button>
            `;
            container.appendChild(tagElement);
            // 图标将在页面初始化时统一加载
        }

        // 删除标签
        function removeTag(button) {
            button.parentElement.remove();
        }

        // 添加新分类
        function addNewCategory() {
            const categoryName = prompt('请输入新分类名称:');
            if (categoryName && categoryName.trim()) {
                const select = document.querySelector('select[name="category"]');
                const option = document.createElement('option');
                option.value = categoryName.toLowerCase().replace(/\s+/g, '-');
                option.textContent = categoryName;
                option.selected = true;
                select.appendChild(option);
                alert(`分类 "${categoryName}" 已添加`);
            }
        }

        // AI优化描述
        function aiOptimize() {
            const textarea = document.querySelector('textarea[name="description"]');
            const currentText = textarea.value;
            
            // 模拟AI优化
            setTimeout(() => {
                textarea.value = currentText + '\n\n【AI优化建议】\n• 突出产品独特卖点\n• 增加感官描述\n• 强调品质保证';
                alert('AI优化建议已添加到描述中');
            }, 1000);
        }

        // 预览产品
        function previewProduct() {
            alert('跳转到产品预览页面');
            // 实际应用中跳转到预览页面
        }

        // 保存草稿
        function saveDraft() {
            console.log('保存草稿');
            alert('草稿已保存');
        }

        // 处理价格类型切换
        document.addEventListener('DOMContentLoaded', function() {
            const priceRadios = document.querySelectorAll('input[name="priceType"]');
            const priceInputs = document.getElementById('priceInputs');
            
            priceRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'negotiable') {
                        priceInputs.style.display = 'none';
                    } else {
                        priceInputs.style.display = 'block';
                    }
                });
            });
        });

        // 表单提交
        document.getElementById('productForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证必填字段
            const productName = document.querySelector('input[name="productName"]').value;
            const category = document.querySelector('select[name="category"]').value;
            
            if (!productName.trim()) {
                alert('请输入产品名称');
                return;
            }
            
            if (!category) {
                alert('请选择产品分类');
                return;
            }
            
            // 模拟保存
            console.log('保存产品信息');
            alert('产品信息已保存并发布');
            
            // 实际应用中跳转回产品列表
            // window.location.href = 'my-shop-products.html';
        });

        // 发送验证码功能
        document.getElementById('send-sms-btn').addEventListener('click', function() {
            const phoneInput = document.querySelector('input[name="phone"]');
            const phone = phoneInput.value.trim();
            const btn = this;
            
            // 验证手机号
            if (!phone) {
                alert('请输入手机号码');
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                return;
            }
            
            // 禁用按钮并开始倒计时
            btn.disabled = true;
            let countdown = 60;
            
            const timer = setInterval(() => {
                btn.textContent = `${countdown}s后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }, 1000);
            
            // 模拟发送验证码
            console.log('发送验证码到:', phone);
            alert('验证码已发送到您的手机');
        });

        // 字符计数
        document.querySelector('textarea[name="description"]').addEventListener('input', function() {
            const count = this.value.length;
            const counter = this.parentElement.querySelector('.text-xs');
            counter.textContent = `${count}/500字`;
            
            if (count > 500) {
                counter.classList.add('text-red-400');
            } else {
                counter.classList.remove('text-red-400');
            }
        });

        // 发布弹窗功能
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }

        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }

        function navigateToPublish(type) {
            closePublishModal();
            switch(type) {
                case 'shops':
                    window.location.href = 'publish-shop.html';
                    break;
                case 'information':
                    window.location.href = 'publish-information.html';
                    break;
                case 'transportation':
                    window.location.href = 'publish-transportation.html';
                    break;
                case 'errands':
                    window.location.href = 'publish-errands.html';
                    break;
            }
        }

        // 全局函数
        window.handleImageUpload = handleImageUpload;
        window.removeImage = removeImage;
        window.addTag = addTag;
        window.addPresetTag = addPresetTag;
        window.removeTag = removeTag;
        window.addNewCategory = addNewCategory;
        window.aiOptimize = aiOptimize;
        window.previewProduct = previewProduct;
        window.saveDraft = saveDraft;
        window.showPublishModal = showPublishModal;
        window.closePublishModal = closePublishModal;
        window.navigateToPublish = navigateToPublish;
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>