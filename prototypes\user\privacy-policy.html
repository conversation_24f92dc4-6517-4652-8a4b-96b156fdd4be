<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            min-height: 100vh;
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .tech-card {
            background: rgba(30, 30, 63, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .tech-gradient {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        }
        .section-title {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="text-slate-200">
    <!-- 顶部导航栏 -->
    <div class="tech-card border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
            <button onclick="goBack()" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                <span>返回</span>
            </button>
            <h1 class="text-lg font-semibold text-white">隐私政策</h1>
            <div class="w-16"></div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-md mx-auto p-4 space-y-6">
        <!-- 更新时间 -->
        <div class="tech-card rounded-xl p-4">
            <div class="flex items-center justify-between">
                <span class="text-slate-400">最后更新时间</span>
                <span class="text-blue-400 font-medium">2024年1月15日</span>
            </div>
        </div>

        <!-- 政策概述 -->
        <div class="tech-card rounded-xl p-6">
            <h2 class="section-title text-xl font-bold mb-4">政策概述</h2>
            <p class="text-slate-300 leading-relaxed">
                本地助手非常重视您的隐私保护。本隐私政策详细说明了我们如何收集、使用、存储和保护您的个人信息。请您仔细阅读并理解本政策的全部内容。
            </p>
        </div>

        <!-- 信息收集 -->
        <div class="tech-card rounded-xl p-6">
            <h2 class="section-title text-xl font-bold mb-4">信息收集</h2>
            <div class="space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="text-white font-semibold mb-2">基本信息</h3>
                    <p class="text-slate-300 text-sm leading-relaxed">
                        包括您的微信授权信息、手机号码、昵称、头像等基本身份信息。
                    </p>
                </div>
                <div class="border-l-4 border-purple-500 pl-4">
                    <h3 class="text-white font-semibold mb-2">位置信息</h3>
                    <p class="text-slate-300 text-sm leading-relaxed">
                        为了提供本地化服务，我们会收集您的地理位置信息，用于推荐附近的商家和服务。
                    </p>
                </div>
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="text-white font-semibold mb-2">使用信息</h3>
                    <p class="text-slate-300 text-sm leading-relaxed">
                        包括您的浏览记录、搜索历史、发布内容、评价信息等使用行为数据。
                    </p>
                </div>
            </div>
        </div>

        <!-- 信息使用 -->
        <div class="tech-card rounded-xl p-6">
            <h2 class="section-title text-xl font-bold mb-4">信息使用</h2>
            <div class="grid grid-cols-1 gap-4">
                <div class="flex items-start space-x-3">
                    <div class="tech-gradient w-6 h-6 rounded-full flex items-center justify-center mt-1">
                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                    </div>
                    <div>
                        <h4 class="text-white font-medium">服务提供</h4>
                        <p class="text-slate-400 text-sm">为您提供个性化的本地信息和服务推荐</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="tech-gradient w-6 h-6 rounded-full flex items-center justify-center mt-1">
                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                    </div>
                    <div>
                        <h4 class="text-white font-medium">身份验证</h4>
                        <p class="text-slate-400 text-sm">验证您的身份，确保账户安全</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="tech-gradient w-6 h-6 rounded-full flex items-center justify-center mt-1">
                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                    </div>
                    <div>
                        <h4 class="text-white font-medium">服务改进</h4>
                        <p class="text-slate-400 text-sm">分析使用数据，优化产品功能和用户体验</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="tech-gradient w-6 h-6 rounded-full flex items-center justify-center mt-1">
                        <i data-lucide="check" class="w-3 h-3 text-white"></i>
                    </div>
                    <div>
                        <h4 class="text-white font-medium">安全保障</h4>
                        <p class="text-slate-400 text-sm">检测和防范欺诈、滥用等安全风险</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息保护 -->
        <div class="tech-card rounded-xl p-6">
            <h2 class="section-title text-xl font-bold mb-4">信息保护</h2>
            <div class="space-y-4">
                <div class="bg-slate-800 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i data-lucide="shield-check" class="w-5 h-5 text-green-400 mr-2"></i>
                        <span class="text-white font-medium">技术保护</span>
                    </div>
                    <p class="text-slate-300 text-sm">
                        采用行业标准的加密技术和安全措施保护您的个人信息。
                    </p>
                </div>
                <div class="bg-slate-800 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i data-lucide="lock" class="w-5 h-5 text-blue-400 mr-2"></i>
                        <span class="text-white font-medium">访问控制</span>
                    </div>
                    <p class="text-slate-300 text-sm">
                        严格限制员工对用户信息的访问权限，建立完善的内部管理制度。
                    </p>
                </div>
                <div class="bg-slate-800 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <i data-lucide="database" class="w-5 h-5 text-purple-400 mr-2"></i>
                        <span class="text-white font-medium">数据存储</span>
                    </div>
                    <p class="text-slate-300 text-sm">
                        在安全的服务器环境中存储数据，定期备份和安全检查。
                    </p>
                </div>
            </div>
        </div>

        <!-- 您的权利 -->
        <div class="tech-card rounded-xl p-6">
            <h2 class="section-title text-xl font-bold mb-4">您的权利</h2>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div class="flex items-center">
                        <i data-lucide="eye" class="w-5 h-5 text-blue-400 mr-3"></i>
                        <span class="text-white">查看个人信息</span>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4 text-slate-400"></i>
                </div>
                <div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div class="flex items-center">
                        <i data-lucide="edit" class="w-5 h-5 text-green-400 mr-3"></i>
                        <span class="text-white">修改个人信息</span>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4 text-slate-400"></i>
                </div>
                <div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div class="flex items-center">
                        <i data-lucide="trash-2" class="w-5 h-5 text-red-400 mr-3"></i>
                        <span class="text-white">删除个人信息</span>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4 text-slate-400"></i>
                </div>
                <div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div class="flex items-center">
                        <i data-lucide="download" class="w-5 h-5 text-purple-400 mr-3"></i>
                        <span class="text-white">导出个人信息</span>
                    </div>
                    <i data-lucide="chevron-right" class="w-4 h-4 text-slate-400"></i>
                </div>
            </div>
        </div>

        <!-- 联系我们 -->
        <div class="tech-card rounded-xl p-6">
            <h2 class="section-title text-xl font-bold mb-4">联系我们</h2>
            <div class="space-y-4">
                <p class="text-slate-300 leading-relaxed">
                    如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：
                </p>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <i data-lucide="mail" class="w-5 h-5 text-blue-400 mr-3"></i>
                        <span class="text-white">邮箱：<EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="phone" class="w-5 h-5 text-green-400 mr-3"></i>
                        <span class="text-white">客服热线：************</span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="map-pin" class="w-5 h-5 text-purple-400 mr-3"></i>
                        <span class="text-white">地址：北京市朝阳区科技园区</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 同意按钮 -->
        <div class="tech-card rounded-xl p-6">
            <button onclick="acceptPolicy()" class="w-full tech-gradient text-white font-semibold py-3 px-6 rounded-lg hover:opacity-90 transition-opacity">
                我已阅读并同意隐私政策
            </button>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 同意隐私政策
        function acceptPolicy() {
            // 这里可以添加同意逻辑，比如设置本地存储标记
            localStorage.setItem('privacyPolicyAccepted', 'true');
            
            // 显示成功提示
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '已同意';
            button.classList.add('bg-green-600');
            button.classList.remove('tech-gradient');
            
            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('bg-green-600');
                button.classList.add('tech-gradient');
                goBack();
            }, 1500);
        }

        // 页面加载完成后检查是否已同意
        document.addEventListener('DOMContentLoaded', function() {
            const accepted = localStorage.getItem('privacyPolicyAccepted');
            if (accepted === 'true') {
                const button = document.querySelector('button[onclick="acceptPolicy()"]');
                button.innerHTML = '<i data-lucide="check" class="w-5 h-5 mr-2"></i>已同意隐私政策';
                button.classList.add('bg-green-600');
                button.classList.remove('tech-gradient');
                lucide.createIcons();
            }
        });
    </script>
</body>
</html>