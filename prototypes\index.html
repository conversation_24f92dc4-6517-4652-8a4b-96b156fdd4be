<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地助手 - 高保真原型展示系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="styles/design-system.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#3b82f6',
                        'tech-purple': '#8b5cf6',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
        }
        .interface-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.2);
            height: fit-content;
        }
        .interface-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
            border-color: rgba(99, 102, 241, 0.5);
        }
        .grid-masonry {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            align-items: start;
        }
        @media (max-width: 768px) {
            .grid-masonry {
                grid-template-columns: 1fr;
            }
        }
        /* 用户端界面预览样式 - 网格布局 */
        .user-interface-preview {
            height: 600px;
            overflow: hidden;
        }
        .user-interface-preview iframe {
            transform: scale(0.7);
            transform-origin: top left;
            width: 143%;
            height: 143%;
        }
        
        /* 管理端界面预览样式 - 大页面展示 */
        .admin-interface-preview {
            height: 600px;
            overflow: hidden;
        }
        .admin-interface-preview iframe {
            transform: scale(0.8);
            transform-origin: top left;
            width: 125%;
            height: 125%;
        }
        
        /* 管理端布局 - 非网格，大页面展示 */
        .admin-layout {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        
        .admin-interface-card {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech">
    <!-- 顶部导航 -->
    <nav class="tech-gradient p-4 sticky top-0 z-50 backdrop-blur-md">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-tech-blue font-bold text-xl">本</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-white">本地助手原型系统</h1>
                    <p class="text-sm text-blue-100 opacity-90">高保真界面展示 · 微信小程序平台</p>
                </div>
            </div>
            <div class="flex items-center space-x-6">
                <div class="text-sm text-blue-100 opacity-90">
                    <i data-lucide="layers" class="w-4 h-4 inline mr-1"></i>
                    科技感设计风格
                </div>
                <div class="text-sm text-blue-100 opacity-90">
                    <i data-lucide="users" class="w-4 h-4 inline mr-1"></i>
                    信息撮合平台
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto p-6 pb-24">
        <!-- 产品概述与统计 -->
        <div class="grid lg:grid-cols-3 gap-8 mb-12">
            <!-- 产品概述 -->
            <div class="lg:col-span-2 bg-dark-card rounded-xl p-8 tech-glow border border-gray-700">
                <div class="flex items-center mb-6">
                    <i data-lucide="info" class="w-6 h-6 text-tech-cyan mr-3"></i>
                    <h2 class="text-2xl font-bold text-tech-cyan">产品概述</h2>
                </div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-semibold mb-2 text-white flex items-center">
                                <i data-lucide="target" class="w-4 h-4 mr-2 text-tech-blue"></i>
                                核心定位
                            </h3>
                            <p class="text-gray-300 text-sm leading-relaxed">本地信息撮合平台，连接用户与本地商家服务</p>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2 text-white flex items-center">
                                <i data-lucide="grid-3x3" class="w-4 h-4 mr-2 text-tech-purple"></i>
                                四大板块
                            </h3>
                            <p class="text-gray-300 text-sm leading-relaxed">商铺、信息、出行、跑腿服务</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-semibold mb-2 text-white flex items-center">
                                <i data-lucide="smartphone" class="w-4 h-4 mr-2 text-tech-cyan"></i>
                                技术平台
                            </h3>
                            <p class="text-gray-300 text-sm leading-relaxed">微信小程序原生开发</p>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2 text-white flex items-center">
                                <i data-lucide="palette" class="w-4 h-4 mr-2 text-green-400"></i>
                                设计理念
                            </h3>
                            <p class="text-gray-300 text-sm leading-relaxed">科技感、简洁高效、易用性优先</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计概览 -->
            <div class="space-y-4">
                <div class="bg-dark-card rounded-xl p-6 border border-gray-700 text-center hover:border-tech-blue transition-colors">
                    <div class="text-3xl font-bold text-tech-blue mb-2">35+</div>
                    <div class="text-sm text-gray-400">用户端界面</div>
                    <div class="text-xs text-gray-500 mt-1">完整用户体验流程</div>
                </div>
                <div class="bg-dark-card rounded-xl p-6 border border-gray-700 text-center hover:border-tech-purple transition-colors">
                    <div class="text-3xl font-bold text-tech-purple mb-2">12+</div>
                    <div class="text-sm text-gray-400">管理端界面</div>
                    <div class="text-xs text-gray-500 mt-1">后台管理系统</div>
                </div>
                <div class="bg-dark-card rounded-xl p-6 border border-gray-700 text-center hover:border-tech-cyan transition-colors">
                    <div class="text-3xl font-bold text-tech-cyan mb-2">4</div>
                    <div class="text-sm text-gray-400">核心业务板块</div>
                    <div class="text-xs text-gray-500 mt-1">商铺·信息·出行·跑腿</div>
                </div>
            </div>
        </div>

        <!-- 界面分类展示 -->
        <div class="space-y-12">
            <!-- 用户端界面 -->
            <section>
                <h2 class="text-2xl font-bold mb-8 text-tech-purple flex items-center">
                    <span class="w-8 h-8 bg-tech-purple rounded-lg flex items-center justify-center mr-3 text-sm">用</span>
                    用户端界面
                </h2>
                
                <!-- 商铺相关界面 -->
                <div class="mb-12">
                    <h3 class="text-xl font-semibold mb-6 text-tech-cyan flex items-center">
                        <span class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-md flex items-center justify-center mr-3 text-xs">店</span>
                        商铺相关界面
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                        <!-- 商铺板块 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/shops.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">商铺板块</h3>
                                <p class="text-sm text-gray-400 mb-2">本地商家信息展示，支持分类筛选和搜索</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/shops.html</code>
                            </div>
                        </div>

                        <!-- 发布商铺信息 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/publish-shop.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">发布商铺信息</h3>
                                <p class="text-sm text-gray-400 mb-2">商家入驻、店铺信息发布表单</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/publish-shop.html</code>
                            </div>
                        </div>

                        <!-- 商家详情 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/shop-detail.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">商家详情</h3>
                                <p class="text-sm text-gray-400 mb-2">商家信息展示、产品列表、联系方式等</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/shop-detail.html</code>
                            </div>
                        </div>

                        <!-- 我的商铺产品 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/my-shop-products.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">我的商铺产品</h3>
                                <p class="text-sm text-gray-400 mb-2">商铺产品管理界面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/my-shop-products.html</code>
                            </div>
                        </div>

                        <!-- 编辑产品 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/edit-product.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">编辑产品</h3>
                                <p class="text-sm text-gray-400 mb-2">产品信息编辑界面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/edit-product.html</code>
                            </div>
                        </div>

                        <!-- 产品详情 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/product-detail.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">产品详情</h3>
                                <p class="text-sm text-gray-400 mb-2">商铺产品详细信息展示</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/product-detail.html</code>
                            </div>
                        </div>

                        <!-- 团购专区 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/group_buy.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">团购专区</h3>
                                <p class="text-sm text-gray-400 mb-2">团购活动展示和参与页面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/group_buy.html</code>
                            </div>
                        </div>

                        <!-- 发布团购 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/publish_group_buy.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">发布团购</h3>
                                <p class="text-sm text-gray-400 mb-2">创建和发布团购活动页面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/publish_group_buy.html</code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 出行相关界面 -->
                <div class="mb-12">
                    <h3 class="text-xl font-semibold mb-6 text-tech-cyan flex items-center">
                        <span class="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-md flex items-center justify-center mr-3 text-xs">行</span>
                        出行相关界面
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                        <!-- 出行板块 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/transportation.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">出行板块</h3>
                                <p class="text-sm text-gray-400 mb-2">拼车、代驾等出行服务信息</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/transportation.html</code>
                            </div>
                        </div>

                        <!-- 发布出行信息 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/publish-transportation.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">发布出行信息</h3>
                                <p class="text-sm text-gray-400 mb-2">拼车、代驾等出行服务发布表单</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/publish-transportation.html</code>
                            </div>
                        </div>

                        <!-- 出行详情 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/transportation-detail.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">出行详情</h3>
                                <p class="text-sm text-gray-400 mb-2">拼车、代驾等出行服务详细信息</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/transportation-detail.html</code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 信息相关界面 -->
                <div class="mb-12">
                    <h3 class="text-xl font-semibold mb-6 text-tech-cyan flex items-center">
                        <span class="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-md flex items-center justify-center mr-3 text-xs">信</span>
                        信息相关界面
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                        <!-- 信息板块 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/information.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">信息板块</h3>
                                <p class="text-sm text-gray-400 mb-2">招聘、租房、二手等本地信息发布与查看</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/information.html</code>
                            </div>
                        </div>

                        <!-- 发布信息 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/publish-information.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">发布信息</h3>
                                <p class="text-sm text-gray-400 mb-2">招聘、租房、二手等信息发布表单</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/publish-information.html</code>
                            </div>
                        </div>

                        <!-- 信息详情 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/information-detail.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">信息详情</h3>
                                <p class="text-sm text-gray-400">招聘、租房、二手等信息详细页面</p>
                            </div>
                        </div>

                        <!-- 我的信息 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/my-information.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">我的信息</h3>
                                <p class="text-sm text-gray-400 mb-2">我发布的信息管理</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/my-information.html</code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 跑腿相关界面 -->
                <div class="mb-12">
                    <h3 class="text-xl font-semibold mb-6 text-tech-cyan flex items-center">
                        <span class="w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-md flex items-center justify-center mr-3 text-xs">跑</span>
                        跑腿相关界面
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                        <!-- 跑腿板块 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/errands.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">跑腿板块</h3>
                                <p class="text-sm text-gray-400 mb-2">代买、代送、代办等跑腿服务</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/errands.html</code>
                            </div>
                        </div>

                        <!-- 发布跑腿信息 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/publish-errands.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">发布跑腿信息</h3>
                                <p class="text-sm text-gray-400 mb-2">代买、代送、代办等跑腿服务发布</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/publish-errands.html</code>
                            </div>
                        </div>

                        <!-- 跑腿详情 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/errands-detail.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">跑腿详情</h3>
                                <p class="text-sm text-gray-400 mb-2">跑腿任务详细信息、进度跟踪</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/errands-detail.html</code>
                            </div>
                        </div>

                        <!-- 我的跑腿 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/my-errands.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">我的跑腿</h3>
                                <p class="text-sm text-gray-400 mb-2">我发布的跑腿服务管理</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/my-errands.html</code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他功能界面 -->
                <div class="mb-12">
                    <h3 class="text-xl font-semibold mb-6 text-tech-cyan flex items-center">
                        <span class="w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-md flex items-center justify-center mr-3 text-xs">其</span>
                        其他功能界面
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                        <!-- 登录界面 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/login.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">登录界面</h3>
                                <p class="text-sm text-gray-400 mb-2">支持微信授权登录和手机验证码登录</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/login.html</code>
                            </div>
                        </div>

                        <!-- 发布中心 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/publish-select.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">发布中心</h3>
                                <p class="text-sm text-gray-400 mb-2">统一发布入口，包含草稿箱、发布历史和快速发布功能</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/publish-select.html</code>
                            </div>
                        </div>
                    </div>





                    <!-- 其他功能界面 -->
                    <h4 class="text-lg font-semibold mb-4 text-gray-400 border-l-4 border-gray-400 pl-3">⚙️ 其他功能</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <!-- 个人中心 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/profile.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">个人中心</h3>
                                <p class="text-sm text-gray-400 mb-2">用户信息管理、我的发布、认证等功能</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/profile.html</code>
                            </div>
                        </div>

                        <!-- 消息中心 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/message.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">消息中心</h3>
                                <p class="text-sm text-gray-400 mb-2">系统通知、互动消息、私信管理</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/message.html</code>
                            </div>
                        </div>

                        <!-- 实名认证 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/verification.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">实名认证</h3>
                                <p class="text-sm text-gray-400 mb-2">身份证上传、人脸识别等认证流程</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/verification.html</code>
                            </div>
                        </div>

                        <!-- 我的优惠券 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/coupons.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">我的优惠券</h3>
                                <p class="text-sm text-gray-400 mb-2">优惠券的管理、核销等功能</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/coupons.html</code>
                            </div>
                        </div>

                        <!-- 用户评价 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/reviews.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">用户评价</h3>
                                <p class="text-sm text-gray-400 mb-2">对商家/服务的评价功能</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/reviews.html</code>
                            </div>
                        </div>

                        <!-- 举报功能 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/report.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">举报功能</h3>
                                <p class="text-sm text-gray-400 mb-2">举报不当内容的功能</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/report.html</code>
                            </div>
                        </div>

                        <!-- 收藏管理 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/favorites-management.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">收藏管理</h3>
                                <p class="text-sm text-gray-400 mb-2">收藏的商铺、信息管理</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/favorites-management.html</code>
                            </div>
                        </div>

                        <!-- 扫码服务功能 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/qr-login.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">扫码服务功能</h3>
                                <p class="text-sm text-gray-400 mb-2">扫描平台生成的呼叫服务二维码界面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/qr-login.html</code>
                            </div>
                        </div>

                        <!-- 设置中心 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/settings.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">设置中心</h3>
                                <p class="text-sm text-gray-400 mb-2">通知设置、隐私设置、发布设置、服务设置等统一管理</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/settings.html</code>
                            </div>
                        </div>

                        <!-- 优惠券管理 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/coupons-management.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">优惠券管理</h3>
                                <p class="text-sm text-gray-400 mb-2">优惠券查看、使用、获取</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/coupons-management.html</code>
                            </div>
                        </div>

                        <!-- 订单管理 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/orders-management.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">订单管理</h3>
                                <p class="text-sm text-gray-400 mb-2">订单查看、跟踪、评价</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/orders-management.html</code>
                            </div>
                        </div>

                        <!-- 地址管理 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/address-management.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">地址管理</h3>
                                <p class="text-sm text-gray-400 mb-2">收货地址、常用地址管理</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/address-management.html</code>
                            </div>
                        </div>

                        <!-- 我的呼叫服务 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/my-call-service.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">我的呼叫服务</h3>
                                <p class="text-sm text-gray-400 mb-2">呼叫服务管理界面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/my-call-service.html</code>
                            </div>
                        </div>

                        <!-- 扫码呼叫服务 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/call-service.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">扫码呼叫服务</h3>
                                <p class="text-sm text-gray-400 mb-2">用户扫码后的呼叫界面</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/call-service.html</code>
                            </div>
                        </div>

                        <!-- 隐私政策 -->
                        <div class="interface-card bg-dark-card rounded-xl overflow-hidden group">
                            <div class="user-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                                <iframe src="user/privacy-policy.html" class="w-full h-full border-0"></iframe>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-tech-cyan">隐私政策</h3>
                                <p class="text-sm text-gray-400 mb-2">隐私政策条款展示</p>
                                <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">user/privacy-policy.html</code>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 管理端界面 -->
            <section>
                <h2 class="text-2xl font-bold mb-8 text-tech-blue flex items-center">
                    <span class="w-8 h-8 bg-tech-blue rounded-lg flex items-center justify-center mr-3 text-sm">管</span>
                    管理端界面 (PC端大页面展示)
                </h2>
                <div class="admin-layout">
                    <!-- 管理员登录 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/login.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">管理员登录</h3>
                            <p class="text-gray-400 mb-2">管理员专用登录界面，支持多重身份验证</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/login.html</code>
                        </div>
                    </div>

                    <!-- 管理仪表盘 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/dashboard.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">管理仪表盘</h3>
                            <p class="text-gray-400 mb-2">数据概览、关键指标监控、实时统计分析</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/dashboard.html</code>
                        </div>
                    </div>

                    <!-- 用户管理 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/user-management.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">用户管理</h3>
                            <p class="text-gray-400 mb-2">用户信息管理、状态控制、权限分配</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/user-management.html</code>
                        </div>
                    </div>

                    <!-- 呼叫服务数据统计 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/call-service-stats.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">呼叫服务数据统计</h3>
                            <p class="text-gray-400 mb-2">呼叫服务数据分析界面（仅平台管理端可见）</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/call-service-stats.html</code>
                        </div>
                    </div>

                    <!-- 内容审核 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/review.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">内容审核</h3>
                            <p class="text-gray-400 mb-2">待审核内容管理、审核流程、批量操作</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/review.html</code>
                        </div>
                    </div>

                    <!-- 举报处理 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/reports.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">举报处理</h3>
                            <p class="text-gray-400 mb-2">用户举报内容的处理和管理、违规处罚</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/reports.html</code>
                        </div>
                    </div>
                    
                    <!-- 广告管理 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/ad-management.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">广告管理</h3>
                            <p class="text-gray-400 mb-2">广告位管理、内容管理、链接管理、效果统计</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/ad-management.html</code>
                        </div>
                    </div>
                    
                    <!-- 内容管理 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/content-management.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">内容管理</h3>
                            <p class="text-gray-400 mb-2">平台内容的统一管理和维护</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/content-management.html</code>
                        </div>
                    </div>

                    <!-- 内容监控 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/content-monitoring.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">内容监控</h3>
                            <p class="text-gray-400 mb-2">实时监控平台内容质量和安全</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/content-monitoring.html</code>
                        </div>
                    </div>

                    <!-- 数据分析 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/data-analytics.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">数据分析</h3>
                            <p class="text-gray-400 mb-2">平台运营数据深度分析和报表</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/data-analytics.html</code>
                        </div>
                    </div>

                    <!-- 商家认领 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/merchant-claim.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">商家认领</h3>
                            <p class="text-gray-400 mb-2">商家认领申请的审核和管理</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/merchant-claim.html</code>
                        </div>
                    </div>

                    <!-- 系统设置 -->
                    <div class="admin-interface-card interface-card bg-dark-card rounded-xl overflow-hidden group">
                        <div class="admin-interface-preview rounded-lg border border-gray-600 overflow-hidden">
                            <iframe src="admin/settings.html" class="w-full h-full border-0"></iframe>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2 text-tech-cyan">系统设置</h3>
                            <p class="text-gray-400 mb-2">平台基础配置、功能开关管理、参数调优</p>
                            <code class="text-xs text-purple-400 bg-purple-900/50 px-2 py-1 rounded">admin/settings.html</code>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- 底部信息 -->
        <footer class="mt-16 border-t border-gray-700 pt-12">
            <div class="grid md:grid-cols-3 gap-8 mb-8">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="code" class="w-5 h-5 mr-2 text-tech-blue"></i>
                        技术栈
                    </h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>HTML5 + Tailwind CSS</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Lucide Icons</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>响应式设计</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>微信小程序适配</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="palette" class="w-5 h-5 mr-2 text-tech-purple"></i>
                        设计原则
                    </h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li class="flex items-center"><i data-lucide="star" class="w-4 h-4 mr-2 text-yellow-400"></i>高端大气上档次</li>
                        <li class="flex items-center"><i data-lucide="star" class="w-4 h-4 mr-2 text-yellow-400"></i>简洁明了易上手</li>
                        <li class="flex items-center"><i data-lucide="star" class="w-4 h-4 mr-2 text-yellow-400"></i>视觉舒适耐看</li>
                        <li class="flex items-center"><i data-lucide="star" class="w-4 h-4 mr-2 text-yellow-400"></i>护眼配色系统</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-tech-cyan"></i>
                        项目信息
                    </h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li class="flex items-center"><i data-lucide="calendar" class="w-4 h-4 mr-2 text-blue-400"></i>基于PRD文档设计</li>
                        <li class="flex items-center"><i data-lucide="users" class="w-4 h-4 mr-2 text-blue-400"></i>多角色界面覆盖</li>
                        <li class="flex items-center"><i data-lucide="layers" class="w-4 h-4 mr-2 text-blue-400"></i>完整业务流程</li>
                        <li class="flex items-center"><i data-lucide="zap" class="w-4 h-4 mr-2 text-blue-400"></i>高保真交互原型</li>
                    </ul>
                </div>
            </div>
            <div class="text-center text-gray-500 text-sm border-t border-gray-800 pt-6">
                <p class="mb-2">本地助手微信小程序 · 高保真原型展示系统</p>
                <p class="text-xs">© 2024 本地助手项目组 · 科技感设计风格 · 信息撮合平台</p>
            </div>
        </footer>
    </div>

    <script>
        // 页面交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Lucide 图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
            
            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.interface-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                    this.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1)';
                    this.style.borderColor = 'rgba(59, 130, 246, 0.6)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1)';
                    this.style.borderColor = 'rgba(59, 130, 246, 0.2)';
                });
            });
            
            // 添加统计卡片动画
            const statCards = document.querySelectorAll('.stat-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });
            
            statCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease-out';
                observer.observe(card);
            });
            
            // 添加平滑滚动
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // 添加页面加载完成提示
            console.log('🎨 本地助手原型展示系统已加载完成');
            console.log('📱 包含 47+ 个高保真界面原型');
            console.log('🚀 基于 HTML5 + Tailwind CSS + Lucide Icons 构建');
        });
        
        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K 快速搜索（预留功能）
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                console.log('快速搜索功能（待实现）');
            }
            
            // ESC 键返回顶部
            if (e.key === 'Escape') {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    </script>
</body>
</html>