<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑跑腿 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
        }
        .form-card {
            background: rgba(30, 30, 63, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .upload-area {
            border: 2px dashed rgba(59, 130, 246, 0.5);
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: rgba(59, 130, 246, 0.8);
            background: rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="text-white">
    <!-- 顶部导航 -->
    <div class="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <button onclick="goBack()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6"></i>
            </button>
            <h1 class="text-lg font-semibold">编辑跑腿</h1>
            <button onclick="saveErrand()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                保存
            </button>
        </div>
    </div>

    <!-- 表单内容 -->
    <div class="p-4 space-y-6">
        <!-- 基本信息 -->
        <div class="form-card rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i data-lucide="edit-3" class="w-5 h-5 mr-2 text-blue-400"></i>
                基本信息
            </h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">跑腿标题</label>
                    <input type="text" id="title" value="帮忙取快递" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">详细描述</label>
                    <textarea id="description" rows="4" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none" placeholder="请详细描述您的需求...">需要帮忙到菜鸟驿站取一个快递，取件码是1234，快递比较重，大概10公斤左右。</textarea>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">服务类型</label>
                        <select id="category" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-blue-500 focus:outline-none">
                            <option value="delivery">代取快递</option>
                            <option value="shopping">代买商品</option>
                            <option value="food">代买食物</option>
                            <option value="document">文件代办</option>
                            <option value="other">其他服务</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">紧急程度</label>
                        <select id="urgency" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-blue-500 focus:outline-none">
                            <option value="normal">普通</option>
                            <option value="urgent" selected>紧急</option>
                            <option value="very-urgent">非常紧急</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地址信息 -->
        <div class="form-card rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-green-400"></i>
                地址信息
            </h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">取件地址</label>
                    <div class="flex space-x-2">
                        <input type="text" id="pickup-address" value="北京市朝阳区望京SOHO T3菜鸟驿站" class="flex-1 bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        <button onclick="selectLocation('pickup')" class="bg-blue-600 hover:bg-blue-700 px-4 py-3 rounded-lg transition-colors">
                            <i data-lucide="map" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">送达地址</label>
                    <div class="flex space-x-2">
                        <input type="text" id="delivery-address" value="北京市朝阳区望京街道10号院2单元1001" class="flex-1 bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        <button onclick="selectLocation('delivery')" class="bg-blue-600 hover:bg-blue-700 px-4 py-3 rounded-lg transition-colors">
                            <i data-lucide="map" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">联系方式</label>
                    <input type="tel" id="contact" value="138****8888" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                </div>
            </div>
        </div>

        <!-- 时间和费用 -->
        <div class="form-card rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i data-lucide="clock" class="w-5 h-5 mr-2 text-orange-400"></i>
                时间和费用
            </h2>
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">期望完成时间</label>
                        <input type="datetime-local" id="deadline" value="2024-01-15T18:00" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-blue-500 focus:outline-none">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">服务费用</label>
                        <div class="relative">
                            <input type="number" id="price" value="25" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 pr-12 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                            <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400">元</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">备注信息</label>
                    <textarea id="notes" rows="3" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none" placeholder="其他需要说明的信息...">快递比较重，请注意安全。取件时需要出示身份证。</textarea>
                </div>
            </div>
        </div>

        <!-- 图片上传 -->
        <div class="form-card rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i data-lucide="image" class="w-5 h-5 mr-2 text-purple-400"></i>
                相关图片
            </h2>
            
            <div class="upload-area rounded-lg p-6 text-center">
                <i data-lucide="upload" class="w-12 h-12 text-slate-400 mx-auto mb-4"></i>
                <p class="text-slate-300 mb-2">点击上传或拖拽图片到此处</p>
                <p class="text-sm text-slate-400">支持 JPG、PNG 格式，最大 5MB</p>
                <input type="file" id="images" multiple accept="image/*" class="hidden">
                <button onclick="document.getElementById('images').click()" class="mt-4 bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg text-sm font-medium transition-colors">
                    选择图片
                </button>
            </div>
            
            <!-- 已上传图片预览 -->
            <div id="image-preview" class="mt-4 grid grid-cols-3 gap-3">
                <div class="relative group">
                    <img src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=200&h=200&fit=crop" alt="快递单" class="w-full h-24 object-cover rounded-lg">
                    <button onclick="removeImage(0)" class="absolute top-2 right-2 bg-red-600 hover:bg-red-700 w-6 h-6 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-4 pb-6">
            <button onclick="saveErrand()" class="flex-1 bg-blue-600 hover:bg-blue-700 py-3 rounded-lg font-medium transition-colors">
                保存修改
            </button>
            <button onclick="deleteErrand()" class="bg-red-600 hover:bg-red-700 px-6 py-3 rounded-lg font-medium transition-colors">
                删除
            </button>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        function goBack() {
            window.history.back();
        }

        function saveErrand() {
            const title = document.getElementById('title').value;
            const description = document.getElementById('description').value;
            const price = document.getElementById('price').value;
            
            if (!title.trim()) {
                alert('请输入跑腿标题');
                return;
            }
            
            if (!description.trim()) {
                alert('请输入详细描述');
                return;
            }
            
            if (!price || price <= 0) {
                alert('请输入有效的服务费用');
                return;
            }
            
            alert('跑腿信息已保存！');
            // 这里可以添加实际的保存逻辑
        }

        function deleteErrand() {
            if (confirm('确定要删除这个跑腿吗？\n\n删除后无法恢复！')) {
                alert('跑腿已删除');
                window.location.href = 'my-errands.html';
            }
        }

        function selectLocation(type) {
            alert(`正在打开地图选择${type === 'pickup' ? '取件' : '送达'}地址...`);
        }

        function removeImage(index) {
            if (confirm('确定要删除这张图片吗？')) {
                // 这里可以添加删除图片的逻辑
                alert('图片已删除');
            }
        }

        // 图片上传处理
        document.getElementById('images').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                alert(`已选择 ${files.length} 张图片，正在上传...`);
                // 这里可以添加实际的图片上传逻辑
            }
        });
    </script>
</body>
</html>