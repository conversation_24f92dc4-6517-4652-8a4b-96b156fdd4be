<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布跑腿信息 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .form-input {
            transition: all 0.3s ease;
        }
        .form-input:focus {
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        .upload-area {
            border: 2px dashed #475569;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #8b5cf6;
            background-color: rgba(139, 92, 246, 0.05);
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <div class="bg-gradient-to-r from-purple-500 to-violet-500 p-4">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white"></i>
            </button>
            <h1 class="text-xl font-semibold text-white">发布跑腿信息</h1>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-md mx-auto p-4 pb-24">
        <form id="errandForm" class="space-y-6">
            <!-- 跑腿类型选择 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">跑腿类型 *</label>
                <div class="flex flex-wrap gap-2">
                    <button type="button" class="type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap" data-type="buy">
                        代买
                    </button>
                    <button type="button" class="type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap" data-type="delivery">
                        代送
                    </button>
                    <button type="button" class="type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap" data-type="handle">
                        代办
                    </button>
                    <button type="button" class="type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap" data-type="other">
                        其他
                    </button>
                </div>
                <input type="hidden" name="errandType" value="">
            </div>

            <!-- 任务标题 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">任务标题 *</label>
                <input type="text" name="title" placeholder="请简要描述跑腿任务" 
                       class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none">
            </div>

            <!-- 详细描述 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">详细描述 *</label>
                <textarea name="description" rows="4" placeholder="请详细描述跑腿任务的具体要求、注意事项等" 
                          class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none resize-none"></textarea>
            </div>

            <!-- 地址信息 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">地址信息 *</label>
                <div class="space-y-3">
                    <div>
                        <label class="block text-xs text-slate-400 mb-1">取件/服务地址</label>
                        <div class="flex space-x-2">
                            <input type="text" name="pickupAddress" placeholder="请输入取件或服务地址" 
                                   class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none">
                            <button type="button" class="px-4 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                                <i data-lucide="map-pin" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div id="deliveryAddress" class="delivery-only">
                        <label class="block text-xs text-slate-400 mb-1">送达地址</label>
                        <div class="flex space-x-2">
                            <input type="text" name="deliveryAddress" placeholder="请输入送达地址" 
                                   class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none">
                            <button type="button" class="px-4 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                                <i data-lucide="map-pin" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 期望时间 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">期望时间（自定义） *</label>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-xs text-slate-400 mb-1">期望日期</label>
                        <input type="date" name="expectedDate" 
                               class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-purple-500 focus:outline-none">
                    </div>
                    <div>
                        <label class="block text-xs text-slate-400 mb-1">期望时间</label>
                        <input type="time" name="expectedTime" 
                               class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-purple-500 focus:outline-none">
                    </div>
                </div>
            </div>

            <!-- 信息有效期 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">信息有效期（自定义） *</label>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <input type="number" name="validHours" min="1" max="168" value="24" 
                               class="form-input w-24 px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-purple-500 focus:outline-none text-center">
                        <span class="text-sm text-slate-300">小时</span>
                        <span class="text-xs text-slate-400">（1-168小时，最长7天）</span>
                    </div>
                    <div class="bg-amber-500/10 border border-amber-500/20 rounded-lg p-3">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="clock" class="w-4 h-4 text-amber-400"></i>
                            <span class="text-xs text-amber-400">信息到期后将自动删除，请合理设置有效期</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 费用设置 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">费用设置 *</label>
                <div>
                    <label class="block text-xs text-slate-400 mb-1">跑腿费</label>
                    <div class="relative">
                        <input type="number" name="fee" placeholder="0" step="0.1" min="0" 
                               class="form-input w-full pl-4 pr-8 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none">
                        <span class="absolute right-3 top-3 text-slate-400 text-sm">元</span>
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <div class="space-y-4">
                    <div>
                        <label for="contact-phone" class="text-sm font-medium text-slate-300 mb-2 hidden">联系方式 *</label>
                        <div class="flex items-center space-x-3">
                                     <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                                         <i data-lucide="phone" class="w-5 h-5 text-slate-400"></i>
                                     </div>
                                     <input type="tel" id="contact-phone" name="phone" placeholder="请输入手机号码"
                                            class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none">
                                 </div>
                    </div>
                    <div>
                        <label for="sms-code" class="text-sm font-medium text-slate-300 mb-2 hidden">短信验证</label>
                        <div class="flex items-center space-x-3">
                                    <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                                        <i data-lucide="message-square" class="w-5 h-5 text-slate-400"></i>
                                    </div>
                                    <input type="text" id="sms-code" name="smsCode" placeholder="请输入短信验证码"
                                           class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-purple-500 focus:outline-none">
                                    <button type="button" id="send-sms-btn" class="px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-500 transition-colors flex items-center justify-center font-semibold whitespace-nowrap">
                                        发送验证码
                                    </button>
                                </div>
                    </div>
                </div>
            </div>

            <!-- 图片上传 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">相关图片</label>
                <div class="upload-area rounded-lg p-6 text-center cursor-pointer" onclick="document.getElementById('imageUpload').click()">
                    <i data-lucide="image-plus" class="w-12 h-12 text-slate-400 mx-auto mb-2"></i>
                    <p class="text-sm text-slate-400 mb-1">上传相关图片</p>
                    <p class="text-xs text-slate-500">如商品截图、位置图片等，最多3张</p>
                </div>
                <input type="file" id="imageUpload" multiple accept="image/*" class="hidden">
                
                <!-- 图片预览区域 -->
                <div id="imagePreview" class="grid grid-cols-3 gap-2 mt-3 hidden">
                    <!-- 动态生成图片预览 -->
                </div>
            </div>




        </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700 p-4">
        <!-- 发布按钮 -->
        <div class="max-w-md mx-auto flex space-x-3">
            <button type="button" class="flex-1 py-3 bg-slate-600 text-white rounded-lg font-medium hover:bg-slate-500 transition-colors">
                保存草稿
            </button>
            <button type="submit" form="errandForm" class="flex-1 py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors">
                发布跑腿
            </button>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
        
        // 定位按钮点击事件
        document.querySelectorAll('button [data-lucide="map-pin"]').forEach(button => {
            button.parentElement.addEventListener('click', function() {
                const input = this.previousElementSibling;
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        // 这里可以调用地图API获取地址
                        input.value = `当前位置 (${lat.toFixed(6)}, ${lng.toFixed(6)})`;
                        alert('定位成功！');
                    }, function(error) {
                        alert('定位失败，请手动输入地址');
                    });
                } else {
                    alert('您的浏览器不支持定位功能');
                }
            });
        });
        
        // 跑腿类型选择功能
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有按钮的选中状态
                document.querySelectorAll('.type-btn').forEach(b => {
                    b.classList.remove('bg-orange-600', 'text-white');
                    b.classList.add('bg-slate-700', 'text-slate-300');
                });
                
                // 设置当前按钮为选中状态
                this.classList.remove('bg-slate-700', 'text-slate-300');
                this.classList.add('bg-orange-600', 'text-white');
                
                // 更新隐藏的input值
                document.querySelector('input[name="errandType"]').value = this.dataset.type;
                
                // 根据类型显示/隐藏送达地址
                const deliveryAddress = document.getElementById('deliveryAddress');
                if (this.dataset.type === 'delivery') {
                    deliveryAddress.style.display = 'block';
                } else {
                    deliveryAddress.style.display = 'none';
                }
            });
        });
        
        // 设置默认日期为今天
        const today = new Date().toISOString().split('T')[0];
        document.querySelector('input[name="expectedDate"]').value = today;
        
        // 图片上传处理
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const files = e.target.files;
            const preview = document.getElementById('imagePreview');
            
            if (files.length > 0) {
                preview.classList.remove('hidden');
                preview.innerHTML = '';
                
                Array.from(files).slice(0, 3).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative aspect-square bg-slate-700 rounded-lg overflow-hidden';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-full object-cover">
                            <button type="button" class="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600" onclick="this.parentElement.remove()">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                });
            }
        });
        
        // 短信验证码发送
        function sendSmsCode() {
            const phoneInput = document.querySelector('[name="phone"]');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                alert('请先填写手机号码');
                phoneInput.focus();
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                phoneInput.focus();
                return;
            }
            
            const btn = document.getElementById('send-sms-btn');
            btn.disabled = true;
            btn.textContent = '发送中...';
            
            // 模拟发送验证码
            setTimeout(() => {
                let countdown = 60;
                const timer = setInterval(() => {
                    btn.textContent = `${countdown}s后重发`;
                    countdown--;
                    
                    if (countdown < 0) {
                        clearInterval(timer);
                        btn.disabled = false;
                        btn.textContent = '发送验证码';
                    }
                }, 1000);
                
                console.log('发送短信验证码到:', phone);
                alert('验证码已发送，请注意查收');
            }, 1000);
        }
        
        // 表单提交处理
        document.getElementById('errandForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证必填字段
            const requiredFields = ['title', 'description', 'pickupAddress', 'expectedDate', 'expectedTime', 'fee', 'phone', 'smsCode'];
            let isValid = true;
            
            requiredFields.forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (!input || !input.value.trim()) {
                    isValid = false;
                    if (input) input.classList.add('border-red-500');
                } else {
                    input.classList.remove('border-red-500');
                }
            });
            
            // 验证跑腿类型是否已选择
            const errandType = document.querySelector('input[name="errandType"]').value;
            if (!errandType) {
                isValid = false;
                alert('请选择跑腿类型');
                return;
            }
            if (errandType === 'delivery') {
                const deliveryAddr = document.querySelector('input[name="deliveryAddress"]');
                if (!deliveryAddr.value.trim()) {
                    isValid = false;
                    deliveryAddr.classList.add('border-red-500');
                }
            }
            
            // 短信验证码检查
            const smsCodeInput = document.querySelector('[name="smsCode"]');
            if (!smsCodeInput.value.trim()) {
                alert('请输入短信验证码');
                smsCodeInput.classList.add('border-red-500');
                return;
            }
            
            if (!/^\d{6}$/.test(smsCodeInput.value)) {
                alert('验证码应为6位数字');
                smsCodeInput.classList.add('border-red-500');
                return;
            }
            
            if (!isValid) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 模拟发布流程
            const submitButton = document.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = '发布中...';
            
            // 模拟API提交
            setTimeout(() => {
                // 收集表单数据
                const errandType = document.querySelector('input[name="errandType"]').value;
                const formData = {
                    errandType: errandType,
                    title: document.querySelector('[name="title"]').value,
                    description: document.querySelector('[name="description"]').value,
                    pickupAddress: document.querySelector('[name="pickupAddress"]').value,
                    expectedDate: document.querySelector('[name="expectedDate"]').value,
                    expectedTime: document.querySelector('[name="expectedTime"]').value,
                    fee: document.querySelector('[name="fee"]').value,
                    phone: document.querySelector('[name="phone"]').value,
                    // notes字段可能不存在，使用可选链操作符
                    notes: document.querySelector('[name="notes"]')?.value || '',
                    timestamp: new Date().toISOString()
                };
                
                // 添加送达地址（如果是代买代送）
                if (errandType === 'delivery') {
                    formData.deliveryAddress = document.querySelector('[name="deliveryAddress"]').value;
                }
                
                console.log('跑腿信息已提交:', formData);
                
                // 显示成功提示
                alert('跑腿信息发布成功！');
                
                // 跳转到跑腿列表页面
                window.location.href = 'errands.html';
            }, 1500);
        });
        
        // 发送验证码功能
        document.getElementById('send-sms-btn').addEventListener('click', function() {
            const btn = this;
            const phoneInput = document.getElementById('contact-phone');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                alert('请先输入手机号码');
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                return;
            }
            
            // 禁用按钮并开始倒计时
            btn.disabled = true;
            let countdown = 60;
            
            const timer = setInterval(() => {
                btn.textContent = `${countdown}s后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }, 1000);
            
            // 模拟发送验证码
            console.log('发送验证码到:', phone);
            alert('验证码已发送到您的手机，请注意查收');
        });
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>