<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出行板块 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/common.js"></script>
    <script src="../scripts/page-initializer.js"></script>
    <link rel="stylesheet" href="../styles/base-styles.css">
    <link rel="stylesheet" href="../styles/core.css">
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .card-hover {
            transition: background-color 0.2s ease;
        }
        .card-hover:hover {
            background-color: rgba(30, 41, 59, 0.8);
        }
        .transport-tag {
            background: linear-gradient(45deg, #06b6d4, #0891b2);
        }
        .urgent-tag {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }
        .price-tag {
            background: linear-gradient(45deg, #3b82f6, #1d4ed8);
        }
        
        /* 统一卡片样式，确保卡片在所有设备上正确显示 */
        .transportation-card {
            display: block;
            margin-bottom: 1rem;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            break-inside: avoid;
            page-break-inside: avoid;
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部搜索区域 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-40">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center space-x-3">
                <!-- 返回按钮 -->
                <button data-action="back" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                
                <!-- 搜索输入框 -->
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索出行服务..." 
                           class="w-full bg-slate-700 border-0 rounded-lg px-4 py-2 pl-10 pr-4 text-sm text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:bg-slate-600 transition-all">
                    <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                </div>
                
                <!-- 扫一扫按钮 -->
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" data-action="openQRScanner">
                    <i data-lucide="qr-code" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 四大板块导航 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-between py-3" role="navigation" aria-label="功能模块">
                <!-- 商铺 -->
                <button data-action="navigate:shops" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                
                <!-- 信息 -->
                <button data-action="navigate:information" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="file-text" class="w-5 h-5"></i>
                    <span class="text-xs">信息</span>
                </button>
                
                <!-- 出行 -->
                <button class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow">
                    <i data-lucide="car" class="w-5 h-5"></i>
                    <span class="text-xs">出行</span>
                </button>
                
                <!-- 跑腿 -->
                <button data-action="navigate:errands" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="package" class="w-5 h-5"></i>
                    <span class="text-xs">跑腿</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 出行分类筛选 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3">
            <!-- 基础分类筛选 -->
            <div class="flex space-x-[6px] mb-3">
                <button class="filter-btn flex-shrink-0 px-3 py-2 bg-orange-600 text-white rounded-full text-sm font-medium" data-filter="all">全部</button>
                <button class="filter-btn flex-shrink-0 px-2 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-filter="车找人">车找人</button>
                <button class="filter-btn flex-shrink-0 px-2 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-filter="人找车">人找车</button>
                <button class="filter-btn flex-shrink-0 px-2 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-filter="货找车">货找车</button>
                <button class="filter-btn flex-shrink-0 px-2 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-filter="车找货">车找货</button>
                <button class="filter-btn flex-shrink-0 px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-filter="代驾">代驾</button>
            </div>
            
            <!-- 线路类型和服务范围筛选 -->
            <div class="grid grid-cols-2 gap-3">
                <!-- 线路类型筛选 -->
                <div class="space-y-2">
                    <legend class="block text-sm font-medium text-slate-300 mb-3">线路类型</legend>
                    <div class="grid grid-cols-2 gap-2">
                        <div data-action="toggleRouteType" data-params='{"type":"flexible"}' id="flexible-route" class="relative cursor-pointer">
                            <div id="flexible-route-bg" class="flex items-center justify-center space-x-[6px] p-2 bg-orange-500/20 border-2 border-orange-500 rounded-lg transition-all hover:bg-orange-500/30">
                                <i id="flexible-route-icon" data-lucide="zap" class="w-3 h-3 text-orange-400"></i>
                                <span id="flexible-route-text" class="text-xs font-medium text-orange-400">非固定</span>
                            </div>
                            <div id="flexible-route-dot" class="absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        </div>
                        <div data-action="toggleRouteType" data-params='{"type":"fixed"}' id="fixed-route" class="relative cursor-pointer">
                            <div id="fixed-route-bg" class="flex items-center justify-center space-x-[6px] p-2 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all hover:bg-slate-500">
                                <i id="fixed-route-icon" data-lucide="repeat" class="w-3 h-3 text-slate-400"></i>
                                <span id="fixed-route-text" class="text-xs text-slate-400">固定线路</span>
                            </div>
                            <div id="fixed-route-dot" class="absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-transparent rounded-full"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 服务范围筛选 -->
                <div class="space-y-2">
                    <div class="text-xs text-slate-400 font-medium">服务范围</div>
                    <div class="grid grid-cols-2 gap-2">
                        <div data-action="toggleAreaType" data-params='{"type":"urban"}' id="urban-area" class="relative cursor-pointer">
                            <div id="urban-area-bg" class="flex items-center justify-center space-x-[6px] p-2 bg-green-500/20 border-2 border-green-500 rounded-lg transition-all hover:bg-green-500/30">
                                <i id="urban-area-icon" data-lucide="building" class="w-3 h-3 text-green-400"></i>
                                <span id="urban-area-text" class="text-xs font-medium text-green-400">城区内</span>
                            </div>
                            <div id="urban-area-dot" class="absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        </div>
                        <div data-action="toggleAreaType" data-params='{"type":"intercity"}' id="intercity-area" class="relative cursor-pointer">
                            <div id="intercity-area-bg" class="flex items-center justify-center space-x-[6px] p-2 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all hover:bg-slate-500">
                                <i id="intercity-area-icon" data-lucide="map" class="w-3 h-3 text-slate-400"></i>
                                <span id="intercity-area-text" class="text-xs text-slate-400">跨城区</span>
                            </div>
                            <div id="intercity-area-dot" class="absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-transparent rounded-full"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 出行服务列表 -->
    <div class="max-w-md mx-auto px-4 py-4 pb-20">
        <!-- 人找车卡片 -->
        <div class="transportation-card bg-slate-800 rounded-xl p-4 mb-4 card-hover border border-slate-700" data-category="人找车" data-route="flexible" data-area="urban">
            <!-- 标题和价格区域 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                <h3 class="font-bold text-white text-lg">罗湖 → 宝安机场</h3>
                    <span class="ml-2 text-sm text-slate-400 whitespace-nowrap">
                        <i data-lucide="clock" class="w-3 h-3 inline-block mr-1 align-text-bottom text-slate-400"></i>
                        明天 06:00
                    </span>
                </div>
                <span class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-2 py-1 rounded-lg text-sm font-bold">
                    每人80元
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-emerald-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="user-plus" class="w-3 h-3"></i>
                    <span>人找车</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="zap" class="w-3 h-3"></i>
                    <span>非固定</span>
                </span>
                <span class="text-teal-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="building" class="w-3 h-3"></i>
                    <span>城区内</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5 hover:text-blue-300 transition-colors">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    赶早班机，需要可靠的师傅。行李不多，两个人。可以提前一晚联系确认。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="space-y-2 text-sm text-slate-300">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-[6px]">
                        <i data-lucide="user-check" class="w-3 h-3 text-slate-400"></i>
                        <span>2人同行</span>
                    </div>
                    <span class="text-xs text-orange-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3"></i>
                        <span>3.2km</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 车找人卡片 -->
        <div class="transportation-card bg-slate-800 rounded-xl p-4 mb-4 card-hover border border-slate-700" data-category="车找人" data-route="fixed" data-area="urban">
            <!-- 标题和价格区域 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                <h3 class="font-bold text-white text-lg">南山 → 福田CBD</h3>
                    <span class="ml-2 text-sm text-slate-400 whitespace-nowrap">
                        <i data-lucide="clock" class="w-3 h-3 inline-block mr-1 align-text-bottom text-slate-400"></i>
                        10月15日 08:30
                    </span>
                </div>
                <span class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-2 py-1 rounded-lg text-sm font-bold">
                    每人25元
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-2">
                <span class="text-cyan-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="car" class="w-3 h-3"></i>
                    <span>车找人</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="repeat" class="w-3 h-3"></i>
                    <span>固定线路</span>
                </span>
                <span class="text-teal-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="building" class="w-3 h-3"></i>
                    <span>城区内</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5 hover:text-blue-300 transition-colors">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
            </div>
            
            <!-- 途径信息 -->
            <div class="flex items-center mb-3 text-sm text-slate-400">
                <i data-lucide="route" class="w-3 h-3 mr-1 text-slate-400"></i>
                <span>途径：科技园、车公庙、市民中心</span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    每天固定线路，上班族拼车。车况良好，驾龄8年，准时出发。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="space-y-2 text-sm text-slate-300">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-[6px]">
                        <i data-lucide="users" class="w-3 h-3 text-slate-400"></i>
                        <span>还差2人</span>
                    </div>
                    <span class="text-xs text-orange-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3"></i>
                        <span>1.8km</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 货找车卡片 -->
        <div class="transportation-card bg-slate-800 rounded-xl p-4 mb-4 card-hover border border-slate-700" data-category="货找车" data-route="flexible" data-area="intercity">
            
            <!-- 标题和价格区域 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                <h3 class="font-bold text-white text-lg">深圳 → 广州</h3>
                    <span class="ml-2 text-sm text-slate-400 whitespace-nowrap">
                        <i data-lucide="clock" class="w-3 h-3 inline-block mr-1 align-text-bottom text-slate-400"></i>
                        10月16日 14:00
                    </span>
                </div>
                <span class="bg-yellow-600 text-white px-2 py-1 rounded-lg text-sm font-bold">
                    面议
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="package" class="w-3 h-3"></i>
                    <span>货找车</span>
                </span>
                <span class="text-gray-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield" class="w-3 h-3"></i>
                    <span>未认证</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="zap" class="w-3 h-3"></i>
                    <span>非固定</span>
                </span>
                <span class="text-purple-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="map" class="w-3 h-3"></i>
                    <span>跨城区</span>
                </span>
                <span class="text-purple-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="ticket" class="w-3 h-3"></i>
                    <span>体验券</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5 hover:text-blue-300 transition-colors">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    电子产品运输，需要厢式货车，有运输资质。货物已打包，装卸方便。
                </p>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center mb-3 text-sm text-slate-400">
                <i data-lucide="map-pin" class="w-3 h-3 mr-1 text-slate-400"></i>
                <span>深圳市南山区科技园南区T3栋</span>
                <span class="ml-2 text-xs text-orange-400">3.5km</span>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="text-sm text-slate-400">
                <div class="flex items-center justify-between">
                    <span class="flex items-center text-xs">
                    </span>
                </div>
            </div>
        </div>

        <!-- 车找货卡片 -->
        <div class="transportation-card bg-slate-800 rounded-xl p-4 mb-4 card-hover border border-slate-700" data-category="车找货" data-route="fixed" data-area="urban">
            
            <!-- 标题和价格区域 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                <h3 class="font-bold text-white text-lg">宝安 → 龙岗</h3>
                    <span class="ml-2 text-sm text-slate-400 whitespace-nowrap">
                        <i data-lucide="clock" class="w-3 h-3 inline-block mr-1 align-text-bottom text-slate-400"></i>
                        10月15日 16:00
                    </span>
                </div>
                <span class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-2 py-1 rounded-lg text-sm font-bold">
                    每吨150元
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-2">
                <span class="text-lime-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="truck" class="w-3 h-3"></i>
                    <span>车找货</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="repeat" class="w-3 h-3"></i>
                    <span>固定线路</span>
                </span>
                <span class="text-teal-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="building" class="w-3 h-3"></i>
                    <span>城区内</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5 hover:text-blue-300 transition-colors">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
            </div>
            
            <!-- 途径信息 -->
            <div class="flex items-center mb-3 text-sm text-slate-400">
                <i data-lucide="route" class="w-3 h-3 mr-1 text-slate-400"></i>
                <span>途径：松岗、公明、光明</span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    专业货运司机，车况良好，可装载3吨以内货物。价格公道，服务周到。
                </p>
            </div>
            
            <!-- 地址信息 -->
            <div class="flex items-center mb-3 text-sm text-slate-400">
                <i data-lucide="map-pin" class="w-3 h-3 mr-1 text-slate-400"></i>
                <span>宝安区福永街道凤凰山物流园B区</span>
                <span class="ml-2 text-xs text-orange-400">2.8km</span>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="text-sm text-slate-400">
                <div class="flex items-center justify-between">
                    <span class="flex items-center text-xs">
                    </span>
                </div>
            </div>
        </div>

        <!-- 代驾服务卡片 -->
        <div class="transportation-card bg-slate-800 rounded-xl p-4 mb-4 card-hover border border-slate-700" data-category="代驾" data-route="flexible" data-area="urban">
            
            <!-- 标题和价格区域 -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                <h3 class="font-bold text-white text-lg">专业代驾服务</h3>
                    <span class="ml-2 text-sm text-slate-400 whitespace-nowrap">
                        <i data-lucide="clock" class="w-3 h-3 inline-block mr-1 align-text-bottom text-slate-400"></i>
                        全天候服务
                    </span>
                </div>
                <span class="bg-yellow-600 text-white px-2 py-1 rounded-lg text-sm font-bold">
                    ¥30起步
                </span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-indigo-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="steering-wheel" class="w-3 h-3"></i>
                    <span>代驾</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="zap" class="w-3 h-3"></i>
                    <span>非固定</span>
                </span>
                <span class="text-teal-400 text-xs px-1.5 py-1 flex items-center space-x-0.5">
                    <i data-lucide="building" class="w-3 h-3"></i>
                    <span>城区内</span>
                </span>
                <span class="text-purple-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="ticket" class="w-3 h-3"></i>
                    <span>体验券</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5 hover:text-blue-300 transition-colors">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    专业代驾司机，安全可靠，熟悉深圳路况。酒后代驾、商务代驾均可。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div>
                <div class="flex items-center space-x-4 text-sm text-slate-400 mb-2">
                    <span class="flex items-center space-x-[6px]">
                        <i data-lucide="shield-check" class="w-3 h-3 text-green-400"></i>
                        <span>驾龄10年</span>
                    </span>
                    <span class="flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>2.5km</span>
                    </span>
                </div>
            </div>
        </div>

    </div>

    <!-- 底部导航 -->
        <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button onclick="window.location.href='shops.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-orange-400">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs font-medium">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button data-action="navigate-to-publish" data-params='{"type":"shops"}' class="module-btn-shop">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布本地信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿任务</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 注册此页面的初始化函数
        PageInitializer.register('shops', () => {
            // 页面特定初始化
        });

        // 发布弹窗功能
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }
        
        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }
        
        function navigateToPublish(type) {
            closePublishModal();
            window.location.href = `publish-${type}.html`;
        }
        
        // 切换线路类型
        function toggleRouteType(type) {
            Logger.debug('切换线路类型:', type);
            
            // 重置所有线路类型样式
            document.getElementById('flexible-route-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all hover:bg-slate-500';
            document.getElementById('flexible-route-icon').className = 'w-3 h-3 text-slate-400';
            document.getElementById('flexible-route-text').className = 'text-xs text-slate-400';
            document.getElementById('flexible-route-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-transparent rounded-full';
            
            document.getElementById('fixed-route-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all hover:bg-slate-500';
            document.getElementById('fixed-route-icon').className = 'w-3 h-3 text-slate-400';
            document.getElementById('fixed-route-text').className = 'text-xs text-slate-400';
            document.getElementById('fixed-route-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-transparent rounded-full';
            
            // 设置选中的线路类型样式
            if (type === 'flexible') {
                document.getElementById('flexible-route-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-orange-500/20 border-2 border-orange-500 rounded-lg transition-all hover:bg-orange-500/30';
                document.getElementById('flexible-route-icon').className = 'w-3 h-3 text-orange-400';
                document.getElementById('flexible-route-text').className = 'text-xs font-medium text-orange-400';
                document.getElementById('flexible-route-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-orange-500 rounded-full';
                window.currentRouteType = 'flexible';
            } else if (type === 'fixed') {
                document.getElementById('fixed-route-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-orange-500/20 border-2 border-orange-500 rounded-lg transition-all hover:bg-orange-500/30';
                document.getElementById('fixed-route-icon').className = 'w-3 h-3 text-orange-400';
                document.getElementById('fixed-route-text').className = 'text-xs font-medium text-orange-400';
                document.getElementById('fixed-route-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-orange-500 rounded-full';
                window.currentRouteType = 'fixed';
            }
            
            // 当"全部"分类被选中时，不应用线路类型筛选
            const selectedCategory = document.querySelector('.filter-btn.bg-orange-600')?.dataset.filter || 'all';
            if (selectedCategory === 'all') {
                Logger.debug("当前是'全部'分类，线路类型筛选仅视觉变化，不影响卡片显示");
                return;
            }
            
            // 重新应用筛选
            filterCards();
        }
        
        // 切换服务范围
        function toggleAreaType(area) {
            Logger.debug('切换服务范围:', area);
            
            // 重置所有服务范围样式
            document.getElementById('urban-area-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all hover:bg-slate-500';
            document.getElementById('urban-area-icon').className = 'w-3 h-3 text-slate-400';
            document.getElementById('urban-area-text').className = 'text-xs text-slate-400';
            document.getElementById('urban-area-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-transparent rounded-full';
            
            document.getElementById('intercity-area-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all hover:bg-slate-500';
            document.getElementById('intercity-area-icon').className = 'w-3 h-3 text-slate-400';
            document.getElementById('intercity-area-text').className = 'text-xs text-slate-400';
            document.getElementById('intercity-area-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-transparent rounded-full';
            
            // 设置选中的服务范围样式
            if (area === 'urban') {
                document.getElementById('urban-area-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-green-500/20 border-2 border-green-500 rounded-lg transition-all hover:bg-green-500/30';
                document.getElementById('urban-area-icon').className = 'w-3 h-3 text-green-400';
                document.getElementById('urban-area-text').className = 'text-xs font-medium text-green-400';
                document.getElementById('urban-area-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-green-500 rounded-full';
                window.currentAreaType = 'urban';
            } else if (area === 'intercity') {
                document.getElementById('intercity-area-bg').className = 'flex items-center justify-center space-x-[6px] p-2 bg-green-500/20 border-2 border-green-500 rounded-lg transition-all hover:bg-green-500/30';
                document.getElementById('intercity-area-icon').className = 'w-3 h-3 text-green-400';
                document.getElementById('intercity-area-text').className = 'text-xs font-medium text-green-400';
                document.getElementById('intercity-area-dot').className = 'absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-green-500 rounded-full';
                window.currentAreaType = 'intercity';
            }
            
            // 当"全部"分类被选中时，不应用服务范围筛选
            const selectedCategory = document.querySelector('.filter-btn.bg-orange-600')?.dataset.filter || 'all';
            if (selectedCategory === 'all') {
                Logger.debug("当前是'全部'分类，服务范围筛选仅视觉变化，不影响卡片显示");
                return;
            }
            
            // 重新应用筛选
            filterCards();
        }
        
        // 综合筛选逻辑 - 按分类、线路类型和服务范围筛选
        function filterCards() {
            const selectedCategory = document.querySelector('.filter-btn.bg-orange-600')?.dataset.filter || 'all';
            const selectedRouteType = window.currentRouteType || 'all';
            const selectedAreaType = window.currentAreaType || 'all';
            
            Logger.debug('筛选条件:', { 分类: selectedCategory, 线路类型: selectedRouteType, 服务范围: selectedAreaType });
            
            const cards = document.querySelectorAll('.transportation-card');
            
            // 显示所有卡片之前先计数
            Logger.debug(`开始筛选，总共 ${cards.length} 个卡片`);
            
            // 先将所有卡片显示出来
            cards.forEach(card => {
                card.style.display = 'block';
                card.style.opacity = '1';
                card.style.visibility = 'visible';
            });
            
            // 如果选择了"全部"分类，则不应用线路类型和服务范围筛选
            if (selectedCategory === 'all') {
                Logger.debug("选择了'全部'分类，显示所有卡片");
                return;
            }
            
            // 应用多条件筛选
            cards.forEach(card => {
                const cardCategory = card.dataset.category;
                const cardRoute = card.dataset.route;
                const cardArea = card.dataset.area;
                
                Logger.debug(`检查卡片: ${cardCategory}, 路线: ${cardRoute}, 区域: ${cardArea}`);
                
                let shouldShow = true;
                
                // 分类筛选
                if (selectedCategory !== 'all' && cardCategory !== selectedCategory) {
                    shouldShow = false;
                }
                
                // 线路类型筛选 - 仅当线路类型不是'all'时才应用
                if (selectedRouteType !== 'all' && cardRoute !== selectedRouteType) {
                    shouldShow = false;
                }
                
                // 服务区域筛选 - 仅当服务区域不是'all'时才应用
                if (selectedAreaType !== 'all' && cardArea !== selectedAreaType) {
                    shouldShow = false;
                }
                
                if (!shouldShow) {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                    card.style.visibility = 'hidden';
                }
            });
            
            // 显示筛选结果统计
            const visibleCards = Array.from(cards).filter(card => card.style.display !== 'none');
            Logger.debug(`筛选后显示了 ${visibleCards.length} 个卡片，总共 ${cards.length} 个卡片`);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化全局变量，不应用默认筛选条件
            window.currentRouteType = 'all'; // 改为'all'以显示全部
            window.currentAreaType = 'all';  // 改为'all'以显示全部
            
            // 初始化分类筛选按钮事件
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(filter => {
                filter.addEventListener('click', function() {
                    // 重置所有按钮状态
                    filterButtons.forEach(f => {
                        f.className = 'filter-btn flex-shrink-0 px-3 py-2 rounded-full text-sm font-medium transition-colors bg-slate-700 text-slate-300 hover:bg-slate-600';
                    });
                    
                    // 设置当前按钮为选中状态
                    this.className = 'filter-btn flex-shrink-0 px-3 py-2 rounded-full text-sm font-medium transition-colors bg-orange-600 text-white';
                    
                    // 执行筛选
                    filterCards();
                });
            });
            
            // 确保所有卡片初始状态为显示
            const allCards = document.querySelectorAll('.transportation-card');
            allCards.forEach(card => {
                card.style.display = 'block';
                card.style.opacity = '1';
                card.style.visibility = 'visible';
                Logger.debug(`初始化显示卡片: ${card.dataset.category}`);
            });
            
            // 确保"全部"按钮为选中状态
            const allButton = document.querySelector('.filter-btn[data-filter="all"]');
            if (allButton) {
                allButton.className = 'filter-btn flex-shrink-0 px-3 py-2 rounded-full text-sm font-medium transition-colors bg-orange-600 text-white';
            }
            
            Logger.debug('页面初始化完成，显示所有卡片');
            });
        
        // 卡片点击查看详情
        document.querySelectorAll('.card-hover').forEach(card => {
            card.addEventListener('click', function(e) {
                // 避免按钮点击事件冒泡
                if (!e.target.closest('button')) {
                    Logger.debug('查看出行详情');
                }
            });
        });
        
        // 电话拨号功能
        document.querySelectorAll('[data-lucide="phone"]').forEach(button => {
            button.parentElement.addEventListener('click', function(e) {
                e.stopPropagation();
                if (typeof callPhone === 'function') {
                    callPhone('13800138000');
                } else {
                    window.location.href = 'tel:13800138000';
                }
            });
        });
    </script>
</body>
</html>