<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出行详情 - 智慧出行</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        .transport-tag {
            background: linear-gradient(to right, #06b6d4, #3b82f6);
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .urgent-tag {
            background: linear-gradient(to right, #ef4444, #ec4899);
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .book-btn {
            background: linear-gradient(to right, #06b6d4, #3b82f6, #8b5cf6);
        }
        .route-dot {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
        }
        .route-line {
            width: 0.125rem;
            height: 2rem;
            background: linear-gradient(to bottom, #22d3ee, #3b82f6);
            margin: 0 auto;
        }
        .tech-card {
            background: linear-gradient(to bottom right, #1e293b, #1e293b, #334155);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 0.75rem;
        }
        .tech-glow {
            box-shadow: 0 10px 15px -3px rgba(6, 182, 212, 0.1), 0 4px 6px -2px rgba(6, 182, 212, 0.05);
        }
        .safety-collapsible {
            transition: max-height 0.3s ease-in-out;
            overflow: hidden;
        }
        .safety-collapsed {
            max-height: 60px;
        }
        .safety-expanded {
            max-height: 1000px;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-100 min-h-screen">
    <!-- 顶部导航 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button onclick="history.back()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </button>
                    <h1 id="detail-title" class="text-lg font-semibold">出行详情</h1>
                </div>
                <div class="flex items-center space-x-1">
                    <!-- 顶部操作按钮已移除，避免重复 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-md mx-auto px-4 py-4 space-y-4 pb-20" id="detail-content">
        <!-- 动态内容将在这里渲染 -->
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700" id="bottom-bar">
        <!-- 动态内容将在这里渲染 -->
    </div>

    <script>
        // 数据定义
        const transportData = {
            'carpool-driver': {
                type: '车找人',
                category: '车找人',
                title: '深圳北站 → 广州南站',
                time: '今天 14:30',
                publishDate: '2024-01-15 10:30',
                price: '¥45/人',
                seats: '还有2个座位',
                distance: '120公里',
                verified: true,
                hasVoucher: true,
                tags: [],
                routeType: '固定线路',
                serviceScope: '城区',
                route: {
                    from: '深圳北站',
                    to: '广州南站',
                    waypoints: ['深圳宝安', '东莞虎门']
                },
                description: '本人驾龄8年，熟悉路线，车况良好，全程高速，预计2小时到达。车内禁烟，可提供充电器和矿泉水。',
                actionText: '联系车主'
            },
            'carpool-passenger': {
                type: '人找车',
                category: '人找车',
                title: '广州南站 → 深圳北站',
                time: '明天 09:00',
                publishDate: '2024-01-15 08:20',
                price: '¥40/人',
                seats: '需要1个座位',
                distance: '120公里',
                verified: false,
                hasVoucher: false,
                tags: [],
                routeType: '非固定线路',
                serviceScope: '跨城区',
                passenger: {
                    name: '张先生',
                    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face',
                    verified: true,
                    rating: '5.0分 · 128次出行'
                },
                route: {
                    from: '广州南站',
                    to: '深圳北站',
                    waypoints: ['东莞虎门', '深圳宝安']
                },
                description: '明天早上有事需要到深圳，希望找个靠谱的司机师傅，可以稍等5-10分钟。',
                actionText: '联系乘客'
            },
            'cargo-driver': {
                type: '车找货',
                category: '车找货',
                title: '深圳 → 东莞 货运服务',
                time: '今天 16:00',
                publishDate: '2024-01-15 12:45',
                price: '¥200起',
                distance: '45公里',
                verified: true,
                hasVoucher: false,
                tags: ['4.2米车', '可装卸'],
                routeType: '固定线路',
                serviceScope: '城区',
                description: '4.2米厢式货车，载重3吨，提供装卸服务，深圳到东莞及周边地区货运，价格面议。',
                actionText: '联系车主'
            },
            'driving-service': {
                type: '货找车',
                category: '货找车',
                title: '深圳 → 东莞 货物运输',
                time: '今天 18:00',
                publishDate: '2024-01-15 14:20',
                price: '¥150起',
                service_area: '深圳全市',
                distance: '45公里',
                verified: true,
                hasVoucher: true,
                tags: ['3吨货物', '需装卸'],
                routeType: '非固定线路',
                serviceScope: '跨城区',
                description: '有一批办公用品需要从深圳运到东莞，重量约3吨，需要司机师傅协助装卸，价格可议。',
                actionText: '联系货主'
            }
        };

        let currentType = 'carpool-driver';
        let isFavorited = false;

        // 工具函数
        function buildTag(text, className = 'bg-slate-600') {
            return `<span class="${className} text-white px-2 py-1 rounded-full text-xs">${text}</span>`;
        }

        function buildSection(icon, title, content) {
            return `
                <div class="tech-card tech-glow rounded-xl p-6 space-y-4">
                    <h3 class="text-lg font-semibold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent flex items-center space-x-2">
                        <i data-lucide="${icon}" class="w-5 h-5 text-cyan-400"></i>
                        <span>${title}</span>
                    </h3>
                    ${content}
                </div>
            `;
        }

        // 构建函数
        function buildHeader(data) {
             const tagsHtml = data.tags && data.tags.length > 0 ? data.tags.map(tag => buildTag(tag, 'bg-gradient-to-r from-orange-500 to-red-500')).join('') : '';
             const seatsHtml = data.seats ? `<span>${data.seats}</span>` : '';
             const serviceAreaHtml = data.service_area ? `<span>${data.service_area}</span>` : '';
             const verifiedBadge = data.verified ? buildTag('已认证', 'bg-gradient-to-r from-green-500 to-emerald-500') : buildTag('未认证', 'bg-gradient-to-r from-gray-500 to-slate-500');
             const voucherBadge = data.hasVoucher ? buildTag('体验券', 'bg-gradient-to-r from-purple-500 to-violet-500') : '';
             const routeTypeBadge = buildTag(`${data.routeType}`, 'bg-gradient-to-r from-indigo-500 to-blue-500');
             const serviceScopeBadge = buildTag(`${data.serviceScope}`, 'bg-gradient-to-r from-teal-500 to-cyan-500');
             const distanceBadge = '';
             const categoryIcon = getCategoryIcon(data.category);
             
             return `
                 <div class="tech-card tech-glow rounded-xl p-6 space-y-4">
                     <div class="flex flex-wrap gap-2 mb-3">
                         <span class="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-1">
                             <i data-lucide="${categoryIcon}" class="w-3 h-3"></i>
                             <span>${data.category}</span>
                         </span>
                         ${verifiedBadge}
                         ${voucherBadge}
                         ${routeTypeBadge}
                         ${serviceScopeBadge}
                         ${distanceBadge}
                         ${tagsHtml}
                     </div>
                     <div class="flex items-start justify-between">
                         <h2 class="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent leading-relaxed flex-1">${data.title}</h2>
                         <div class="flex items-center space-x-1 ml-4">
                             <button onclick="toggleFavorite()" class="p-2 hover:bg-slate-700/50 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-cyan-500/20">
                                 <i data-lucide="heart" class="w-4 h-4 text-cyan-400" id="favorite-icon"></i>
                             </button>
                             <button onclick="shareTrip()" class="p-2 hover:bg-slate-700/50 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20">
                                 <i data-lucide="share-2" class="w-4 h-4 text-blue-400"></i>
                             </button>
                             <button onclick="reportTrip()" class="p-2 hover:bg-slate-700/50 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-red-500/20">
                                 <i data-lucide="flag" class="w-4 h-4 text-red-400"></i>
                             </button>
                         </div>
                     </div>
                     <div class="flex items-center justify-between text-sm text-slate-400">
                         <span class="text-cyan-300">${data.time}</span>
                         ${seatsHtml}${serviceAreaHtml}
                     </div>
                     <div class="flex items-center justify-between text-xs text-slate-500">
                         <span>发布时间：${data.publishDate}</span>
                         <span class="text-cyan-400">${data.price}</span>
                     </div>
                 </div>
             `;
         }
        
        function getCategoryIcon(category) {
             const iconMap = {
                 '车找人': 'car',
                 '人找车': 'users',
                 '车找货': 'truck',
                 '货找车': 'package'
             };
             return iconMap[category] || 'car';
         }

        function buildRoute(data) {
            if (!data.route) return '';
            
            let routeHtml = `
                <div class="space-y-4">
                    <div class="flex items-start space-x-4">
                        <div class="flex flex-col items-center">
                            <div class="route-dot bg-green-500"></div>
                            <div class="route-line"></div>
                        </div>
                        <div class="flex-1 pt-1">
                            <h4 class="font-semibold text-slate-200">${data.route.from}</h4>
                            <p class="text-sm text-slate-400 mt-1">起点</p>
                        </div>
                    </div>
            `;
            
            // 添加途径点
            if (data.route.waypoints && data.route.waypoints.length > 0) {
                data.route.waypoints.forEach(waypoint => {
                    routeHtml += `
                        <div class="flex items-start space-x-4">
                            <div class="flex flex-col items-center">
                                <div class="route-dot bg-blue-500"></div>
                                <div class="route-line"></div>
                            </div>
                            <div class="flex-1 pt-1">
                                <h4 class="font-semibold text-slate-200">${waypoint}</h4>
                                <p class="text-sm text-slate-400 mt-1">途径</p>
                            </div>
                        </div>
                    `;
                });
            }
            
            routeHtml += `
                    <div class="flex items-start space-x-4">
                        <div class="flex flex-col items-center">
                            <div class="route-dot bg-red-500"></div>
                        </div>
                        <div class="flex-1 pt-1">
                            <h4 class="font-semibold text-slate-200">${data.route.to}</h4>
                            <p class="text-sm text-slate-400 mt-1">终点</p>
                        </div>
                    </div>
                </div>
            `;
            
            return buildSection('route', '途径路线', routeHtml);
        }

        function buildPassengerInfo(data) {
            if (!data.passenger) return '';
            const verifiedTag = data.passenger.verified ? buildTag('信用极好', 'bg-green-500') : '';
            const content = `
                <div class="flex items-center space-x-4">
                    <img src="${data.passenger.avatar}" alt="乘客头像" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h3 class="font-semibold text-slate-200">${data.passenger.name}</h3>
                            ${verifiedTag}
                        </div>
                        <div class="text-xs text-slate-400 mt-1">${data.passenger.rating}</div>
                    </div>
                </div>
            `;
            return buildSection('user', '乘客信息', content);
        }

        function buildDescription(data) {
            if (!data.description) return '';
            const content = `<p class="text-slate-300 text-sm leading-relaxed">${data.description}</p>`;
            return buildSection('file-text', '详情描述', content);
        }

        function buildSafetyReminder() {
              const content = `
                  <div id="safety-reminder" class="tech-card tech-glow rounded-xl p-4 relative border border-amber-500/30">
                      <div class="flex items-center justify-between mb-2 cursor-pointer" onclick="toggleSafetyContent()">
                          <div class="flex items-center space-x-2">
                              <i data-lucide="shield-alert" class="w-5 h-5 text-amber-400"></i>
                              <h4 class="text-amber-400 font-medium">平台安全提醒</h4>
                              <i data-lucide="chevron-down" class="w-4 h-4 text-amber-400 transition-transform duration-200" id="safety-chevron"></i>
                          </div>
                          <div class="flex items-center space-x-2">
                              <span id="countdown" class="text-xs text-amber-300 bg-amber-800/30 px-2 py-1 rounded">5s</span>
                              <button onclick="closeSafetyReminder()" class="text-amber-400 hover:text-amber-300">
                                  <i data-lucide="x" class="w-4 h-4"></i>
                              </button>
                          </div>
                      </div>
                      <div id="safety-content" class="safety-collapsible safety-collapsed">
                          <div class="text-sm text-slate-300 space-y-2 pt-2">
                              <div class="font-medium text-red-400 mb-2">⚠️ 紧急情况处理</div>
                              <ul class="space-y-1 mb-3">
                                  <li>• 遇到危险立即报警：110</li>
                                  <li>• 交通事故报警：122</li>
                                  <li>• 医疗急救：120</li>
                                  <li>• 请勿联系平台客服处理紧急情况</li>
                              </ul>
                              <div class="font-medium text-amber-400 mb-2">🛡️ 出行安全指南</div>
                              <ul class="space-y-1 mb-3">
                                  <li>• 上车前核实司机身份和车辆信息</li>
                                  <li>• 检查车辆安全状况和证件</li>
                                  <li>• 系好安全带，注意行车安全</li>
                                  <li>• 避免深夜独自乘车</li>
                                  <li>• 确认货物包装完好，贵重物品请投保</li>
                                  <li>• 核实司机资质和车辆状况</li>
                              </ul>
                              <div class="font-medium text-gray-400 mb-2">📋 平台免责声明</div>
                              <ul class="space-y-1">
                                  <li>• 本平台仅提供信息展示服务，不承担任何法律责任</li>
                                  <li>• 用户私下电话沟通产生的纠纷，平台概不负责</li>
                                  <li>• 平台不参与任何交易过程，不承担相关责任</li>
                                  <li>• 请通过平台内聊天功能联系，保护个人隐私</li>
                              </ul>
                          </div>
                      </div>
                  </div>
              `;
              return content;
          }
          
          function toggleSafetyContent() {
              const content = document.getElementById('safety-content');
              const chevron = document.getElementById('safety-chevron');
              
              if (content && chevron) {
                  if (content.classList.contains('safety-collapsed')) {
                      content.classList.remove('safety-collapsed');
                      content.classList.add('safety-expanded');
                      chevron.style.transform = 'rotate(180deg)';
                  } else {
                      content.classList.remove('safety-expanded');
                      content.classList.add('safety-collapsed');
                      chevron.style.transform = 'rotate(0deg)';
                  }
              }
          }
          
          function closeSafetyReminder() {
              const reminder = document.getElementById('safety-reminder');
              if (reminder) {
                  reminder.style.display = 'none';
              }
          }
          
          function startCountdown() {
              let count = 5;
              const countdownElement = document.getElementById('countdown');
              
              const timer = setInterval(() => {
                  count--;
                  if (countdownElement) {
                      countdownElement.textContent = count + 's';
                  }
                  
                  if (count <= 0) {
                      clearInterval(timer);
                      closeSafetyReminder();
                  }
              }, 1000);
          }

        function buildReviews() {
            const content = `
                <div class="space-y-4">
                    <div class="bg-slate-700 rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-2">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" alt="用户头像" class="w-8 h-8 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-slate-200">王女士</span>
                                    <span class="text-xs text-slate-400">2024-01-10</span>
                                </div>
                                <div class="flex items-center space-x-1 mt-1">
                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400 fill-current"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-slate-300">李师傅人很好，开车很稳，准时到达！</p>
                    </div>
                </div>
            `;
            return buildSection('message-circle', '乘客评价', content);
        }

        function buildRecommendations() {
            const content = `
                <div class="space-y-3">
                    <div class="bg-slate-700 rounded-lg p-4 cursor-pointer hover:bg-slate-600 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-slate-200">深圳北站 → 广州南站</h4>
                                <p class="text-sm text-slate-400 mt-1">今天 16:30 • ¥40/人</p>
                            </div>
                            <div class="text-right">
                                <span class="text-xs text-green-400">还有3座</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-slate-700 rounded-lg p-4 cursor-pointer hover:bg-slate-600 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-slate-200">深圳北站 → 广州南站</h4>
                                <p class="text-sm text-slate-400 mt-1">明天 08:00 • ¥45/人</p>
                            </div>
                            <div class="text-right">
                                <span class="text-xs text-green-400">还有1座</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            return buildSection('compass', '相关推荐', content);
        }
        
        function buildDetailHtml(data) {
            const sections = [
                buildHeader(data),
                buildSafetyReminder(),
                buildRoute(data),
                buildPassengerInfo(data),
                buildDescription(data)
            ];
            
            // 只为拼车司机显示评价
            if (currentType === 'carpool-driver') {
                sections.push(buildReviews());
                sections.push(buildRecommendations());
            }
            
            return sections.filter(section => section).join('');
        }

        function buildBottomBar(data) {
            const priceHtml = data.price ? `<span class="text-xl font-bold text-green-400">${data.price}</span>` : '';
            return `
                <div class="max-w-md mx-auto px-4 py-3">
                    <div class="flex items-center justify-between">
                        <div class="text-slate-200">${priceHtml}</div>
                        <button class="book-btn text-white px-6 py-3 rounded-lg font-medium hover:opacity-90 transition-opacity flex items-center justify-center space-x-2" onclick="contact()">
                            <i data-lucide="phone" class="w-5 h-5"></i>
                            <span>${data.actionText}</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 交互函数
        function toggleFavorite() {
            isFavorited = !isFavorited;
            const icon = document.getElementById('favorite-icon');
            if (isFavorited) {
                icon.classList.add('text-red-500', 'fill-current');
            } else {
                icon.classList.remove('text-red-500', 'fill-current');
            }
        }

        function shareTrip() {
            const data = transportData[currentType];
            if (navigator.share) {
                navigator.share({
                    title: data.title,
                    text: `${data.time}，${data.price || '价格面议'}`,
                    url: window.location.href
                });
            } else {
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }

        function contact() {
            const data = transportData[currentType];
            alert(`正在为您连接${data.category}服务...`);
        }
        
        function forwardTrip() {
            alert('转发功能开发中...');
        }
        
        function reportTrip() {
            if (confirm('确定要举报此出行信息吗？')) {
                alert('举报已提交，我们会尽快处理。');
            }
        }

        // 主渲染函数
        function renderTransportDetail() {
            const urlParams = new URLSearchParams(window.location.search);
            currentType = urlParams.get('type') || 'carpool-driver';
            const data = transportData[currentType];

            if (!data) {
                document.getElementById('detail-content').innerHTML = `<p class="text-center text-slate-400 py-10">未找到相关信息。</p>`;
                return;
            }

            document.getElementById('detail-title').textContent = data.category + '详情';
            document.getElementById('detail-content').innerHTML = buildDetailHtml(data);
            document.getElementById('bottom-bar').innerHTML = buildBottomBar(data);

            lucide.createIcons();
            // 启动安全提醒倒计时
            setTimeout(startCountdown, 100);
        }

        // 页面加载时渲染
        document.addEventListener('DOMContentLoaded', renderTransportDetail);
    </script>
</body>
</html>