<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广告管理 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .status-active {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .status-inactive {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }
        .status-pending {
            background: linear-gradient(45deg, #f59e0b, #f97316);
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 侧边栏 -->
    <div class="fixed left-0 top-0 h-full w-64 bg-slate-800 border-r border-slate-700 z-50">
        <div class="p-6">
            <div class="flex items-center space-x-3 mb-8">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-lg font-bold text-white">管理后台</h1>
                    <p class="text-xs text-slate-400">本地助手</p>
                </div>
            </div>
            
            <nav class="space-y-2">
                <a href="dashboard.html" class="flex items-center space-x-3 px-4 py-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                    <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
                    <span>仪表盘</span>
                </a>
                <a href="user-management.html" class="flex items-center space-x-3 px-4 py-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                    <i data-lucide="users" class="w-5 h-5"></i>
                    <span>用户管理</span>
                </a>
                <a href="content-management.html" class="flex items-center space-x-3 px-4 py-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                    <i data-lucide="file-text" class="w-5 h-5"></i>
                    <span>内容管理</span>
                </a>
                <a href="ad-management.html" class="flex items-center space-x-3 px-4 py-3 bg-blue-600 text-white rounded-lg">
                    <i data-lucide="megaphone" class="w-5 h-5"></i>
                    <span>广告管理</span>
                </a>
                <a href="data-analytics.html" class="flex items-center space-x-3 px-4 py-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                    <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
                    <span>数据分析</span>
                </a>
                <a href="system-settings.html" class="flex items-center space-x-3 px-4 py-3 text-slate-300 hover:bg-slate-700 hover:text-white rounded-lg transition-colors">
                    <i data-lucide="settings" class="w-5 h-5"></i>
                    <span>系统设置</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="ml-64 min-h-screen">
        <!-- 顶部导航栏 -->
        <div class="bg-slate-800 border-b border-slate-700 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-white">广告管理</h2>
                    <p class="text-sm text-slate-400 mt-1">管理平台广告内容和投放策略</p>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button onclick="showCreateAdModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>新建广告</span>
                    </button>
                    
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
                             alt="管理员" class="w-8 h-8 rounded-full">
                        <span class="text-sm text-slate-300">管理员</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-slate-400 text-sm">活跃广告</p>
                            <p class="text-2xl font-bold text-white mt-1">12</p>
                        </div>
                        <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="play-circle" class="w-6 h-6 text-green-500"></i>
                        </div>
                    </div>
                    <div class="flex items-center mt-4 text-sm">
                        <span class="text-green-500">+2</span>
                        <span class="text-slate-400 ml-1">较上周</span>
                    </div>
                </div>
                
                <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-slate-400 text-sm">总点击量</p>
                            <p class="text-2xl font-bold text-white mt-1">8,547</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="mouse-pointer-click" class="w-6 h-6 text-blue-500"></i>
                        </div>
                    </div>
                    <div class="flex items-center mt-4 text-sm">
                        <span class="text-green-500">+12.5%</span>
                        <span class="text-slate-400 ml-1">较上周</span>
                    </div>
                </div>
                
                <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-slate-400 text-sm">点击率</p>
                            <p class="text-2xl font-bold text-white mt-1">3.2%</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="target" class="w-6 h-6 text-purple-500"></i>
                        </div>
                    </div>
                    <div class="flex items-center mt-4 text-sm">
                        <span class="text-green-500">+0.3%</span>
                        <span class="text-slate-400 ml-1">较上周</span>
                    </div>
                </div>
                
                <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-slate-400 text-sm">广告收入</p>
                            <p class="text-2xl font-bold text-white mt-1">¥12,450</p>
                        </div>
                        <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="dollar-sign" class="w-6 h-6 text-orange-500"></i>
                        </div>
                    </div>
                    <div class="flex items-center mt-4 text-sm">
                        <span class="text-green-500">+8.2%</span>
                        <span class="text-slate-400 ml-1">较上周</span>
                    </div>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="bg-slate-800 rounded-xl p-6 border border-slate-700 mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="搜索广告标题或内容..." 
                                   class="w-full sm:w-80 bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 pl-10 text-sm text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                        </div>
                        
                        <select class="bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-sm text-slate-300 focus:ring-2 focus:ring-blue-500">
                            <option>全部状态</option>
                            <option>活跃中</option>
                            <option>已暂停</option>
                            <option>待审核</option>
                        </select>
                        
                        <select class="bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-sm text-slate-300 focus:ring-2 focus:ring-blue-500">
                            <option>全部板块</option>
                            <option>商铺板块</option>
                            <option>信息板块</option>
                            <option>出行板块</option>
                            <option>跑腿板块</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button class="bg-slate-700 hover:bg-slate-600 text-slate-300 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <i data-lucide="download" class="w-4 h-4"></i>
                            <span>导出数据</span>
                        </button>
                        <button class="bg-slate-700 hover:bg-slate-600 text-slate-300 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <i data-lucide="settings" class="w-4 h-4"></i>
                            <span>广告位设置</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 广告列表 -->
            <div class="bg-slate-800 rounded-xl border border-slate-700 overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-slate-700">
                            <tr>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">广告信息</th>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">投放板块</th>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">状态</th>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">点击量</th>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">点击率</th>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">收入</th>
                                <th class="text-left py-4 px-6 text-sm font-medium text-slate-300">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-700">
                            <tr class="hover:bg-slate-700/50 transition-colors">
                                <td class="py-4 px-6">
                                    <div class="flex items-center space-x-3">
                                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=48&h=48&fit=crop&crop=center" 
                                             alt="广告图片" class="w-12 h-12 rounded-lg object-cover">
                                        <div>
                                            <p class="text-sm font-medium text-white">智能家居新品上市</p>
                                            <p class="text-xs text-slate-400">全屋智能解决方案，让生活更便捷</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">商铺板块</span>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="status-active text-white px-2 py-1 rounded-full text-xs">活跃中</span>
                                </td>
                                <td class="py-4 px-6 text-sm text-slate-300">1,247</td>
                                <td class="py-4 px-6 text-sm text-slate-300">3.2%</td>
                                <td class="py-4 px-6 text-sm text-slate-300">¥2,450</td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300 transition-colors">
                                            <i data-lucide="edit" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                            <i data-lucide="pause" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300 transition-colors">
                                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-slate-700/50 transition-colors">
                                <td class="py-4 px-6">
                                    <div class="flex items-center space-x-3">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face" 
                                             alt="广告图片" class="w-12 h-12 rounded-lg object-cover">
                                        <div>
                                            <p class="text-sm font-medium text-white">在线职业技能培训</p>
                                            <p class="text-xs text-slate-400">提升职场竞争力，获得更好工作机会</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs">信息板块</span>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="status-active text-white px-2 py-1 rounded-full text-xs">活跃中</span>
                                </td>
                                <td class="py-4 px-6 text-sm text-slate-300">892</td>
                                <td class="py-4 px-6 text-sm text-slate-300">2.8%</td>
                                <td class="py-4 px-6 text-sm text-slate-300">¥1,780</td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300 transition-colors">
                                            <i data-lucide="edit" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                            <i data-lucide="pause" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300 transition-colors">
                                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-slate-700/50 transition-colors">
                                <td class="py-4 px-6">
                                    <div class="flex items-center space-x-3">
                                        <img src="https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=48&h=48&fit=crop&crop=center" 
                                             alt="广告图片" class="w-12 h-12 rounded-lg object-cover">
                                        <div>
                                            <p class="text-sm font-medium text-white">专业汽车保养服务</p>
                                            <p class="text-xs text-slate-400">上门保养，专业技师，品质保证</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs">出行板块</span>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="status-pending text-white px-2 py-1 rounded-full text-xs">待审核</span>
                                </td>
                                <td class="py-4 px-6 text-sm text-slate-300">-</td>
                                <td class="py-4 px-6 text-sm text-slate-300">-</td>
                                <td class="py-4 px-6 text-sm text-slate-300">¥0</td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button class="text-green-400 hover:text-green-300 transition-colors">
                                            <i data-lucide="check" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300 transition-colors">
                                            <i data-lucide="x" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-slate-700/50 transition-colors">
                                <td class="py-4 px-6">
                                    <div class="flex items-center space-x-3">
                                        <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=48&h=48&fit=crop&crop=center" 
                                             alt="广告图片" class="w-12 h-12 rounded-lg object-cover">
                                        <div>
                                            <p class="text-sm font-medium text-white">闪送同城急送服务</p>
                                            <p class="text-xs text-slate-400">1小时内送达，专人专送更安全</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full text-xs">跑腿板块</span>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="status-inactive text-white px-2 py-1 rounded-full text-xs">已暂停</span>
                                </td>
                                <td class="py-4 px-6 text-sm text-slate-300">654</td>
                                <td class="py-4 px-6 text-sm text-slate-300">2.1%</td>
                                <td class="py-4 px-6 text-sm text-slate-300">¥1,320</td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-400 hover:text-blue-300 transition-colors">
                                            <i data-lucide="edit" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-green-400 hover:text-green-300 transition-colors">
                                            <i data-lucide="play" class="w-4 h-4"></i>
                                        </button>
                                        <button class="text-red-400 hover:text-red-300 transition-colors">
                                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="bg-slate-700 px-6 py-4 flex items-center justify-between">
                    <div class="text-sm text-slate-400">
                        显示 1-10 条，共 24 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors">
                            上一页
                        </button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded">
                            1
                        </button>
                        <button class="px-3 py-1 bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors">
                            2
                        </button>
                        <button class="px-3 py-1 bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors">
                            3
                        </button>
                        <button class="px-3 py-1 bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新建广告弹窗 -->
    <div id="createAdModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-xl max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">新建广告</h3>
                    <button onclick="closeCreateAdModal()" class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">广告标题</label>
                            <input type="text" placeholder="请输入广告标题" 
                                   class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">投放板块</label>
                            <select class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 focus:ring-2 focus:ring-blue-500">
                                <option>选择投放板块</option>
                                <option>商铺板块</option>
                                <option>信息板块</option>
                                <option>出行板块</option>
                                <option>跑腿板块</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">广告描述</label>
                        <textarea placeholder="请输入广告描述" rows="3"
                                  class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">广告图片</label>
                        <div class="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center">
                            <i data-lucide="upload" class="w-8 h-8 text-slate-400 mx-auto mb-2"></i>
                            <p class="text-slate-400 text-sm">点击上传或拖拽图片到此处</p>
                            <p class="text-slate-500 text-xs mt-1">支持 JPG、PNG 格式，建议尺寸 400x300px</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">链接类型</label>
                            <select class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 focus:ring-2 focus:ring-blue-500">
                                <option>外部链接</option>
                                <option>纯展示</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">目标链接</label>
                            <input type="url" placeholder="https://example.com" 
                                   class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">开始时间</label>
                            <input type="datetime-local" 
                                   class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">结束时间</label>
                            <input type="datetime-local" 
                                   class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-slate-300 focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-4 pt-6 border-t border-slate-700">
                        <button type="button" onclick="closeCreateAdModal()" 
                                class="px-6 py-2 bg-slate-600 text-slate-300 rounded-lg hover:bg-slate-500 transition-colors">
                            取消
                        </button>
                        <button type="submit" 
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            创建广告
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
        
        // 新建广告弹窗
        function showCreateAdModal() {
            document.getElementById('createAdModal').classList.remove('hidden');
        }
        
        function closeCreateAdModal() {
            document.getElementById('createAdModal').classList.add('hidden');
        }
        
        // 表格操作
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-lucide="edit"]')) {
                console.log('编辑广告');
            } else if (e.target.closest('[data-lucide="pause"]')) {
                console.log('暂停广告');
            } else if (e.target.closest('[data-lucide="play"]')) {
                console.log('启用广告');
            } else if (e.target.closest('[data-lucide="trash-2"]')) {
                if (confirm('确定要删除这个广告吗？')) {
                    console.log('删除广告');
                }
            } else if (e.target.closest('[data-lucide="check"]')) {
                console.log('审核通过');
            } else if (e.target.closest('[data-lucide="x"]')) {
                console.log('审核拒绝');
            }
        });
    </script>
</body>
</html>