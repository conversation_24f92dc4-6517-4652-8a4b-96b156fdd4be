<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容监控 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .risk-high { color: #dc2626; background-color: rgba(220, 38, 38, 0.1); }
        .risk-medium { color: #f59e0b; background-color: rgba(245, 158, 11, 0.1); }
        .risk-low { color: #10b981; background-color: rgba(16, 185, 129, 0.1); }
        .status-processing { color: #3b82f6; }
        .status-resolved { color: #10b981; }
        .status-pending { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 顶部导航 -->
    <nav class="gradient-bg p-4 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i data-lucide="layout-dashboard" class="w-8 h-8"></i>
                <h1 class="text-xl font-bold">本地助手管理后台</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm">管理员</span>
                <i data-lucide="user" class="w-6 h-6"></i>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold mb-2">主动内容监控系统</h2>
            <p class="text-gray-400">实时监控平台内容，自动识别违规信息并进行处理</p>
        </div>

        <!-- 监控概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">今日监控</p>
                        <p class="text-3xl font-bold">2,456</p>
                        <p class="text-green-400 text-sm mt-1">↗ +12.5%</p>
                    </div>
                    <div class="bg-blue-600 p-3 rounded-lg">
                        <i data-lucide="eye" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">风险内容</p>
                        <p class="text-3xl font-bold text-red-400">23</p>
                        <p class="text-red-400 text-sm mt-1">需要处理</p>
                    </div>
                    <div class="bg-red-600 p-3 rounded-lg">
                        <i data-lucide="triangle-alert" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">自动处理</p>
                        <p class="text-3xl font-bold text-green-400">156</p>
                        <p class="text-green-400 text-sm mt-1">98.2% 准确率</p>
                    </div>
                    <div class="bg-green-600 p-3 rounded-lg">
                        <i data-lucide="circle-check" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">误报率</p>
                        <p class="text-3xl font-bold text-yellow-400">1.8%</p>
                        <p class="text-gray-400 text-sm mt-1">持续优化</p>
                    </div>
                    <div class="bg-yellow-600 p-3 rounded-lg">
                        <i data-lucide="trending-down" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控配置 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 关键词过滤 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">关键词过滤配置</h3>
                    <button onclick="addKeyword()" class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm transition-colors">
                        <i data-lucide="plus" class="w-4 h-4 inline mr-1"></i>
                        添加关键词
                    </button>
                </div>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-red-600 text-xs rounded">高风险</span>
                            <span>违法、诈骗、赌博</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-green-400 hover:text-green-300">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-yellow-600 text-xs rounded">中风险</span>
                            <span>代办、刷单、兼职</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-green-400 hover:text-green-300">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-green-600 text-xs rounded">低风险</span>
                            <span>联系方式</span>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-green-400 hover:text-green-300">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图片审核配置 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">图片审核配置</h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-400">AI审核</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div>
                            <p class="font-medium">涉黄内容检测</p>
                            <p class="text-sm text-gray-400">检测色情、暴露等不当图片</p>
                        </div>
                        <span class="px-2 py-1 bg-green-600 text-xs rounded">已启用</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div>
                            <p class="font-medium">暴力内容检测</p>
                            <p class="text-sm text-gray-400">检测血腥、暴力等图片</p>
                        </div>
                        <span class="px-2 py-1 bg-green-600 text-xs rounded">已启用</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div>
                            <p class="font-medium">文字识别检测</p>
                            <p class="text-sm text-gray-400">识别图片中的违规文字</p>
                        </div>
                        <span class="px-2 py-1 bg-green-600 text-xs rounded">已启用</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <div>
                            <p class="font-medium">二维码检测</p>
                            <p class="text-sm text-gray-400">检测恶意二维码和链接</p>
                        </div>
                        <span class="px-2 py-1 bg-yellow-600 text-xs rounded">待启用</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 异常行为监控 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">异常行为监控</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-gray-700 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium">频繁发布监控</h4>
                        <i data-lucide="activity" class="w-5 h-5 text-blue-400"></i>
                    </div>
                    <p class="text-sm text-gray-400 mb-2">检测短时间内大量发布内容的行为</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-gray-500">阈值: 10条/小时</span>
                        <span class="px-2 py-1 bg-green-600 text-xs rounded">正常</span>
                    </div>
                </div>
                
                <div class="bg-gray-700 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium">批量注册监控</h4>
                        <i data-lucide="users" class="w-5 h-5 text-yellow-400"></i>
                    </div>
                    <p class="text-sm text-gray-400 mb-2">检测同一IP或设备的批量注册</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-gray-500">阈值: 5个/天</span>
                        <span class="px-2 py-1 bg-yellow-600 text-xs rounded">警告</span>
                    </div>
                </div>
                
                <div class="bg-gray-700 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium">恶意举报监控</h4>
                        <i data-lucide="flag" class="w-5 h-5 text-red-400"></i>
                    </div>
                    <p class="text-sm text-gray-400 mb-2">检测恶意大量举报的行为</p>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-gray-500">阈值: 20次/天</span>
                        <span class="px-2 py-1 bg-red-600 text-xs rounded">风险</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时监控日志 -->
        <div class="bg-gray-800 rounded-lg overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold">实时监控日志</h3>
                    <div class="flex space-x-2">
                        <select class="px-3 py-1 bg-gray-700 rounded text-sm">
                            <option>全部类型</option>
                            <option>关键词检测</option>
                            <option>图片审核</option>
                            <option>行为异常</option>
                        </select>
                        <button onclick="refreshLogs()" class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm transition-colors">
                            <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-1"></i>
                            刷新
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                时间
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                检测类型
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                内容摘要
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                风险等级
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                处理状态
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="monitoringLogs" class="bg-gray-800 divide-y divide-gray-700">
                        <!-- 监控日志将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 内容详情模态框 -->
    <div id="contentDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">内容详情</h3>
                        <button onclick="closeContentDetail()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6" id="contentDetailContent">
                    <!-- 内容详情将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加关键词模态框 -->
    <div id="addKeywordModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-md w-full">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">添加关键词</h3>
                        <button onclick="closeAddKeyword()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">关键词</label>
                            <input type="text" id="newKeyword" placeholder="输入关键词，多个用逗号分隔" 
                                   class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">风险等级</label>
                            <select id="keywordRisk" class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">处理方式</label>
                            <select id="keywordAction" class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                                <option value="block">直接屏蔽</option>
                                <option value="review">人工审核</option>
                                <option value="warn">警告提示</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3 mt-6">
                        <button onclick="submitKeyword()" class="flex-1 bg-blue-600 hover:bg-blue-700 py-2 rounded-lg transition-colors">
                            添加
                        </button>
                        <button onclick="closeAddKeyword()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 rounded-lg transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 模拟监控日志数据
        const monitoringLogs = [
            {
                time: '2024-01-20 14:35:22',
                type: '关键词检测',
                content: '用户发布内容包含敏感词"刷单"',
                risk: 'medium',
                status: 'processing',
                userId: 'U001',
                contentId: 'C001'
            },
            {
                time: '2024-01-20 14:32:15',
                type: '图片审核',
                content: '检测到可能包含违规内容的图片',
                risk: 'high',
                status: 'pending',
                userId: 'U002',
                contentId: 'C002'
            },
            {
                time: '2024-01-20 14:28:45',
                type: '行为异常',
                content: '用户在1小时内发布了15条信息',
                risk: 'medium',
                status: 'resolved',
                userId: 'U003',
                contentId: 'C003'
            },
            {
                time: '2024-01-20 14:25:33',
                type: '关键词检测',
                content: '检测到联系方式信息',
                risk: 'low',
                status: 'resolved',
                userId: 'U004',
                contentId: 'C004'
            },
            {
                time: '2024-01-20 14:20:18',
                type: '图片审核',
                content: '图片中检测到二维码',
                risk: 'medium',
                status: 'processing',
                userId: 'U005',
                contentId: 'C005'
            }
        ];

        // 渲染监控日志
        function renderMonitoringLogs() {
            const tbody = document.getElementById('monitoringLogs');
            tbody.innerHTML = monitoringLogs.map(log => `
                <tr class="hover:bg-gray-700 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        ${log.time}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded ${
                            log.type === '关键词检测' ? 'bg-blue-600' :
                            log.type === '图片审核' ? 'bg-purple-600' : 'bg-orange-600'
                        }">
                            ${log.type}
                        </span>
                    </td>
                    <td class="px-6 py-4 text-sm">
                        <p class="max-w-xs truncate">${log.content}</p>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded ${
                            log.risk === 'high' ? 'risk-high' :
                            log.risk === 'medium' ? 'risk-medium' : 'risk-low'
                        }">
                            ${log.risk === 'high' ? '高风险' : log.risk === 'medium' ? '中风险' : '低风险'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="${
                            log.status === 'processing' ? 'status-processing' :
                            log.status === 'resolved' ? 'status-resolved' : 'status-pending'
                        }">
                            ${log.status === 'processing' ? '处理中' : log.status === 'resolved' ? '已处理' : '待处理'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <div class="flex space-x-2">
                            <button onclick="viewContentDetail('${log.contentId}')" 
                                    class="text-blue-400 hover:text-blue-300 transition-colors">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button onclick="processContent('${log.contentId}')" 
                                    class="text-green-400 hover:text-green-300 transition-colors">
                                <i data-lucide="check" class="w-4 h-4"></i>
                            </button>
                            <button onclick="blockContent('${log.contentId}')" 
                                    class="text-red-400 hover:text-red-300 transition-colors">
                                <i data-lucide="ban" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 查看内容详情
        function viewContentDetail(contentId) {
            const content = `
                <div class="space-y-4">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">检测结果</h4>
                        <p class="text-sm text-gray-400">检测到敏感关键词："刷单"、"兼职"</p>
                        <p class="text-sm text-gray-400">风险评分：75/100</p>
                    </div>
                    
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">原始内容</h4>
                        <p class="text-sm">招聘兼职人员，在家即可工作，日薪200-500元，无需经验，有手机即可。联系微信：xxxxx</p>
                    </div>
                    
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">用户信息</h4>
                        <p class="text-sm text-gray-400">用户ID：U001</p>
                        <p class="text-sm text-gray-400">注册时间：2024-01-15</p>
                        <p class="text-sm text-gray-400">历史违规：0次</p>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button onclick="approveContent()" class="flex-1 bg-green-600 hover:bg-green-700 py-2 rounded-lg transition-colors">
                            通过审核
                        </button>
                        <button onclick="rejectContent()" class="flex-1 bg-red-600 hover:bg-red-700 py-2 rounded-lg transition-colors">
                            拒绝发布
                        </button>
                    </div>
                </div>
            `;
            
            document.getElementById('contentDetailContent').innerHTML = content;
            document.getElementById('contentDetailModal').classList.remove('hidden');
        }

        // 关闭内容详情
        function closeContentDetail() {
            document.getElementById('contentDetailModal').classList.add('hidden');
        }

        // 添加关键词
        function addKeyword() {
            document.getElementById('addKeywordModal').classList.remove('hidden');
        }

        // 关闭添加关键词
        function closeAddKeyword() {
            document.getElementById('addKeywordModal').classList.add('hidden');
        }

        // 提交关键词
        function submitKeyword() {
            const keyword = document.getElementById('newKeyword').value;
            const risk = document.getElementById('keywordRisk').value;
            const action = document.getElementById('keywordAction').value;
            
            if (!keyword.trim()) {
                alert('请输入关键词');
                return;
            }
            
            alert(`关键词添加成功：\n关键词：${keyword}\n风险等级：${risk}\n处理方式：${action}`);
            closeAddKeyword();
        }

        // 处理内容
        function processContent(contentId) {
            alert(`处理内容：${contentId}`);
        }

        // 屏蔽内容
        function blockContent(contentId) {
            if (confirm('确定要屏蔽此内容吗？')) {
                alert(`内容 ${contentId} 已被屏蔽`);
            }
        }

        // 通过审核
        function approveContent() {
            alert('内容审核通过');
            closeContentDetail();
        }

        // 拒绝发布
        function rejectContent() {
            alert('内容已拒绝发布');
            closeContentDetail();
        }

        // 刷新日志
        function refreshLogs() {
            alert('日志已刷新');
            renderMonitoringLogs();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderMonitoringLogs();
        });
    </script>
</body>
</html>