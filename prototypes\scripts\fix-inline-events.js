/**
 * 内联事件修复工具
 * 
 * 用于将内联onclick事件转换为使用data-event属性
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('开始修复内联事件...');
    
    // 查找所有带有onclick属性的元素
    const elements = document.querySelectorAll('[onclick]');
    console.log(`找到 ${elements.length} 个带有内联onclick事件的元素`);
    
    elements.forEach((element, index) => {
        const onclickValue = element.getAttribute('onclick');
        
        // 移除onclick属性
        element.removeAttribute('onclick');
        
        // 常见的事件模式匹配
        let handler = '';
        let args = '';
        
        // 匹配 window.location.href='xxx' 模式
        if (onclickValue.includes('window.location.href=')) {
            const url = onclickValue.match(/window\.location\.href=['"]([^'"]+)['"]/)[1];
            handler = 'navigation.goToPage';
            args = url;
            
            // 设置data-event属性
            element.setAttribute('data-event', 'click');
            element.setAttribute('data-handler', handler);
            element.setAttribute('data-args', args);
        }
        // 匹配 contactShop('xxx') 模式
        else if (onclickValue.includes('contactShop(')) {
            const match = onclickValue.match(/contactShop\(['"]([^'"]+)['"]\)/);
            if (match) {
                handler = 'business.contactShop';
                args = match[1];
                
                // 设置data-event属性
                element.setAttribute('data-event', 'click');
                element.setAttribute('data-handler', handler);
                element.setAttribute('data-args', args);
            }
        }
        // 匹配 showMerchantGuide() 模式
        else if (onclickValue.includes('showMerchantGuide()')) {
            handler = 'business.showMerchantGuide';
            
            // 设置data-event属性
            element.setAttribute('data-event', 'click');
            element.setAttribute('data-handler', handler);
        }
        // 匹配 showComplaintGuide() 模式
        else if (onclickValue.includes('showComplaintGuide()')) {
            handler = 'business.showComplaintGuide';
            
            // 设置data-event属性
            element.setAttribute('data-event', 'click');
            element.setAttribute('data-handler', handler);
        }
        // 匹配 showServiceDisclaimer() 模式
        else if (onclickValue.includes('showServiceDisclaimer()')) {
            handler = 'business.showServiceDisclaimer';
            
            // 设置data-event属性
            element.setAttribute('data-event', 'click');
            element.setAttribute('data-handler', handler);
        }
        // 处理 event.stopPropagation() 情况
        else if (onclickValue.includes('event.stopPropagation()')) {
            // 提取实际的函数调用
            const functionCall = onclickValue.replace('event.stopPropagation(); ', '');
            
            // 递归处理
            const tempElement = document.createElement('div');
            tempElement.setAttribute('onclick', functionCall);
            document.body.appendChild(tempElement);
            
            // 修复后复制属性
            setTimeout(() => {
                if (tempElement.hasAttribute('data-event')) {
                    element.setAttribute('data-event', tempElement.getAttribute('data-event'));
                    element.setAttribute('data-handler', tempElement.getAttribute('data-handler'));
                    
                    if (tempElement.hasAttribute('data-args')) {
                        element.setAttribute('data-args', tempElement.getAttribute('data-args'));
                    }
                    
                    // 添加阻止冒泡
                    const originalHandler = element.getAttribute('data-handler');
                    if (originalHandler) {
                        element.setAttribute('data-stop-propagation', 'true');
                    }
                }
                
                // 移除临时元素
                document.body.removeChild(tempElement);
            }, 0);
        }
        // 其他未识别的模式
        else {
            console.warn(`未能识别的onclick模式: ${onclickValue}`);
            
            // 保留原始代码作为注释
            element.setAttribute('data-original-onclick', onclickValue);
        }
        
        console.log(`处理元素 ${index + 1}/${elements.length}: ${handler} ${args ? '(' + args + ')' : ''}`);
    });
    
    // 修改事件处理器，支持阻止冒泡
    if (window.eventHandlers) {
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (this.hasAttribute && this.hasAttribute('data-stop-propagation')) {
                const wrappedListener = function(event) {
                    event.stopPropagation();
                    return listener.apply(this, arguments);
                };
                
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            
            return originalAddEventListener.call(this, type, listener, options);
        };
    }
    
    console.log('内联事件修复完成');
}); 