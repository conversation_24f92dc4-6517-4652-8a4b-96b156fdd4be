<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>举报处理 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
        }
        .report-card {
            transition: all 0.2s ease;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech">
    <!-- 顶部导航栏 -->
    <nav class="bg-dark-card border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 tech-gradient rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">举</span>
                </div>
                <h1 class="text-xl font-bold">举报处理</h1>
                <span class="px-3 py-1 bg-red-500 bg-opacity-20 text-red-400 text-sm rounded-full">23 待处理</span>
            </div>
            <div class="flex items-center space-x-4">
                <button class="px-4 py-2 bg-tech-blue rounded-lg text-white text-sm hover:bg-opacity-80 transition-colors">
                    导出报告
                </button>
                <div class="w-8 h-8 bg-tech-purple rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">管</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="p-6">
        <!-- 筛选区域 -->
        <div class="bg-dark-card rounded-xl p-6 mb-6 border border-gray-700">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- 举报类型 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">举报类型</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="">全部类型</option>
                        <option value="spam">垃圾信息</option>
                        <option value="fraud">虚假信息</option>
                        <option value="inappropriate">不当内容</option>
                        <option value="harassment">骚扰行为</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <!-- 处理状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">处理状态</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="pending">待处理</option>
                        <option value="processing">处理中</option>
                        <option value="resolved">已解决</option>
                        <option value="rejected">已驳回</option>
                        <option value="all">全部状态</option>
                    </select>
                </div>

                <!-- 严重程度 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">严重程度</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="">全部程度</option>
                        <option value="high">严重</option>
                        <option value="medium">中等</option>
                        <option value="low">轻微</option>
                    </select>
                </div>

                <!-- 举报时间 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">举报时间</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="all">全部时间</option>
                    </select>
                </div>

                <!-- 搜索 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">搜索内容</label>
                    <div class="relative">
                        <input type="text" placeholder="搜索举报内容或用户" 
                               class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-tech-purple">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 举报统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">待处理</h3>
                    <div class="w-8 h-8 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">23</p>
            </div>

            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">今日处理</h3>
                    <div class="w-8 h-8 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">67</p>
            </div>

            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">解决率</h3>
                    <div class="w-8 h-8 bg-tech-blue bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-tech-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">92.3%</p>
            </div>

            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">平均处理时间</h3>
                    <div class="w-8 h-8 bg-tech-cyan bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-tech-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">4.2小时</p>
            </div>
        </div>

        <!-- 举报列表 -->
        <div class="space-y-4">
            <!-- 举报项1 -->
            <div class="report-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-white">虚假商铺信息举报</h3>
                                <span class="px-2 py-1 bg-red-500 bg-opacity-20 text-red-400 text-xs rounded-full">虚假信息</span>
                                <span class="px-2 py-1 bg-yellow-500 bg-opacity-20 text-yellow-400 text-xs rounded-full">待处理</span>
                                <span class="px-2 py-1 bg-red-500 bg-opacity-20 text-red-400 text-xs rounded-full">严重</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">被举报内容：张记小面馆 | 举报人：用户10005</p>
                            <p class="text-gray-400 text-sm leading-relaxed mb-3">举报理由：该商铺地址不存在，电话无法接通，疑似虚假信息。我按照地址去了现场，根本没有这家店，而且电话打了多次都是空号...</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span>举报时间：2024-01-15 09:30</span>
                                <span>被举报用户：张三 (ID: 10001)</span>
                                <span>举报次数：3次</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-400">
                        <p>处理期限</p>
                        <p class="text-red-400">剩余 2小时</p>
                    </div>
                </div>

                <!-- 相关证据 -->
                <div class="mb-4">
                    <p class="text-sm text-gray-400 mb-2">举报证据</p>
                    <div class="flex space-x-3">
                        <div class="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center">
                            <span class="text-gray-400 text-xs">现场照片</span>
                        </div>
                        <div class="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center">
                            <span class="text-gray-400 text-xs">通话记录</span>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-red-500 bg-opacity-20 border border-red-500 text-red-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                            </svg>
                            删除内容
                        </button>
                        <button class="px-4 py-2 bg-yellow-500 bg-opacity-20 border border-yellow-500 text-yellow-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            警告用户
                        </button>
                        <button class="px-4 py-2 bg-gray-500 bg-opacity-20 border border-gray-500 text-gray-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            驳回举报
                        </button>
                        <button class="px-4 py-2 bg-tech-blue bg-opacity-20 border border-tech-blue text-tech-blue rounded-lg hover:bg-opacity-30 transition-colors">
                            查看详情
                        </button>
                    </div>
                    <div class="text-sm text-gray-400">
                        处理人：<span class="text-tech-cyan">未分配</span>
                    </div>
                </div>
            </div>

            <!-- 举报项2 -->
            <div class="report-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-yellow-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-white">不当言论举报</h3>
                                <span class="px-2 py-1 bg-yellow-500 bg-opacity-20 text-yellow-500 text-xs rounded-full">不当内容</span>
                                <span class="px-2 py-1 bg-blue-500 bg-opacity-20 text-blue-400 text-xs rounded-full">处理中</span>
                                <span class="px-2 py-1 bg-yellow-500 bg-opacity-20 text-yellow-400 text-xs rounded-full">中等</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">被举报内容：招聘信息评论区 | 举报人：用户10008</p>
                            <p class="text-gray-400 text-sm leading-relaxed mb-3">举报理由：用户在招聘信息下发表不当言论，涉及歧视性内容，影响平台环境...</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span>举报时间：2024-01-15 11:15</span>
                                <span>被举报用户：李四 (ID: 10002)</span>
                                <span>举报次数：1次</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-400">
                        <p>处理期限</p>
                        <p class="text-green-400">剩余 18小时</p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-red-500 bg-opacity-20 border border-red-500 text-red-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                            </svg>
                            删除评论
                        </button>
                        <button class="px-4 py-2 bg-yellow-500 bg-opacity-20 border border-yellow-500 text-yellow-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            警告用户
                        </button>
                        <button class="px-4 py-2 bg-gray-500 bg-opacity-20 border border-gray-500 text-gray-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            驳回举报
                        </button>
                        <button class="px-4 py-2 bg-tech-blue bg-opacity-20 border border-tech-blue text-tech-blue rounded-lg hover:bg-opacity-30 transition-colors">
                            查看详情
                        </button>
                    </div>
                    <div class="text-sm text-gray-400">
                        处理人：<span class="text-tech-cyan">管理员A</span>
                    </div>
                </div>
            </div>

            <!-- 举报项3 -->
            <div class="report-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-white">垃圾信息举报</h3>
                                <span class="px-2 py-1 bg-orange-500 bg-opacity-20 text-orange-400 text-xs rounded-full">垃圾信息</span>
                                <span class="px-2 py-1 bg-green-500 bg-opacity-20 text-green-400 text-xs rounded-full">已解决</span>
                                <span class="px-2 py-1 bg-gray-500 bg-opacity-20 text-gray-400 text-xs rounded-full">轻微</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">被举报内容：重复发布相同信息 | 举报人：用户10012</p>
                            <p class="text-gray-400 text-sm leading-relaxed mb-3">举报理由：该用户在短时间内重复发布相同的出行信息，涉嫌刷屏...</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span>举报时间：2024-01-15 08:20</span>
                                <span>被举报用户：王五 (ID: 10003)</span>
                                <span>处理时间：2024-01-15 10:45</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-400">
                        <p>处理结果</p>
                        <p class="text-green-400">已删除重复内容</p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-tech-blue bg-opacity-20 border border-tech-blue text-tech-blue rounded-lg hover:bg-opacity-30 transition-colors">
                            查看处理记录
                        </button>
                        <span class="text-sm text-green-400">✓ 处理完成</span>
                    </div>
                    <div class="text-sm text-gray-400">
                        处理人：<span class="text-tech-cyan">管理员B</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-400">
                显示 1-3 条，共 23 条待处理举报
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">上一页</button>
                <button class="px-3 py-1 bg-tech-blue text-white text-sm rounded">1</button>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">2</button>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">3</button>
                <span class="text-gray-400">...</span>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">8</button>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">下一页</button>
            </div>
        </div>
    </div>

    <script>
        // 举报处理操作
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('删除内容') || button.textContent.includes('删除评论')) {
                button.addEventListener('click', function() {
                    if (confirm('确认删除此内容？此操作不可撤销。')) {
                        alert('内容已删除 - 实际开发中将更新数据库状态');
                        this.closest('.report-card').style.opacity = '0.5';
                    }
                });
            } else if (button.textContent.includes('警告用户')) {
                button.addEventListener('click', function() {
                    const warning = prompt('请输入警告内容：');
                    if (warning) {
                        alert(`警告已发送：${warning} - 实际开发中将通知用户`);
                    }
                });
            } else if (button.textContent.includes('驳回举报')) {
                button.addEventListener('click', function() {
                    const reason = prompt('请输入驳回理由：');
                    if (reason) {
                        alert(`举报已驳回，理由：${reason} - 实际开发中将更新状态`);
                        this.closest('.report-card').style.opacity = '0.5';
                    }
                });
            } else if (button.textContent.includes('查看详情') || button.textContent.includes('查看处理记录')) {
                button.addEventListener('click', function() {
                    alert('打开详情页面 - 实际开发中将显示完整举报信息');
                });
            }
        });

        // 导出报告
        document.querySelector('nav button').addEventListener('click', function() {
            alert('导出举报处理报告 - 实际开发中将生成Excel或PDF文件');
        });
    </script>
</body>
</html>