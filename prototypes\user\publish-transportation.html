<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>发布出行信息 - 本地助手</title>
    <meta name="description" content="发布出行信息，寻找拼车伙伴，货运服务，代驾服务">
    <meta name="keywords" content="出行,拼车,货运,代驾,本地助手">
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://unpkg.com">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="../styles/design-system.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <style>
        /* 基础样式重置 */
        * {
            box-sizing: border-box;
        }
        
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            font-family: var(--font-family-sans);
            background-color: var(--color-bg-primary);
            color: var(--color-text-primary);
        }
        
        /* 表单增强样式 */
        .form-input {
            transition: all var(--duration-normal) ease;
            min-height: 44px; /* 最小触摸目标 */
        }
        
        .form-input:focus {
            box-shadow: 0 0 0 3px var(--color-focus-ring);
            outline: none;
        }
        
        .form-input:invalid {
            border-color: var(--color-error);
        }
        
        /* 无障碍增强 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        /* 响应式优化 */
        @media (max-width: 640px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
        
        /* 动画优化 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            .form-input {
                border-width: 2px;
            }
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <header class="bg-gradient-to-r from-blue-500 to-cyan-500 p-4" role="banner">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button 
                type="button"
                onclick="history.back()" 
                class="p-2 hover:bg-white/20 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 min-w-[44px] min-h-[44px] flex items-center justify-center"
                aria-label="返回上一页"
                title="返回上一页">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white" aria-hidden="true"></i>
            </button>
            <h1 class="text-xl font-semibold text-white flex-1">发布出行信息</h1>
            <button 
                type="button"
                id="helpBtn"
                class="p-2 hover:bg-white/20 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 min-w-[44px] min-h-[44px] flex items-center justify-center"
                aria-label="查看帮助信息"
                title="查看帮助信息">
                <i data-lucide="help-circle" class="w-6 h-6 text-white" aria-hidden="true"></i>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-md mx-auto p-4 pb-24" role="main">
        <form id="transportForm" class="space-y-6" novalidate>
            <!-- 出行类型选择 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="block text-sm font-medium text-slate-300 mb-3">
                    出行类型 <span class="text-red-400" aria-label="必填项">*</span>
                </legend>
                <div class="flex flex-wrap gap-2" role="radiogroup" aria-labelledby="transport-type-legend">
                    <label class="type-option cursor-pointer">
                        <input type="radio" name="transportType" value="people-find-car" class="sr-only" required>
                        <span class="type-btn block px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap min-h-[44px] flex items-center justify-center focus-within:ring-2 focus-within:ring-blue-500">
                            <i data-lucide="users" class="w-4 h-4 mr-2" aria-hidden="true"></i>
                            人找车
                        </span>
                    </label>
                    <label class="type-option cursor-pointer">
                        <input type="radio" name="transportType" value="car-find-people" class="sr-only" required>
                        <span class="type-btn block px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap min-h-[44px] flex items-center justify-center focus-within:ring-2 focus-within:ring-blue-500">
                            <i data-lucide="car" class="w-4 h-4 mr-2" aria-hidden="true"></i>
                            车找人
                        </span>
                    </label>
                    <label class="type-option cursor-pointer">
                        <input type="radio" name="transportType" value="cargo-find-car" class="sr-only" required>
                        <span class="type-btn block px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap min-h-[44px] flex items-center justify-center focus-within:ring-2 focus-within:ring-blue-500">
                            <i data-lucide="package" class="w-4 h-4 mr-2" aria-hidden="true"></i>
                            货找车
                        </span>
                    </label>
                    <label class="type-option cursor-pointer">
                        <input type="radio" name="transportType" value="car-find-cargo" class="sr-only" required>
                        <span class="type-btn block px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap min-h-[44px] flex items-center justify-center focus-within:ring-2 focus-within:ring-blue-500">
                            <i data-lucide="truck" class="w-4 h-4 mr-2" aria-hidden="true"></i>
                            车找货
                        </span>
                    </label>
                    <label class="type-option cursor-pointer">
                        <input type="radio" name="transportType" value="driver-service" class="sr-only" required>
                        <span class="type-btn block px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm font-medium hover:bg-slate-600 transition-colors whitespace-nowrap min-h-[44px] flex items-center justify-center focus-within:ring-2 focus-within:ring-blue-500">
                            <i data-lucide="user-check" class="w-4 h-4 mr-2" aria-hidden="true"></i>
                            代驾
                        </span>
                    </label>
                </div>
                <div class="error-message hidden mt-2 text-sm text-red-400" role="alert" aria-live="polite"></div>
            </fieldset>

            <!-- 路线信息 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="block text-sm font-medium text-slate-300 mb-3">
                    路线信息 <span class="text-red-400" aria-label="必填项">*</span>
                </legend>
                <div class="route-input space-y-4">
                    <div class="relative">
                        <label for="startLocation" class="sr-only">出发地点</label>
                        <div class="absolute left-4 top-4 w-3 h-3 bg-blue-500 rounded-full z-10" aria-hidden="true"></div>
                        <input 
                            type="text" 
                            id="startLocation"
                            name="startLocation" 
                            placeholder="出发地点" 
                            required
                            autocomplete="address-line1"
                            class="form-input w-full pl-12 pr-12 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                            aria-describedby="startLocation-error">
                        <button 
                            type="button" 
                            class="absolute right-3 top-3 p-1 text-slate-400 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded min-w-[32px] min-h-[32px] flex items-center justify-center"
                            aria-label="选择出发地点"
                            title="选择出发地点">
                            <i data-lucide="map-pin" class="w-5 h-5" aria-hidden="true"></i>
                        </button>
                        <div id="startLocation-error" class="error-message hidden mt-1 text-sm text-red-400" role="alert" aria-live="polite"></div>
                    </div>
                    
                    <div class="relative">
                        <label for="endLocation" class="sr-only">目的地点</label>
                        <div class="absolute left-4 top-4 w-3 h-3 bg-cyan-500 rounded-full z-10" aria-hidden="true"></div>
                        <input 
                            type="text" 
                            id="endLocation"
                            name="endLocation" 
                            placeholder="目的地点" 
                            required
                            autocomplete="address-line2"
                            class="form-input w-full pl-12 pr-12 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                            aria-describedby="endLocation-error">
                        <button 
                            type="button" 
                            class="absolute right-3 top-3 p-1 text-slate-400 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded min-w-[32px] min-h-[32px] flex items-center justify-center"
                            aria-label="选择目的地点"
                            title="选择目的地点">
                            <i data-lucide="map-pin" class="w-5 h-5" aria-hidden="true"></i>
                        </button>
                        <div id="endLocation-error" class="error-message hidden mt-1 text-sm text-red-400" role="alert" aria-live="polite"></div>
                    </div>
                    
                    <div class="mt-4">
                        <label for="waypoints" class="block text-xs text-slate-400 mb-1">途径地点（可选）</label>
                        <input 
                            type="text" 
                            id="waypoints"
                            name="waypoints" 
                            placeholder="请输入途径地点，如：深大地铁站、科技园地铁站" 
                            class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                        <small class="text-xs text-slate-500 mt-1 block">
                            <i data-lucide="info" class="w-3 h-3 inline mr-1" aria-hidden="true"></i>
                            多个地点请用逗号分隔
                        </small>
                    </div>
                    
                    <!-- 服务范围选择 -->
                    <fieldset class="bg-slate-700/50 rounded-lg p-3 border border-slate-600">
                        <legend class="block text-sm font-medium text-slate-200 mb-3 flex items-center space-x-2">
                            <i data-lucide="map" class="w-4 h-4 text-blue-400" aria-hidden="true"></i>
                            <span>服务范围 <span class="text-red-400" aria-label="必填项">*</span></span>
                        </legend>
                        <div class="grid grid-cols-2 gap-3" role="radiogroup" aria-labelledby="area-type-legend">
                            <label class="relative cursor-pointer group focus-within:ring-2 focus-within:ring-blue-500 rounded-lg">
                                <input type="radio" id="area-urban" name="areaType" value="urban" class="sr-only" checked required>
                                <div class="flex items-center justify-center space-x-2 p-3 bg-green-500/20 border-2 border-green-500 rounded-lg transition-all group-hover:bg-green-500/30 min-h-[44px]">
                                    <i data-lucide="building" class="w-4 h-4 text-green-400" aria-hidden="true"></i>
                                    <span class="text-sm font-medium text-green-400">城区</span>
                                </div>
                                <div class="absolute top-1 right-1 w-2 h-2 bg-green-500 rounded-full" aria-hidden="true"></div>
                            </label>
                            <label class="relative cursor-pointer group focus-within:ring-2 focus-within:ring-blue-500 rounded-lg">
                                <input type="radio" id="area-cross-urban" name="areaType" value="cross-urban" class="sr-only">
                                <div class="flex items-center justify-center space-x-2 p-3 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all group-hover:bg-slate-500 min-h-[44px]">
                                    <i data-lucide="map-pin" class="w-4 h-4 text-slate-400" aria-hidden="true"></i>
                                    <span class="text-sm text-slate-400">跨城区</span>
                                </div>
                            </label>
                        </div>
                        <p class="text-xs text-slate-400 mt-2" role="note">
                            <i data-lucide="info" class="w-3 h-3 inline mr-1" aria-hidden="true"></i>
                            城区：本地短途服务 | 跨城区：长途及乡镇服务
                        </p>
                    </fieldset>
                    
                    <!-- 线路类型选择 -->
                    <fieldset class="bg-slate-700/50 rounded-lg p-3 border border-slate-600">
                        <legend class="block text-sm font-medium text-slate-200 mb-3 flex items-center space-x-2">
                            <i data-lucide="route" class="w-4 h-4 text-orange-400" aria-hidden="true"></i>
                            <span>线路类型 <span class="text-red-400" aria-label="必填项">*</span></span>
                        </legend>
                        <div class="grid grid-cols-2 gap-3" role="radiogroup" aria-labelledby="route-type-legend">
                            <label class="relative cursor-pointer group focus-within:ring-2 focus-within:ring-blue-500 rounded-lg">
                                <input type="radio" id="route-flexible" name="routeType" value="flexible" class="sr-only" checked required>
                                <div class="flex items-center justify-center space-x-2 p-3 bg-orange-500/20 border-2 border-orange-500 rounded-lg transition-all group-hover:bg-orange-500/30 min-h-[44px]">
                                    <i data-lucide="zap" class="w-4 h-4 text-orange-400" aria-hidden="true"></i>
                                    <span class="text-sm font-medium text-orange-400">非固定线路</span>
                                </div>
                                <div class="absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full" aria-hidden="true"></div>
                            </label>
                            <label class="relative cursor-pointer group focus-within:ring-2 focus-within:ring-blue-500 rounded-lg">
                                <input type="radio" id="route-fixed" name="routeType" value="fixed" class="sr-only">
                                <div class="flex items-center justify-center space-x-2 p-3 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all group-hover:bg-slate-500 min-h-[44px]">
                                    <i data-lucide="repeat" class="w-4 h-4 text-slate-400" aria-hidden="true"></i>
                                    <span class="text-sm text-slate-400">固定线路</span>
                                </div>
                            </label>
                        </div>
                        <p class="text-xs text-slate-400 mt-2" role="note">
                            <i data-lucide="info" class="w-3 h-3 inline mr-1" aria-hidden="true"></i>
                            非固定：个人灵活出行 | 固定：企业定期班车
                        </p>
                    </fieldset>
                    

                </div>
            </div>

            <!-- 出行时间 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="block text-sm font-medium text-slate-300 mb-3">
                    出行时间 <span class="text-red-400" aria-label="必填项">*</span>
                </legend>
                <div class="space-y-3">
                    <div>
                        <label for="travelDate" class="block text-xs text-slate-400 mb-2">出行日期</label>
                        <input 
                            type="date" 
                            id="travelDate"
                            name="travelDate" 
                            required
                            class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                            aria-describedby="travelDate-error">
                        <div id="travelDate-error" class="error-message hidden mt-1 text-sm text-red-400" role="alert" aria-live="polite"></div>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label for="startTime" class="block text-xs text-slate-400 mb-2">开始时间</label>
                            <input 
                                type="time" 
                                id="startTime"
                                name="startTime" 
                                required
                                class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                                aria-describedby="startTime-error">
                            <div id="startTime-error" class="error-message hidden mt-1 text-sm text-red-400" role="alert" aria-live="polite"></div>
                        </div>
                        <div>
                            <label for="endTime" class="block text-xs text-slate-400 mb-2">结束时间</label>
                            <input 
                                type="time" 
                                id="endTime"
                                name="endTime" 
                                class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                                aria-describedby="endTime-help">
                            <small id="endTime-help" class="text-xs text-slate-500 mt-1 block">可选，不填表示全天</small>
                        </div>
                    </div>
                    <p class="text-xs text-slate-400 mt-2" role="note">
                        <i data-lucide="info" class="w-3 h-3 inline mr-1" aria-hidden="true"></i>
                        到达结束时间后，信息将自动删除
                    </p>
                </div>
            </fieldset>



            <!-- 价格设置 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="block text-sm font-medium text-slate-300 mb-3">价格设置</legend>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label for="price" class="block text-xs text-slate-400 mb-1">单价</label>
                        <div class="relative">
                            <input 
                                type="number" 
                                id="price"
                                name="price" 
                                placeholder="0" 
                                step="0.1" 
                                min="0"
                                class="form-input w-full pl-4 pr-8 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                                aria-describedby="price-help">
                            <span class="absolute right-3 top-3 text-slate-400 text-sm" aria-hidden="true">元</span>
                        </div>
                        <small id="price-help" class="text-xs text-slate-500 mt-1 block">输入0表示免费</small>
                    </div>
                    <div>
                        <label for="priceTypeSelect" class="block text-xs text-slate-400 mb-1">计费方式</label>
                        <select 
                            name="priceType" 
                            id="priceTypeSelect" 
                            class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                            aria-describedby="priceType-help">
                            <option value="negotiate">面议</option>
                            <option value="per_person">每人</option>
                            <option value="per_seat">空位</option>
                            <option value="freight">运费</option>
                            <option value="per_trip">每趟</option>
                            <option value="per_km">每公里</option>
                        </select>
                        <small id="priceType-help" class="text-xs text-slate-500 mt-1 block">选择合适的计费方式</small>
                    </div>
                </div>
            </fieldset>

            <!-- 联系方式 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="block text-sm font-medium text-slate-300 mb-3">
                    联系方式 <span class="text-red-400" aria-label="必填项">*</span>
                </legend>
                <div class="space-y-4">
                    <div>
                        <label for="contact-phone" class="sr-only">手机号码</label>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg" aria-hidden="true">
                                <i data-lucide="phone" class="w-5 h-5 text-slate-400"></i>
                            </div>
                            <input 
                                type="tel" 
                                id="contact-phone" 
                                name="phone" 
                                placeholder="请输入手机号码"
                                required
                                pattern="[0-9]{11}"
                                maxlength="11"
                                class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                                aria-describedby="phone-error phone-help">
                        </div>
                        <small id="phone-help" class="text-xs text-slate-500 mt-1 block ml-13">请输入11位手机号码</small>
                        <div id="phone-error" class="error-message hidden mt-1 text-sm text-red-400 ml-13" role="alert" aria-live="polite"></div>
                    </div>
                    <div>
                        <label for="sms-code" class="sr-only">短信验证码</label>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg" aria-hidden="true">
                                <i data-lucide="message-square" class="w-5 h-5 text-slate-400"></i>
                            </div>
                            <input 
                                type="text" 
                                id="sms-code" 
                                name="smsCode" 
                                placeholder="请输入短信验证码"
                                required
                                pattern="[0-9]{4,6}"
                                maxlength="6"
                                class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                                aria-describedby="sms-error sms-help">
                            <button 
                                type="button" 
                                id="send-sms-btn" 
                                class="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors flex items-center justify-center font-semibold whitespace-nowrap min-w-[100px] min-h-[44px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                                aria-describedby="sms-btn-help">
                                发送验证码
                            </button>
                        </div>
                        <small id="sms-help" class="text-xs text-slate-500 mt-1 block ml-13">请输入4-6位数字验证码</small>
                        <div id="sms-error" class="error-message hidden mt-1 text-sm text-red-400 ml-13" role="alert" aria-live="polite"></div>
                        <small id="sms-btn-help" class="text-xs text-slate-500 mt-1 block ml-13">点击发送验证码到手机</small>
                    </div>
                </div>
            </fieldset>

            <!-- 体验券设置 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="sr-only">体验券设置</legend>
                <div class="space-y-3">
                    <label class="flex items-center justify-between cursor-pointer min-h-[44px]" for="enableVoucher">
                        <span class="text-sm text-slate-300">提供体验券</span>
                        <div class="relative">
                            <input 
                                type="checkbox" 
                                id="enableVoucher"
                                name="enableVoucher" 
                                class="sr-only" 
                                onchange="toggleVoucherSettings(this)"
                                aria-describedby="voucher-help">
                            <div class="w-11 h-6 bg-slate-600 rounded-full shadow-inner transition-colors duration-300 ease-in-out voucher-switch focus-within:ring-2 focus-within:ring-blue-500">
                                <div class="w-4 h-4 bg-white rounded-full shadow transform transition-transform duration-300 ease-in-out translate-x-1 mt-1 voucher-toggle"></div>
                            </div>
                        </div>
                    </label>
                    <small id="voucher-help" class="text-xs text-slate-500 block">开启后可设置体验券优惠信息</small>
                    <div id="voucherSettings" class="hidden space-y-3 pl-6" role="group" aria-labelledby="voucher-group-label">
                        <h4 id="voucher-group-label" class="sr-only">体验券详细设置</h4>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label for="voucherCount" class="block text-xs text-slate-400 mb-1">
                                    体验券数量 <span class="text-red-400" aria-label="必填项">*</span>
                                </label>
                                <input 
                                    type="number" 
                                    id="voucherCount"
                                    name="voucherCount" 
                                    value="1" 
                                    min="1" 
                                    max="10" 
                                    class="form-input w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
                                    aria-describedby="count-help-voucher">
                                <small id="count-help-voucher" class="text-xs text-slate-500 mt-1 block">1-10张</small>
                            </div>
                            <div>
                                <label for="voucherPrice" class="block text-xs text-slate-400 mb-1">
                                    体验价格 <span class="text-red-400" aria-label="必填项">*</span>
                                </label>
                                <div class="relative">
                                    <input 
                                        type="number" 
                                        id="voucherPrice"
                                        name="voucherPrice" 
                                        placeholder="0" 
                                        step="0.1" 
                                        min="0"
                                        class="form-input w-full pl-3 pr-8 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                                        aria-describedby="price-help-voucher">
                                    <span class="absolute right-2 top-2 text-slate-400 text-xs" aria-hidden="true">元</span>
                                </div>
                                <small id="price-help-voucher" class="text-xs text-slate-500 mt-1 block">支持小数</small>
                            </div>
                        </div>
                        <div>
                            <label for="voucherDesc" class="block text-xs text-slate-400 mb-1">体验券说明</label>
                            <input 
                                type="text" 
                                id="voucherDesc"
                                name="voucherDesc" 
                                placeholder="如：首次体验优惠、新用户专享等" 
                                maxlength="50"
                                class="form-input w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                                aria-describedby="desc-help-voucher">
                            <small id="desc-help-voucher" class="text-xs text-slate-500 mt-1 block">最多50个字符，用于说明体验券用途</small>
                        </div>
                    </div>
                </div>
            </fieldset>

            <!-- 备注说明 -->
            <fieldset class="bg-slate-800 rounded-xl p-4">
                <legend class="sr-only">备注说明</legend>
                <label for="notes" class="block text-sm font-medium text-slate-300 mb-2">备注说明</label>
                <textarea 
                    id="notes"
                    name="notes" 
                    rows="3" 
                    placeholder="其他说明信息，如：途经路线、特殊要求等" 
                    maxlength="200"
                    class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"
                    aria-describedby="notes-help"></textarea>
                <small id="notes-help" class="text-xs text-slate-500 mt-1 block">最多200个字符，可选填写</small>
            </fieldset>



        </form>
    </div>

    <!-- 底部操作栏 -->
    <footer class="fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700 p-4" role="contentinfo">
        <!-- 发布按钮 -->
        <div class="max-w-md mx-auto space-y-3">
            <div class="flex space-x-3" role="group" aria-label="表单操作">
                <button 
                    type="button" 
                    id="save-draft-btn"
                    class="flex-1 py-3 bg-slate-600 text-white rounded-lg font-medium hover:bg-slate-500 transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-slate-400"
                    aria-describedby="draft-help">
                    <i data-lucide="save" class="w-4 h-4 inline mr-2" aria-hidden="true"></i>
                    保存草稿
                </button>
                <button 
                    type="submit" 
                    form="transportForm" 
                    id="publish-btn"
                    class="flex-1 py-3 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-600 transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-purple-400"
                    aria-describedby="publish-help">
                    <i data-lucide="send" class="w-4 h-4 inline mr-2" aria-hidden="true"></i>
                    发布出行
                </button>
            </div>
            <div class="text-xs text-slate-500 text-center space-y-1">
                <div id="draft-help">保存草稿：暂存信息，稍后继续编辑</div>
                <div id="publish-help">发布出行：立即发布到平台供其他用户查看</div>
            </div>
        </div>
    </footer>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
        
        // 出行类型切换
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 重置所有按钮状态
                document.querySelectorAll('.type-btn').forEach(b => {
                    b.classList.remove('bg-blue-500', 'text-white');
                    b.classList.add('bg-slate-700', 'text-slate-300');
                });
                
                // 设置当前按钮为选中状态
                this.classList.remove('bg-slate-700', 'text-slate-300');
                this.classList.add('bg-blue-500', 'text-white');
                
                // 设置隐藏字段值
                document.querySelector('input[name="transportType"]').value = this.dataset.type;
                
                // 根据出行类型更新计费方式选项
                updatePriceTypeOptions(this.dataset.type);
            });
        });
        
        // 根据出行类型更新计费方式选项
        function updatePriceTypeOptions(transportType) {
            const priceTypeSelect = document.getElementById('priceTypeSelect');
            priceTypeSelect.innerHTML = '';
            
            // 默认添加面议选项
            priceTypeSelect.innerHTML += '<option value="negotiate">面议</option>';
            
            switch(transportType) {
                case 'people-find-car':
                    priceTypeSelect.innerHTML += '<option value="per_person">每人</option>';
                    priceTypeSelect.innerHTML += '<option value="per_km">每公里</option>';
                    priceTypeSelect.innerHTML += '<option value="per_trip">每趟</option>';
                    break;
                case 'car-find-people':
                    priceTypeSelect.innerHTML += '<option value="per_seat">空位</option>';
                    priceTypeSelect.innerHTML += '<option value="per_km">每公里</option>';
                    priceTypeSelect.innerHTML += '<option value="per_trip">每趟</option>';
                    break;
                case 'cargo-find-car':
                    priceTypeSelect.innerHTML += '<option value="freight">运费</option>';
                    priceTypeSelect.innerHTML += '<option value="per_km">每公里</option>';
                    priceTypeSelect.innerHTML += '<option value="per_trip">每趟</option>';
                    break;
                case 'car-find-cargo':
                    priceTypeSelect.innerHTML += '<option value="per_trip">每趟</option>';
                    priceTypeSelect.innerHTML += '<option value="per_km">每公里</option>';
                    priceTypeSelect.innerHTML += '<option value="freight">运费</option>';
                    break;
                case 'driver-service':
                    priceTypeSelect.innerHTML += '<option value="per_trip">每趟</option>';
                    priceTypeSelect.innerHTML += '<option value="per_km">每公里</option>';
                    break;
                default:
                    priceTypeSelect.innerHTML += '<option value="per_person">每人</option>';
                    priceTypeSelect.innerHTML += '<option value="per_seat">空位</option>';
                    priceTypeSelect.innerHTML += '<option value="freight">运费</option>';
                    priceTypeSelect.innerHTML += '<option value="per_trip">每趟</option>';
                    priceTypeSelect.innerHTML += '<option value="per_km">每公里</option>';
            }
        }
        
        // 选择计费方式
        function selectPriceType(type, element) {
            // 重置所有计费方式选项
            const container = element.closest('.grid');
            container.querySelectorAll('label').forEach(label => {
                const input = label.querySelector('input');
                const circle = label.querySelector('div');
                const dot = circle.querySelector('div');
                const text = label.querySelector('span');
                
                input.checked = false;
                circle.className = 'w-4 h-4 border-2 border-slate-500 rounded-full flex items-center justify-center';
                dot.className = 'w-2 h-2 bg-transparent rounded-full';
                text.className = 'text-slate-400';
            });
            
            // 设置选中状态
            const input = element.querySelector('input');
            const circle = element.querySelector('div');
            const dot = circle.querySelector('div');
            const text = element.querySelector('span');
            
            input.checked = true;
            circle.className = 'w-4 h-4 border-2 border-orange-500 rounded-full flex items-center justify-center';
            dot.className = 'w-2 h-2 bg-orange-500 rounded-full';
            text.className = 'text-slate-300';
        }
        
        // 选择服务范围
        function selectServiceRange(range, element) {
            // 重置所有服务范围按钮
            document.querySelectorAll('[onclick^="selectServiceRange"]').forEach(btn => {
                btn.classList.remove('bg-emerald-500', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            // 设置当前按钮为选中状态
            element.classList.remove('bg-slate-700', 'text-slate-300');
            element.classList.add('bg-emerald-500', 'text-white');
            
            console.log('选择服务范围:', range);
        }
        
        // 选择线路类型
        function selectRouteType(type, element) {
            // 重置所有线路类型按钮
            document.querySelectorAll('[onclick^="selectRouteType"]').forEach(btn => {
                btn.classList.remove('bg-orange-500', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            // 设置当前按钮为选中状态
            element.classList.remove('bg-slate-700', 'text-slate-300');
            element.classList.add('bg-orange-500', 'text-white');
            
            console.log('选择线路类型:', type);
        }
        

        
        // 服务范围选择交互
        document.querySelectorAll('label.relative.cursor-pointer.group').forEach(label => {
            label.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                    
                    // 触发change事件
                    const event = new Event('change', { bubbles: true });
                    radio.dispatchEvent(event);
                }
            });
        });
        
        document.querySelectorAll('input[name="areaType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 重置所有选项样式
                document.querySelectorAll('input[name="areaType"]').forEach(r => {
                    const container = r.nextElementSibling;
                    const indicator = r.parentElement.querySelector('.absolute');
                    if (r.checked) {
                        if (r.value === 'urban') {
                            container.className = 'flex items-center justify-center space-x-2 p-3 bg-green-500/20 border-2 border-green-500 rounded-lg transition-all group-hover:bg-green-500/30';
                            container.querySelector('i').className = 'w-4 h-4 text-green-400';
                            container.querySelector('span').className = 'text-sm font-medium text-green-400';
                            indicator.className = 'absolute top-1 right-1 w-2 h-2 bg-green-500 rounded-full';
                        } else {
                            container.className = 'flex items-center justify-center space-x-2 p-3 bg-blue-500/20 border-2 border-blue-500 rounded-lg transition-all group-hover:bg-blue-500/30';
                            container.querySelector('i').className = 'w-4 h-4 text-blue-400';
                            container.querySelector('span').className = 'text-sm font-medium text-blue-400';
                            indicator.className = 'absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full';
                        }
                    } else {
                        container.className = 'flex items-center justify-center space-x-2 p-3 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all group-hover:bg-slate-500';
                        container.querySelector('i').className = 'w-4 h-4 text-slate-400';
                        container.querySelector('span').className = 'text-sm text-slate-400';
                        indicator.className = 'absolute top-1 right-1 w-2 h-2 bg-transparent rounded-full';
                    }
                });
            });
        });
        
        // 线路类型选择交互
        document.querySelectorAll('input[name="routeType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 重置所有选项样式
                document.querySelectorAll('input[name="routeType"]').forEach(r => {
                    const container = r.nextElementSibling;
                    const indicator = r.parentElement.querySelector('.absolute');
                    if (r.checked) {
                        if (r.value === 'flexible') {
                            container.className = 'flex items-center justify-center space-x-2 p-3 bg-orange-500/20 border-2 border-orange-500 rounded-lg transition-all group-hover:bg-orange-500/30';
                            container.querySelector('i').className = 'w-4 h-4 text-orange-400';
                            container.querySelector('span').className = 'text-sm font-medium text-orange-400';
                            indicator.className = 'absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full';
                        } else {
                            container.className = 'flex items-center justify-center space-x-2 p-3 bg-purple-500/20 border-2 border-purple-500 rounded-lg transition-all group-hover:bg-purple-500/30';
                            container.querySelector('i').className = 'w-4 h-4 text-purple-400';
                            container.querySelector('span').className = 'text-sm font-medium text-purple-400';
                            indicator.className = 'absolute top-1 right-1 w-2 h-2 bg-purple-500 rounded-full';
                        }
                    } else {
                        container.className = 'flex items-center justify-center space-x-2 p-3 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all group-hover:bg-slate-500';
                        container.querySelector('i').className = 'w-4 h-4 text-slate-400';
                        container.querySelector('span').className = 'text-sm text-slate-400';
                        indicator.className = 'absolute top-1 right-1 w-2 h-2 bg-transparent rounded-full';
                    }
                });
            });
        });
        
        // 人数增减按钮
        document.querySelectorAll('.capacity-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const input = document.querySelector('input[name="passengerCount"]');
                const action = this.dataset.action;
                let value = parseInt(input.value);
                
                if (action === 'increase' && value < 8) {
                    input.value = value + 1;
                } else if (action === 'decrease' && value > 1) {
                    input.value = value - 1;
                }
            });
        });
        
        // 体验券设置开关
        document.querySelector('input[name="enableVoucher"]').addEventListener('change', function() {
            const settings = document.getElementById('voucherSettings');
            if (this.checked) {
                settings.classList.remove('hidden');
            } else {
                settings.classList.add('hidden');
            }
        });
        
        // 设置默认日期为今天
        const today = new Date().toISOString().split('T')[0];
        document.querySelector('input[name="travelDate"]').value = today;
        
        // 验证码生成和刷新
        let currentVerifyCode = 'D9K2';
        
        function generateVerifyCode() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 4; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }
        
        function refreshVerifyCode() {
            currentVerifyCode = generateVerifyCode();
            document.getElementById('verifyCodeDisplay').textContent = currentVerifyCode;
        }
        
        // 表单提交处理
        document.getElementById('transportForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证必填字段
            const requiredFields = ['startLocation', 'endLocation', 'travelDate', 'travelTime', 'phone', 'verifyCode'];
            let isValid = true;
            
            requiredFields.forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('border-red-500');
                } else {
                    input.classList.remove('border-red-500');
                }
            });
            
            // 验证容量信息
            const transportType = document.querySelector('input[name="transportType"]:checked').value;
            if (transportType === 'delivery') {
                const cargoWeight = document.querySelector('input[name="cargoWeight"]');
                if (!cargoWeight.value.trim()) {
                    isValid = false;
                    cargoWeight.classList.add('border-red-500');
                }
            } else {
                const passengerCount = document.querySelector('input[name="passengerCount"]');
                if (!passengerCount.value || passengerCount.value < 1) {
                    isValid = false;
                    passengerCount.classList.add('border-red-500');
                }
            }
            
            // 验证码检查
            const verifyCodeInput = document.querySelector('[name="verifyCode"]');
            if (verifyCodeInput.value.toUpperCase() !== currentVerifyCode) {
                alert('验证码错误，请重新输入');
                verifyCodeInput.classList.add('border-red-500');
                refreshVerifyCode();
                return;
            }
            
            if (!isValid) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 模拟发布流程
            const submitButton = document.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = '发布中...';
            
            // 模拟API提交
            setTimeout(() => {
                // 收集表单数据
                const transportType = document.querySelector('input[name="transportType"]:checked').value;
                const formData = {
                    transportType: transportType,
                    startLocation: document.querySelector('[name="startLocation"]').value,
                    endLocation: document.querySelector('[name="endLocation"]').value,
                    travelDate: document.querySelector('[name="travelDate"]').value,
                    travelTime: document.querySelector('[name="travelTime"]').value,
                    phone: document.querySelector('[name="phone"]').value,
                    notes: document.querySelector('[name="notes"]').value,
                    timestamp: new Date().toISOString()
                };
                
                // 添加容量信息
                if (transportType === 'delivery') {
                    formData.cargoWeight = document.querySelector('[name="cargoWeight"]').value;
                    formData.cargoType = document.querySelector('[name="cargoType"]').value;
                } else {
                    formData.passengerCount = document.querySelector('[name="passengerCount"]').value;
                }
                
                console.log('出行信息已提交:', formData);
                
                // 显示成功提示
                alert('出行信息发布成功！');
                
                // 跳转到出行列表页面
                window.location.href = 'transportation.html';
            }, 1500);
        });
        
        // 分享出行信息功能
        function shareTransportInfo() {
            const transportType = document.querySelector('input[name="transportType"]:checked');
            const startLocation = document.querySelector('input[name="startLocation"]').value;
            const endLocation = document.querySelector('input[name="endLocation"]').value;
            const travelDate = document.querySelector('input[name="travelDate"]').value;
            const travelTime = document.querySelector('input[name="travelTime"]').value;
            
            if (!startLocation || !endLocation || !travelDate || !travelTime) {
                alert('请先完善出行信息再分享');
                return;
            }
            
            const shareText = `【出行信息分享】\n类型：${transportType ? transportType.nextElementSibling.textContent : ''}\n路线：${startLocation} → ${endLocation}\n时间：${travelDate} ${travelTime}\n\n快来一起出行吧！`;
            
            if (navigator.share) {
                navigator.share({
                    title: '出行信息分享',
                    text: shareText
                }).catch(console.error);
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('出行信息已复制到剪贴板，可以分享给朋友了！');
                }).catch(() => {
                    alert('分享内容：\n' + shareText);
                });
            }
        }
        
        // 切换体验券设置显示
        function toggleVoucherSettings(checkbox) {
            const voucherSettings = document.getElementById('voucherSettings');
            const switchElement = checkbox.parentElement.querySelector('.voucher-switch');
            const toggleElement = checkbox.parentElement.querySelector('.voucher-toggle');
            
            if (checkbox.checked) {
                voucherSettings.classList.remove('hidden');
                switchElement.classList.add('bg-blue-500');
                switchElement.classList.remove('bg-slate-600');
                toggleElement.classList.add('translate-x-5');
                toggleElement.classList.remove('translate-x-1');
            } else {
                voucherSettings.classList.add('hidden');
                switchElement.classList.remove('bg-blue-500');
                switchElement.classList.add('bg-slate-600');
                toggleElement.classList.remove('translate-x-5');
                toggleElement.classList.add('translate-x-1');
            }
        }
        
        // 服务范围选择事件
        document.querySelectorAll('input[name="areaType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 重置所有选项
                document.querySelectorAll('input[name="areaType"]').forEach(r => {
                    const label = r.closest('label');
                    const div = label.querySelector('div');
                    const icon = div.querySelector('i');
                    const span = div.querySelector('span');
                    const dot = label.querySelector('.absolute');
                    
                    if (r.checked) {
                        if (r.value === 'urban') {
                            div.className = 'flex items-center justify-center space-x-2 p-3 bg-green-500/20 border-2 border-green-500 rounded-lg transition-all group-hover:bg-green-500/30';
                            icon.className = 'w-4 h-4 text-green-400';
                            span.className = 'text-sm font-medium text-green-400';
                            if (!dot) {
                                const newDot = document.createElement('div');
                                newDot.className = 'absolute top-1 right-1 w-2 h-2 bg-green-500 rounded-full';
                                label.appendChild(newDot);
                            }
                        } else {
                            div.className = 'flex items-center justify-center space-x-2 p-3 bg-blue-500/20 border-2 border-blue-500 rounded-lg transition-all group-hover:bg-blue-500/30';
                            icon.className = 'w-4 h-4 text-blue-400';
                            span.className = 'text-sm font-medium text-blue-400';
                            if (!dot) {
                                const newDot = document.createElement('div');
                                newDot.className = 'absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full';
                                label.appendChild(newDot);
                            }
                        }
                    } else {
                        div.className = 'flex items-center justify-center space-x-2 p-3 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all group-hover:bg-slate-500';
                        icon.className = 'w-4 h-4 text-slate-400';
                        span.className = 'text-sm text-slate-400';
                        if (dot) dot.remove();
                    }
                });
                console.log('选择服务范围:', this.value);
            });
        });
        
        // 初始化Lucide图标
        lucide.createIcons();
        
        // 线路类型选择事件
        document.querySelectorAll('input[name="routeType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 重置所有选项
                document.querySelectorAll('input[name="routeType"]').forEach(r => {
                    const label = r.closest('label');
                    const div = label.querySelector('div');
                    const icon = div.querySelector('i');
                    const span = div.querySelector('span');
                    const dot = label.querySelector('.absolute');
                    
                    if (r.checked) {
                        if (r.value === 'flexible') {
                            div.className = 'flex items-center justify-center space-x-2 p-3 bg-orange-500/20 border-2 border-orange-500 rounded-lg transition-all group-hover:bg-orange-500/30';
                            icon.className = 'w-4 h-4 text-orange-400';
                            span.className = 'text-sm font-medium text-orange-400';
                            if (!dot) {
                                const newDot = document.createElement('div');
                                newDot.className = 'absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full';
                                label.appendChild(newDot);
                            }
                        } else {
                            div.className = 'flex items-center justify-center space-x-2 p-3 bg-purple-500/20 border-2 border-purple-500 rounded-lg transition-all group-hover:bg-purple-500/30';
                            icon.className = 'w-4 h-4 text-purple-400';
                            span.className = 'text-sm font-medium text-purple-400';
                            if (!dot) {
                                const newDot = document.createElement('div');
                                newDot.className = 'absolute top-1 right-1 w-2 h-2 bg-purple-500 rounded-full';
                                label.appendChild(newDot);
                            }
                        }
                    } else {
                        div.className = 'flex items-center justify-center space-x-2 p-3 bg-slate-600 border-2 border-slate-500 rounded-lg transition-all group-hover:bg-slate-500';
                        icon.className = 'w-4 h-4 text-slate-400';
                        span.className = 'text-sm text-slate-400';
                        if (dot) dot.remove();
                    }
                });
                console.log('选择线路类型:', this.value);
            });
        });
        
        // 发送验证码功能
        document.getElementById('send-sms-btn').addEventListener('click', function() {
            const btn = this;
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                alert('请先输入手机号码');
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号码');
                return;
            }
            
            // 禁用按钮并开始倒计时
            btn.disabled = true;
            let countdown = 60;
            
            const timer = setInterval(() => {
                btn.textContent = `${countdown}s后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }, 1000);
            
            // 模拟发送验证码
            console.log('发送验证码到:', phone);
            alert('验证码已发送到您的手机，请注意查收');
        });
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>