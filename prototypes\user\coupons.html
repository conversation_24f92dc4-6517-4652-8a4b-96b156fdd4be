<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的优惠券 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            min-height: 100vh;
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .coupon-card {
            background: linear-gradient(135deg, #1e1e3f 0%, #252547 100%);
            border: 1px solid #2a2d4a;
            transition: all 0.3s ease;
        }
        .coupon-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
        .coupon-used {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            opacity: 0.6;
        }
        .coupon-expired {
            background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
            opacity: 0.7;
        }
        .tab-active {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }
        .tab-inactive {
            background: rgba(255, 255, 255, 0.1);
            color: #94a3b8;
        }
    </style>
</head>
<body class="text-slate-100">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-900/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <button onclick="goBack()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                <h1 class="text-lg font-semibold">我的优惠券</h1>
            </div>
            <div class="flex items-center space-x-2">
                <button onclick="showHelp()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 标签页导航 -->
    <div class="p-4">
        <div class="flex bg-slate-800/50 rounded-xl p-1">
            <button onclick="switchTab('available')" id="tab-available" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-active">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="ticket" class="w-4 h-4"></i>
                    <span>可使用</span>
                    <span id="available-count" class="bg-white/20 px-2 py-0.5 rounded-full text-xs">3</span>
                </div>
            </button>
            <button onclick="switchTab('used')" id="tab-used" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-inactive">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="circle-check" class="w-4 h-4"></i>
                    <span>已使用</span>
                    <span id="used-count" class="bg-white/20 px-2 py-0.5 rounded-full text-xs">5</span>
                </div>
            </button>
            <button onclick="switchTab('expired')" id="tab-expired" class="flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-colors tab-inactive">
                <div class="flex items-center justify-center space-x-2">
                    <i data-lucide="clock" class="w-4 h-4"></i>
                    <span>已过期</span>
                    <span id="expired-count" class="bg-white/20 px-2 py-0.5 rounded-full text-xs">2</span>
                </div>
            </button>
        </div>
    </div>

    <!-- 优惠券列表 -->
    <div class="px-4 pb-20">
        <!-- 可使用优惠券 -->
        <div id="available-coupons" class="space-y-4">
            <!-- 满减券 -->
            <div class="coupon-card rounded-xl p-4 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-500/20 to-transparent rounded-bl-full"></div>
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">满减券</span>
                            <span class="text-green-400 text-sm font-medium">川香小厨</span>
                        </div>
                        <h3 class="text-lg font-bold text-white mb-1">满50减10元</h3>
                        <p class="text-slate-400 text-sm mb-3">适用于店内所有菜品，不与其他优惠同享</p>
                        <div class="flex items-center justify-between">
                            <div class="text-xs text-slate-500">
                                <span>有效期至：2024-03-15</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="showCouponCode('CXCK001')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    立即使用
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 text-right">
                        <div class="text-2xl font-bold text-green-400">¥10</div>
                        <div class="text-xs text-slate-500">满50可用</div>
                    </div>
                </div>
            </div>

            <!-- 折扣券 -->
            <div class="coupon-card rounded-xl p-4 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-500/20 to-transparent rounded-bl-full"></div>
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="bg-purple-500 text-white px-2 py-1 rounded text-xs font-medium">折扣券</span>
                            <span class="text-purple-400 text-sm font-medium">时尚理发店</span>
                        </div>
                        <h3 class="text-lg font-bold text-white mb-1">理发服务8.5折</h3>
                        <p class="text-slate-400 text-sm mb-3">适用于所有理发服务，不含烫染项目</p>
                        <div class="flex items-center justify-between">
                            <div class="text-xs text-slate-500">
                                <span>有效期至：2024-03-20</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="showCouponCode('SSLFP002')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    立即使用
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 text-right">
                        <div class="text-2xl font-bold text-purple-400">8.5折</div>
                        <div class="text-xs text-slate-500">无门槛</div>
                    </div>
                </div>
            </div>

            <!-- 体验券 -->
            <div class="coupon-card rounded-xl p-4 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-500/20 to-transparent rounded-bl-full"></div>
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="bg-orange-500 text-white px-2 py-1 rounded text-xs font-medium">体验券</span>
                            <span class="text-orange-400 text-sm font-medium">悦动健身房</span>
                        </div>
                        <h3 class="text-lg font-bold text-white mb-1">免费体验3天</h3>
                        <p class="text-slate-400 text-sm mb-3">包含所有器械使用和团体课程</p>
                        <div class="flex items-center justify-between">
                            <div class="text-xs text-slate-500">
                                <span>有效期至：2024-03-25</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="showCouponCode('YDJS003')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    立即使用
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 text-right">
                        <div class="text-2xl font-bold text-orange-400">免费</div>
                        <div class="text-xs text-slate-500">体验券</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已使用优惠券 -->
        <div id="used-coupons" class="space-y-4 hidden">
            <div class="coupon-card coupon-used rounded-xl p-4 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-gray-500/20 to-transparent rounded-bl-full"></div>
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">已使用</span>
                            <span class="text-gray-400 text-sm font-medium">美味烧烤</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-300 mb-1">满100减20元</h3>
                        <p class="text-gray-500 text-sm mb-3">使用时间：2024-02-15 19:30</p>
                        <div class="flex items-center justify-between">
                            <div class="text-xs text-gray-600">
                                <span>券码：MWSK004</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="rateMerchant('美味烧烤')" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    评价商家
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 text-right">
                        <div class="text-2xl font-bold text-gray-400">¥20</div>
                        <div class="text-xs text-gray-600">已使用</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已过期优惠券 -->
        <div id="expired-coupons" class="space-y-4 hidden">
            <div class="coupon-card coupon-expired rounded-xl p-4 relative overflow-hidden">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-red-500/20 to-transparent rounded-bl-full"></div>
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">已过期</span>
                            <span class="text-red-400 text-sm font-medium">咖啡时光</span>
                        </div>
                        <h3 class="text-lg font-bold text-red-300 mb-1">买一送一</h3>
                        <p class="text-red-500 text-sm mb-3">过期时间：2024-02-01</p>
                        <div class="flex items-center justify-between">
                            <div class="text-xs text-red-600">
                                <span>券码：KFSG005</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button disabled class="bg-gray-600 text-gray-400 px-4 py-2 rounded-lg text-sm font-medium cursor-not-allowed">
                                    已过期
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 text-right">
                        <div class="text-2xl font-bold text-red-400">买一送一</div>
                        <div class="text-xs text-red-600">已过期</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="text-center py-16 hidden">
            <div class="w-24 h-24 mx-auto mb-4 bg-slate-700 rounded-full flex items-center justify-center">
                <i data-lucide="ticket" class="w-12 h-12 text-slate-500"></i>
            </div>
            <h3 class="text-lg font-medium text-slate-300 mb-2">暂无优惠券</h3>
            <p class="text-slate-500 mb-6">快去商家页面领取优惠券吧</p>
            <button onclick="goToShops()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                去逛逛商家
            </button>
        </div>
    </div>

    <!-- 优惠券详情弹窗 -->
    <div id="coupon-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-sm">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center">
                        <i data-lucide="qr-code" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">优惠券码</h3>
                    <div class="bg-slate-700 rounded-lg p-4 mb-4">
                        <div id="coupon-code" class="text-2xl font-mono font-bold text-blue-400 tracking-wider">CXCK001</div>
                    </div>
                    <p class="text-slate-400 text-sm mb-6">请向商家出示此券码进行核销</p>
                    <div class="flex space-x-3">
                        <button onclick="copyCouponCode()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                            复制券码
                        </button>
                        <button onclick="closeCouponModal()" class="flex-1 bg-slate-600 hover:bg-slate-700 text-white py-3 rounded-lg font-medium transition-colors">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用说明弹窗 -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">优惠券使用说明</h3>
                    <button onclick="closeHelpModal()" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                <div class="space-y-4 text-sm text-slate-300">
                    <div>
                        <h4 class="font-medium text-white mb-2">如何使用优惠券？</h4>
                        <p>1. 点击"立即使用"获取券码</p>
                        <p>2. 到店消费时向商家出示券码</p>
                        <p>3. 商家核销后即可享受优惠</p>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-2">注意事项</h4>
                        <p>• 每张优惠券仅限使用一次</p>
                        <p>• 请在有效期内使用</p>
                        <p>• 部分优惠券不可与其他优惠同享</p>
                        <p>• 具体使用规则以商家说明为准</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        let currentTab = 'available';
        let currentCouponCode = '';

        // 标签页切换
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签页样式
            document.querySelectorAll('[id^="tab-"]').forEach(btn => {
                btn.className = btn.className.replace('tab-active', 'tab-inactive');
            });
            document.getElementById(`tab-${tab}`).className = 
                document.getElementById(`tab-${tab}`).className.replace('tab-inactive', 'tab-active');
            
            // 显示对应内容
            document.getElementById('available-coupons').classList.add('hidden');
            document.getElementById('used-coupons').classList.add('hidden');
            document.getElementById('expired-coupons').classList.add('hidden');
            document.getElementById('empty-state').classList.add('hidden');
            
            const targetElement = document.getElementById(`${tab}-coupons`);
            if (targetElement) {
                targetElement.classList.remove('hidden');
            }
        }

        // 显示优惠券码
        function showCouponCode(code) {
            currentCouponCode = code;
            document.getElementById('coupon-code').textContent = code;
            document.getElementById('coupon-modal').classList.remove('hidden');
        }

        // 关闭优惠券弹窗
        function closeCouponModal() {
            document.getElementById('coupon-modal').classList.add('hidden');
        }

        // 复制优惠券码
        function copyCouponCode() {
            navigator.clipboard.writeText(currentCouponCode).then(() => {
                // 简单的复制成功提示
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '已复制';
                btn.classList.add('bg-green-600');
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('bg-green-600');
                }, 2000);
            });
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('help-modal').classList.remove('hidden');
        }

        // 关闭帮助弹窗
        function closeHelpModal() {
            document.getElementById('help-modal').classList.add('hidden');
        }

        // 评价商家
        function rateMerchant(merchantName) {
            console.log('评价商家:', merchantName);
            // 这里可以跳转到评价页面
        }

        // 去商家页面
        function goToShops() {
            window.location.href = 'shops.html';
        }

        // 返回
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.getElementById('coupon-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCouponModal();
            }
        });

        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHelpModal();
            }
        });
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>