<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>呼叫服务数据统计 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
        .chart-placeholder {
            background: linear-gradient(45deg, rgba(0, 102, 255, 0.1), rgba(99, 102, 241, 0.1));
            border: 2px dashed rgba(99, 102, 241, 0.3);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech">
    <!-- 顶部导航栏 -->
    <nav class="bg-dark-card border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 tech-gradient rounded-lg flex items-center justify-center">
                    <i data-lucide="phone" class="w-4 h-4 text-white"></i>
                </div>
                <h1 class="text-xl font-bold">呼叫服务数据统计</h1>
                <span class="text-sm text-gray-400 bg-gray-700 px-3 py-1 rounded-full">仅平台管理端可见</span>
            </div>
            <div class="flex items-center space-x-4">
                <select class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white">
                    <option>最近7天</option>
                    <option>最近30天</option>
                    <option>最近90天</option>
                    <option>自定义时间</option>
                </select>
                <button class="bg-tech-blue hover:bg-blue-600 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i data-lucide="download" class="w-4 h-4 mr-2 inline"></i>
                    导出报告
                </button>
            </div>
        </div>
    </nav>

    <div class="p-6">
        <!-- 核心指标概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 总呼叫次数 -->
            <div class="stat-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-tech-blue bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i data-lucide="phone-call" class="w-6 h-6 text-tech-blue"></i>
                    </div>
                    <span class="text-xs text-green-400 bg-green-400 bg-opacity-20 px-2 py-1 rounded-full">+23.5%</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-1">12,847</h3>
                <p class="text-sm text-gray-400">总呼叫次数</p>
            </div>

            <!-- 活跃商家数 -->
            <div class="stat-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-tech-purple bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i data-lucide="store" class="w-6 h-6 text-tech-purple"></i>
                    </div>
                    <span class="text-xs text-green-400 bg-green-400 bg-opacity-20 px-2 py-1 rounded-full">+18.2%</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-1">456</h3>
                <p class="text-sm text-gray-400">活跃商家数</p>
            </div>

            <!-- 平均满意度 -->
            <div class="stat-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-tech-cyan bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i data-lucide="star" class="w-6 h-6 text-tech-cyan"></i>
                    </div>
                    <span class="text-xs text-green-400 bg-green-400 bg-opacity-20 px-2 py-1 rounded-full">+0.3</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-1">4.7</h3>
                <p class="text-sm text-gray-400">平均满意度</p>
            </div>

            <!-- 引流用户数 -->
            <div class="stat-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-green-500"></i>
                    </div>
                    <span class="text-xs text-green-400 bg-green-400 bg-opacity-20 px-2 py-1 rounded-full">+31.7%</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-1">3,254</h3>
                <p class="text-sm text-gray-400">引流新用户</p>
            </div>
        </div>

        <!-- 详细数据分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 引流数据分析 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2"></i>
                    引流数据分析
                </h3>
                <div class="h-64 chart-placeholder rounded-lg flex items-center justify-center mb-4">
                    <div class="text-center">
                        <i data-lucide="bar-chart-3" class="w-16 h-16 text-tech-blue mx-auto mb-4"></i>
                        <p class="text-gray-400">用户流量趋势图</p>
                        <p class="text-sm text-gray-500 mt-2">显示呼叫服务带来的用户增长</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-gray-800 rounded-lg">
                        <p class="text-2xl font-bold text-tech-blue">68%</p>
                        <p class="text-sm text-gray-400">新用户转化率</p>
                    </div>
                    <div class="text-center p-3 bg-gray-800 rounded-lg">
                        <p class="text-2xl font-bold text-tech-purple">4.2</p>
                        <p class="text-sm text-gray-400">平均使用频次</p>
                    </div>
                </div>
            </div>

            <!-- 功能使用统计 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan flex items-center">
                    <i data-lucide="activity" class="w-5 h-5 mr-2"></i>
                    功能使用统计
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-tech-blue rounded-full"></div>
                            <span class="text-gray-300">点餐服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">4,567</span>
                            <span class="text-sm text-gray-400">(35.5%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-tech-purple rounded-full"></div>
                            <span class="text-gray-300">结账服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">3,234</span>
                            <span class="text-sm text-gray-400">(25.2%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-tech-cyan rounded-full"></div>
                            <span class="text-gray-300">加水服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">2,891</span>
                            <span class="text-sm text-gray-400">(22.5%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-gray-300">其他服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">2,155</span>
                            <span class="text-sm text-gray-400">(16.8%)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商家参与度和用户满意度 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 商家参与度 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan flex items-center">
                    <i data-lucide="building-2" class="w-5 h-5 mr-2"></i>
                    商家参与度分析
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-tech-blue/10 to-tech-purple/10 rounded-lg border border-tech-blue/20">
                        <div>
                            <p class="text-lg font-semibold text-white">活跃商家排行榜</p>
                            <p class="text-sm text-gray-400">本月呼叫服务使用次数</p>
                        </div>
                        <i data-lucide="trophy" class="w-8 h-8 text-yellow-500"></i>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-xs font-bold text-black">1</span>
                                <span class="text-gray-300">老王餐厅</span>
                            </div>
                            <span class="text-white font-semibold">1,234次</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center text-xs font-bold text-black">2</span>
                                <span class="text-gray-300">小李咖啡店</span>
                            </div>
                            <span class="text-white font-semibold">987次</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-xs font-bold text-black">3</span>
                                <span class="text-gray-300">张记面馆</span>
                            </div>
                            <span class="text-white font-semibold">756次</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户满意度 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan flex items-center">
                    <i data-lucide="heart" class="w-5 h-5 mr-2"></i>
                    用户满意度趋势
                </h3>
                <div class="h-48 chart-placeholder rounded-lg flex items-center justify-center mb-4">
                    <div class="text-center">
                        <i data-lucide="line-chart" class="w-12 h-12 text-tech-cyan mx-auto mb-2"></i>
                        <p class="text-gray-400">满意度趋势图</p>
                        <p class="text-sm text-gray-500 mt-1">显示评分变化趋势</p>
                    </div>
                </div>
                <div class="grid grid-cols-5 gap-2">
                    <div class="text-center p-2 bg-gray-800 rounded">
                        <div class="flex items-center justify-center mb-1">
                            <i data-lucide="star" class="w-4 h-4 text-yellow-500 fill-current"></i>
                            <span class="text-xs ml-1">5</span>
                        </div>
                        <p class="text-sm font-semibold text-white">67%</p>
                    </div>
                    <div class="text-center p-2 bg-gray-800 rounded">
                        <div class="flex items-center justify-center mb-1">
                            <i data-lucide="star" class="w-4 h-4 text-yellow-500 fill-current"></i>
                            <span class="text-xs ml-1">4</span>
                        </div>
                        <p class="text-sm font-semibold text-white">23%</p>
                    </div>
                    <div class="text-center p-2 bg-gray-800 rounded">
                        <div class="flex items-center justify-center mb-1">
                            <i data-lucide="star" class="w-4 h-4 text-yellow-500 fill-current"></i>
                            <span class="text-xs ml-1">3</span>
                        </div>
                        <p class="text-sm font-semibold text-white">7%</p>
                    </div>
                    <div class="text-center p-2 bg-gray-800 rounded">
                        <div class="flex items-center justify-center mb-1">
                            <i data-lucide="star" class="w-4 h-4 text-yellow-500 fill-current"></i>
                            <span class="text-xs ml-1">2</span>
                        </div>
                        <p class="text-sm font-semibold text-white">2%</p>
                    </div>
                    <div class="text-center p-2 bg-gray-800 rounded">
                        <div class="flex items-center justify-center mb-1">
                            <i data-lucide="star" class="w-4 h-4 text-yellow-500 fill-current"></i>
                            <span class="text-xs ml-1">1</span>
                        </div>
                        <p class="text-sm font-semibold text-white">1%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场潜力分析和地域分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 市场潜力分析 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan flex items-center">
                    <i data-lucide="target" class="w-5 h-5 mr-2"></i>
                    市场潜力分析
                </h3>
                <div class="space-y-4">
                    <div class="p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-400 font-medium">市场需求指数</span>
                            <span class="text-2xl font-bold text-green-400">85%</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                    <div class="p-4 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg border border-blue-500/20">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-blue-400 font-medium">功能普及率</span>
                            <span class="text-2xl font-bold text-blue-400">72%</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full" style="width: 72%"></div>
                        </div>
                    </div>
                    <div class="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-purple-400 font-medium">发展潜力</span>
                            <span class="text-2xl font-bold text-purple-400">91%</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full" style="width: 91%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地域分布统计 -->
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <h3 class="text-lg font-semibold mb-4 text-tech-cyan flex items-center">
                    <i data-lucide="map-pin" class="w-5 h-5 mr-2"></i>
                    地域分布统计
                </h3>
                <div class="h-48 chart-placeholder rounded-lg flex items-center justify-center mb-4">
                    <div class="text-center">
                        <i data-lucide="map" class="w-12 h-12 text-tech-purple mx-auto mb-2"></i>
                        <p class="text-gray-400">地域分布热力图</p>
                        <p class="text-sm text-gray-500 mt-1">显示各地区使用情况</p>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span class="text-gray-300">市中心区</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">3,456</span>
                            <span class="text-sm text-gray-400">(26.9%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                            <span class="text-gray-300">商业区</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">2,789</span>
                            <span class="text-sm text-gray-400">(21.7%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-gray-300">住宅区</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">2,234</span>
                            <span class="text-sm text-gray-400">(17.4%)</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-gray-300">其他区域</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-white font-semibold">4,368</span>
                            <span class="text-sm text-gray-400">(34.0%)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();
        
        // 模拟数据更新
        function updateStats() {
            // 这里可以添加实时数据更新逻辑
            console.log('更新呼叫服务统计数据');
        }
        
        // 每30秒更新一次数据
        setInterval(updateStats, 30000);
        
        // 导出报告功能
        document.querySelector('button').addEventListener('click', function() {
            alert('正在生成呼叫服务数据报告...');
        });
    </script>
</body>
</html>