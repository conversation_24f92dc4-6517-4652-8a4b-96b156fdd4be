<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .menu-item {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        .menu-item:hover {
            background-color: rgba(99, 102, 241, 0.1);
            border-color: rgba(99, 102, 241, 0.3);
            transform: translateX(5px);
        }
        .stat-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }
        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }
    </style>
</head>
<body class="bg-dark-bg text-white">
    <!-- 顶部用户信息 -->
    <div class="tech-gradient p-6">
        <div class="flex items-center space-x-4">
            <!-- 用户头像 -->
            <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                <span class="text-2xl font-bold text-tech-blue">张</span>
            </div>
            
            <!-- 用户信息 -->
            <div class="flex-1">
                <h2 class="text-xl font-bold text-white">张三</h2>
                <div class="flex items-center space-x-2 mt-1">
                    <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">已认证</span>
                    <span class="text-sm text-white/80">ID: 123456789</span>
                </div>
                <p class="text-sm text-white/70 mt-1">注册时间：2024-01-15</p>
            </div>
            
            <!-- 设置按钮 -->
            <button class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 数据统计 -->
    <div class="p-4">
        <div class="grid grid-cols-4 gap-4">
            <div class="stat-card bg-dark-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-tech-cyan mb-1">12</div>
                <div class="text-sm text-gray-400">发布信息</div>
            </div>
            
            <div class="stat-card bg-dark-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-green-400 mb-1">8</div>
                <div class="text-sm text-gray-400">成功撮合</div>
            </div>
            
            <div class="stat-card bg-dark-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-yellow-400 mb-1">4.8</div>
                <div class="text-sm text-gray-400">信用评分</div>
            </div>
            
            <div class="stat-card bg-dark-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold text-purple-400 mb-1">25</div>
                <div class="text-sm text-gray-400">收藏商家</div>
            </div>
        </div>
    </div>

    <!-- 功能菜单 -->
    <div class="p-4 space-y-4">
        <!-- 我的发布 -->
        <div class="bg-dark-card rounded-xl p-1">
            <h3 class="text-lg font-semibold text-white p-3 pb-2">我的发布</h3>
            
            <div class="grid grid-cols-2 gap-3 p-3">
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="checkUserShopAndNavigate">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
                            </svg>
                        </div>
                        <h3 class="font-medium text-white text-sm">商铺管理</h3>
                        <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full mt-1 inline-block">3</span>
                    </div>
                </div>
                
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="navigate:my-information">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3 class="font-medium text-white text-sm">信息</h3>
                        <span class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full mt-1 inline-block">5</span>
                    </div>
                </div>
                
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="navigate:my-transportation">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i data-lucide="car" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-medium text-white text-sm">出行</h3>
                        <span class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full mt-1 inline-block">2</span>
                    </div>
                </div>
                
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="navigate:my-errands">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i data-lucide="package" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-medium text-white text-sm">跑腿</h3>
                        <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full mt-1 inline-block">4</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的记录 -->
        <div class="bg-dark-card rounded-xl p-1">
            <h3 class="text-lg font-semibold text-white p-3 pb-2">我的记录</h3>
            
            <div class="grid grid-cols-2 gap-3 p-3">
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="navigate:reviews">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i data-lucide="star" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-medium text-white text-sm">我的评价</h3>
                        <span class="px-2 py-1 bg-yellow-600 text-white text-xs rounded-full mt-1 inline-block">15</span>
                    </div>
                </div>
                
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="navigate:contact-history">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-violet-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i data-lucide="clock" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-medium text-white text-sm">联系记录</h3>
                        <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full mt-1 inline-block">23</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的评价 -->
        <div class="bg-dark-card rounded-xl p-1">
            <h3 class="text-lg font-semibold text-white p-3 pb-2">我的评价</h3>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="navigate:reviews">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
                            <i data-lucide="edit-3" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-white">待评价</p>
                            <p class="text-sm text-gray-400">需要评价的服务</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full">3条</span>
                        <i data-lucide="chevron-right" class="w-5 h-5 text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="navigate:reviews">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                            <i data-lucide="star" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-white">已评价</p>
                            <p class="text-sm text-gray-400">我发出的评价</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">12条</span>
                        <i data-lucide="chevron-right" class="w-5 h-5 text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="navigate:reviews">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-teal-500 rounded-lg flex items-center justify-center">
                            <i data-lucide="message-circle" class="w-5 h-5 text-white"></i>
                        </div>
                        <div>
                            <p class="font-medium text-white">收到的评价</p>
                            <p class="text-sm text-gray-400">他人对我的评价</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">18条</span>
                        <i data-lucide="chevron-right" class="w-5 h-5 text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的资产 -->
        <div class="bg-dark-card rounded-xl p-1">
            <h3 class="text-lg font-semibold text-white p-3 pb-2">我的资产</h3>
            
            <div class="p-3">
                <div class="menu-item rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors" data-action="navigate:coupons">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-rose-500 rounded-lg flex items-center justify-center">
                                <i data-lucide="ticket" class="w-5 h-5 text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium text-white">优惠券</p>
                                <p class="text-sm text-gray-400">查看我的优惠券</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 bg-pink-600 text-white text-xs rounded-full">12张</span>
                            <i data-lucide="chevron-right" class="w-5 h-5 text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 账户管理 -->
        <div class="bg-dark-card rounded-xl p-1">
            <h3 class="text-lg font-semibold text-white p-3 pb-2">账户管理</h3>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="editProfile">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">个人资料</p>
                            <p class="text-sm text-gray-400">修改头像、昵称等</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">实名认证</p>
                            <p class="text-sm text-gray-400">提升账户安全性</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">已认证</span>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="navigate:coupons-management">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">优惠券管理</p>
                            <p class="text-sm text-gray-400">查看和使用优惠券</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 bg-pink-600 text-white text-xs rounded-full">4张</span>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="showPrivacySettings">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">隐私设置</p>
                            <p class="text-sm text-gray-400">管理个人隐私</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="showAccountSecurity">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">账号安全</p>
                            <p class="text-sm text-gray-400">手机号、密码等安全设置</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="showAccountDeletion">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">注销账号</p>
                            <p class="text-sm text-gray-400">永久删除账号和数据</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 其他功能 -->
        <div class="bg-dark-card rounded-xl p-1">
            <h3 class="text-lg font-semibold text-white p-3 pb-2">其他功能</h3>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="showFeedback">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">意见反馈</p>
                            <p class="text-sm text-gray-400">帮助我们改进</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="navigate:help">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">帮助中心</p>
                            <p class="text-sm text-gray-400">使用指南和FAQ</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
            
            <div class="menu-item rounded-lg p-3 cursor-pointer" data-action="logout">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-red-400 to-pink-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-white">退出登录</p>
                            <p class="text-sm text-gray-400">安全退出账户</p>
                        </div>
                    </div>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button data-action="navigate:shops" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                <button data-action="navigate:message" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button data-action="showPublishModal" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button data-action="navigate:help" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            // 初始化事件处理
            initProfileEvents();
        });
        
        // 初始化个人中心页面事件
        function initProfileEvents() {
            // 处理所有data-action按钮和链接
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (!target) return;
                
                const action = target.dataset.action;
                
                // 检查是否是导航动作
                if (action.startsWith('navigate:')) {
                    const page = action.split(':')[1];
                    window.location.href = page + '.html';
                    return;
                }
                
                // 其他动作处理
                switch (action) {
                    case 'checkUserShopAndNavigate':
                        // 检查用户是否有商铺
                        const hasShop = localStorage.getItem('userHasShop') === 'true';
                        if (hasShop) {
                            window.location.href = 'my-shop-products.html';
                        } else {
                            if (confirm('您还没有开通商铺，是否立即创建?')) {
                                window.location.href = 'publish-shop.html';
                            }
                        }
                        break;
                        
                    case 'editProfile':
                        // 模拟编辑个人资料
                        alert('个人资料编辑功能即将上线');
                        break;
                        
                    case 'showPrivacySettings':
                        // 显示隐私设置
                        window.location.href = 'privacy-policy.html';
                        break;
                        
                    case 'showAccountSecurity':
                        // 显示账号安全设置
                        alert('账号安全设置功能即将上线');
                        break;
                        
                    case 'showAccountDeletion':
                        // 账号删除确认
                        if (confirm('您确定要注销账号吗？此操作不可逆，您的所有数据将被永久删除。')) {
                            if (confirm('再次确认：注销后账号无法恢复，所有数据将被清除。是否继续？')) {
                                alert('账号注销申请已提交，我们会在3个工作日内处理您的请求。');
                            }
                        }
                        break;
                        
                    case 'showFeedback':
                        // 意见反馈
                        alert('感谢您的反馈！请描述您遇到的问题或建议，我们会尽快回复您。');
                        break;
                        
                    case 'logout':
                        // 退出登录
                        if (confirm('确定要退出登录吗？')) {
                            // 清除登录状态
                            sessionStorage.removeItem('isLoggedIn');
                            localStorage.removeItem('rememberLogin');
                            
                            // 重定向到登录页
                            window.location.href = 'login.html';
                        }
                        break;
                        
                    case 'showPublishModal':
                        // 显示发布选项
                        window.location.href = 'publish-select.html';
                        break;
                }
            });
        }
    </script>
</body>
</html>