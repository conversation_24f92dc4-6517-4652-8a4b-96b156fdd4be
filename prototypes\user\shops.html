<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商铺板块 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/config.js"></script>
    <script src="../scripts/logger.js"></script>
    <script src="../scripts/common.js"></script>
    <script src="../scripts/templates.js"></script>
    <script src="../scripts/event-handlers.js"></script>
    <script src="../scripts/component-registry.js"></script>
    <script src="../scripts/fix-inline-events.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .card-hover {
            transition: background-color 0.2s ease;
        }
        .card-hover:hover {
            background-color: rgba(30, 41, 59, 0.8);
        }
        .shop-avatar {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }
        .rating-stars {
            color: #fbbf24;
        }
        .coupon-tag {
            background: linear-gradient(45deg, #f59e0b, #f97316);
        }
        .verified-badge {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .type-tag {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部搜索区域 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center space-x-3">
                <!-- 城市选择按钮 -->
                <button class="flex items-center space-x-1 bg-slate-700 px-3 py-2 rounded-lg text-sm font-medium text-slate-300 hover:bg-slate-600 transition-colors">
                    <i data-lucide="map-pin" class="w-4 h-4"></i>
                    <span>深圳</span>
                    <i data-lucide="chevron-down" class="w-3 h-3"></i>
                </button>
                
                <!-- 搜索输入框 -->
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索商铺服务..." 
                           class="w-full bg-slate-700 border-0 rounded-lg px-4 py-2 pl-10 pr-4 text-sm text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:bg-slate-600 transition-all">
                    <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                </div>
                

                <!-- 扫一扫按钮 -->
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" data-action="openQRScanner">
                    <i data-lucide="qr-code" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 四大板块导航 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-between py-3">
                <button class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg bg-blue-600 text-white font-medium">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                <button data-action="navigate:information" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="info" class="w-5 h-5"></i>
                    <span class="text-xs">信息</span>
                </button>
                <button data-action="navigate:transportation" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="car" class="w-5 h-5"></i>
                    <span class="text-xs">出行</span>
                </button>
                <button data-action="navigate:errands" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="package" class="w-5 h-5"></i>
                    <span class="text-xs">跑腿</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 商铺分类筛选 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between mb-3">
                <div class="flex space-x-2 overflow-x-auto scrollbar-hide flex-1">
                    <button class="flex-shrink-0 px-4 py-2 bg-blue-600 text-white rounded-full text-sm font-medium" onclick="filterByType('all')">全部</button>
                    <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" onclick="filterByType('physical')">实体</button>
                    <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" onclick="filterByType('online')">网店</button>
                    <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" onclick="filterByType('vendor')">摊贩</button>
                    <button class="flex-shrink-0 px-4 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" onclick="filterByType('master')">师傅</button>
                </div>
                <button class="flex items-center justify-center w-8 h-8 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors ml-2" data-action="toggleViewMode" title="切换浏览模式">
                    <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                </button>
            </div>
            
            <!-- 高级筛选按钮 -->
            <div class="flex items-center justify-between">
                <div class="flex space-x-2">
                    <button class="flex items-center space-x-1 px-3 py-1.5 bg-slate-700 text-slate-300 rounded-lg text-xs hover:bg-slate-600 transition-colors" data-action="toggleFilter" data-params='{"filter":"distance"}'>
                        <i data-lucide="map-pin" class="w-3 h-3"></i>
                        <span>距离</span>
                        <i data-lucide="chevron-down" class="w-3 h-3"></i>
                    </button>
                    <button class="flex items-center space-x-1 px-3 py-1.5 bg-slate-700 text-slate-300 rounded-lg text-xs hover:bg-slate-600 transition-colors" data-action="toggleFilter" data-params='{"filter":"rating"}'>
                        <i data-lucide="star" class="w-3 h-3"></i>
                        <span>评分</span>
                        <i data-lucide="chevron-down" class="w-3 h-3"></i>
                    </button>
                    <button class="flex items-center space-x-1 px-3 py-1.5 bg-slate-700 text-slate-300 rounded-lg text-xs hover:bg-slate-600 transition-colors" data-action="toggleFilter" data-params='{"filter":"time"}'>
                        <i data-lucide="clock" class="w-3 h-3"></i>
                        <span>营业时间</span>
                        <i data-lucide="chevron-down" class="w-3 h-3"></i>
                    </button>
                </div>
                <button class="flex items-center space-x-1 px-3 py-1.5 bg-slate-700 text-slate-300 rounded-lg text-xs hover:bg-slate-600 transition-colors" data-action="showAdvancedFilter">
                    <i data-lucide="filter" class="w-3 h-3"></i>
                    <span>筛选</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 高级筛选面板 -->
    <div id="advancedFilter" class="hidden bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="space-y-4">
                <!-- 距离筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">距离范围</label>
                    <div class="flex space-x-2">
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterDistance" data-params='{"value":"500m"}'>500m内</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterDistance" data-params='{"value":"1km"}'>1km内</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterDistance" data-params='{"value":"3km"}'>3km内</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterDistance" data-params='{"value":"all"}'>不限</button>
                    </div>
                </div>
                
                <!-- 评分筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">用户评分</label>
                    <div class="flex space-x-2">
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterRating" data-params='{"value":"4.5"}'>4.5分以上</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterRating" data-params='{"value":"4.0"}'>4.0分以上</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterRating" data-params='{"value":"3.5"}'>3.5分以上</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterRating" data-params='{"value":"all"}'>不限</button>
                    </div>
                </div>
                
                <!-- 价格区间筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">价格区间</label>
                    <div class="flex space-x-2">
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterPrice" data-params='{"value":"low"}'>经济实惠</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterPrice" data-params='{"value":"medium"}'>中等价位</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterPrice" data-params='{"value":"high"}'>高端消费</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterPrice" data-params='{"value":"all"}'>不限</button>
                    </div>
                </div>
                
                <!-- 营业状态筛选 -->
                <div>
                    <label class="block text-sm font-medium text-slate-300 mb-2">营业状态</label>
                    <div class="flex space-x-2">
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterStatus" data-params='{"value":"open"}'>营业中</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterStatus" data-params='{"value":"24h"}'>24小时</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterStatus" data-params='{"value":"verified"}'>已认证</button>
                        <button class="flex-1 py-2 px-3 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-blue-600 hover:text-white transition-colors" data-action="filterStatus" data-params='{"value":"all"}'>全部</button>
                    </div>
                </div>
                
                <!-- 筛选操作按钮 -->
                <div class="flex space-x-3 pt-2">
                    <button class="flex-1 py-2 px-4 bg-slate-600 text-slate-300 rounded-lg text-sm hover:bg-slate-500 transition-colors" data-action="resetFilters">重置</button>
                    <button class="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors" data-action="applyFilters">应用筛选</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content: Tips and List -->
    <main class="max-w-md mx-auto px-4 pt-4 pb-24 space-y-4">
        <!-- 商家认证提示 -->
        <div id="info-banner" class="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 relative">
            <div class="flex items-start space-x-3">
                <i data-lucide="shield-check" class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0"></i>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-blue-400 mb-2">商家认证说明</h4>
                    <div class="text-xs text-blue-300/90 space-y-1">
                        <p>• 带有"实体认证"标识的商家已通过营业执照验证</p>
                        <p>• 建议优先选择认证商家，服务更有保障</p>
                        <p>• 消费前请核实商家信息和服务内容</p>
                        <p>• 如遇服务纠纷请及时联系平台客服</p>
                    </div>
                    <button class="text-xs text-blue-400 hover:text-blue-300 mt-2 underline" onclick="showMerchantGuide()">查看商家选择指南</button>
                </div>
                <div class="flex items-center space-x-2">
                    <span id="countdown-timer" class="text-xs text-blue-400 font-mono">5s</span>
                    <button id="close-banner-btn" class="text-blue-400 hover:text-blue-200">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 商铺列表 -->
        <div id="shopList" class="space-y-3">

        <!-- 商铺卡片 1 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="physical" data-action="openShopDetail" data-params='{"id":"shop1"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=50&h=50&fit=crop&crop=center" 
                         alt="川味小厨" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="川味小厨家常菜馆正宗四川口味">
                                川味小厨家常菜馆正宗四川口味
                            </h3>
                            <span class="bg-blue-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">实体</span>
                        </div>
                        
                        <!-- 第二行：已认证+距离+营业状态+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-green-400 flex items-center space-x-1">
                                <i data-lucide="shield-check" class="w-3 h-3"></i>
                                <span>已认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>500m</span>
                            </span>
                            <span class="text-red-500 flex items-center space-x-1">
                                <i data-lucide="clock" class="w-3 h-3"></i>
                                <span>已休息</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" data-action="contactShop" data-params='{"id":"shop1"}' data-stop-propagation="true">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="川菜、家常菜、麻辣烫、火锅、干锅系列">
                        主营：川菜、家常菜、麻辣烫、火锅、干锅系列
                    </p>
                </div>
                
                <!-- 产品缩略图（放大并居中） -->
                <div class="flex justify-center">
                    <div class="grid grid-cols-3 gap-3 w-full max-w-sm">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=300&fit=crop&crop=center" 
                             alt="招牌菜1" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=300&h=300&fit=crop&crop=center" 
                             alt="招牌菜2" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=300&h=300&fit=crop&crop=center" 
                             alt="招牌菜3" class="w-full h-20 rounded-lg object-cover shadow-md">
                    </div>
                </div>
                
                <!-- 地址信息（仅实体类型显示） -->
                <div class="px-1">
                    <p class="text-slate-400 text-xs">地址：成都市锦江区春熙路步行街128号</p>
                </div>
            </div>
        </div>

        <!-- 团购广告卡片 -->
        <div class="bg-gradient-to-r from-green-900 to-teal-900 rounded-lg shadow-md border border-green-700 p-3 relative overflow-hidden cursor-pointer" onclick="window.location.href='group_buy.html'">
            <div class="absolute top-0 right-0 bg-yellow-500 text-slate-900 text-xs px-2 py-1 font-bold">广告</div>
            <div class="flex items-center space-x-3">
                <!-- 商铺头像（小尺寸） -->
                <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=50&h=50&fit=crop&crop=center" 
                     alt="咖啡团购" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                
                <!-- 内容区域 -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2 mb-1">
                        <h3 class="font-bold text-white text-base leading-tight line-clamp-1" title="星巴克咖啡 - 双人下午茶套餐">
                            星巴克咖啡 - 双人下午茶套餐
                        </h3>
                        <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full font-medium flex-shrink-0">团购</span>
                    </div>
                    
                    <div class="flex items-center space-x-2 text-xs mb-2">
                        <span class="text-teal-300 flex items-center space-x-1">
                            <i data-lucide="clock" class="w-3 h-3"></i>
                            <span>剩余15:32:08</span>
                        </span>
                        <span class="text-yellow-300 font-bold flex items-center space-x-1">
                            <i data-lucide="star" class="w-3 h-3"></i>
                            <span>热销推荐</span>
                        </span>
                        <span class="text-green-300 text-xs flex items-center space-x-1">
                            <i data-lucide="users" class="w-3 h-3"></i>
                            <span>已售892份</span>
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-teal-200 text-xs">原价: <span class="line-through">¥128</span></span>
                            <span class="text-yellow-300 font-bold text-lg">团购价: ¥68</span>
                        </div>
                        <button class="bg-yellow-500 hover:bg-yellow-600 text-slate-900 font-bold py-1 px-4 rounded-lg transition-colors text-sm">
                            立即抢购
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商铺卡片 4 - 无图片版本 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="master" data-action="openShopDetail" data-params='{"id":"shop3"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=center" 
                         alt="专业维修" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="专业手机电脑维修服务中心">
                                专业手机电脑维修服务中心
                            </h3>
                            <span class="bg-purple-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">师傅</span>
                        </div>
                        
                        <!-- 第二行：已认证+距离+营业状态+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-green-400 flex items-center space-x-1">
                                <i data-lucide="shield-check" class="w-3 h-3"></i>
                                <span>已认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>800m</span>
                            </span>
                            <span class="text-green-500 flex items-center space-x-1">
                                <i data-lucide="clock" class="w-3 h-3"></i>
                                <span>营业中</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" onclick="event.stopPropagation(); contactShop('shop3')">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="手机维修、电脑维修、数据恢复、屏幕更换、系统重装">
                        主营：手机维修、电脑维修、数据恢复、屏幕更换、系统重装
                    </p>
                </div>
                

            </div>
        </div>

        <!-- 商铺卡片 5 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="master" data-action="openShopDetail" data-params='{"id":"shop2"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=50&h=50&fit=crop&crop=center" 
                         alt="美发沙龙" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="时尚美发沙龙专业造型设计工作室">
                                时尚美发沙龙专业造型设计工作室
                            </h3>
                            <span class="bg-purple-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">师傅</span>
                        </div>
                        
                        <!-- 第二行：已认证+距离+营业状态+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-green-400 flex items-center space-x-1">
                                <i data-lucide="shield-check" class="w-3 h-3"></i>
                                <span>已认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>1.2km</span>
                            </span>
                            <span class="text-green-500 flex items-center space-x-1">
                                <i data-lucide="clock" class="w-3 h-3"></i>
                                <span>营业中</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" onclick="event.stopPropagation(); contactShop('shop2')">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="专业美发、烫染、护理、造型设计、头皮护理">
                        主营：专业美发、烫染、护理、造型设计、头皮护理
                    </p>
                </div>
                
                <!-- 产品缩略图（放大并居中） -->
                <div class="flex justify-center">
                    <div class="grid grid-cols-3 gap-3 w-full max-w-sm">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop&crop=center" 
                             alt="发型作品1" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=300&h=300&fit=crop&crop=center" 
                             alt="发型作品2" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=300&h=300&fit=crop&crop=center" 
                             alt="发型作品3" class="w-full h-20 rounded-lg object-cover shadow-md">
                    </div>
                </div>
            </div>
        </div>
        

        
        <!-- 商铺卡片 5 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="physical" data-action="openShopDetail" data-params='{"id":"shop3"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=50&h=50&fit=crop&crop=center" 
                         alt="便利店" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="24小时便利店">
                                24小时便利店
                            </h3>
                            <span class="bg-blue-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">实体</span>
                        </div>
                        
                        <!-- 第二行：未认证+距离+营业状态+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-slate-400 flex items-center space-x-1">
                                <i data-lucide="alert-circle" class="w-3 h-3"></i>
                                <span>未认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>300m</span>
                            </span>
                            <span class="text-blue-500 flex items-center space-x-1">
                                <i data-lucide="clock" class="w-3 h-3"></i>
                                <span>24小时</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" onclick="event.stopPropagation(); contactShop('shop3')">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="日用品、零食、饮料、充值服务、快递代收">
                        主营：日用品、零食、饮料、充值服务、快递代收
                    </p>
                </div>
                
                <!-- 产品缩略图（放大并居中） -->
                <div class="flex justify-center">
                    <div class="grid grid-cols-3 gap-3 w-full max-w-sm">
                        <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=300&fit=crop&crop=center" 
                             alt="便利商品1" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop&crop=center" 
                             alt="便利商品2" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop&crop=center" 
                             alt="便利商品3" class="w-full h-20 rounded-lg object-cover shadow-md">
                    </div>
                </div>
                
                <!-- 地址信息（仅实体类型显示） -->
                <div class="px-1">
                    <p class="text-slate-400 text-xs">地址：成都市武侯区科华北路62号力宝大厦1楼</p>
                </div>
            </div>
        </div>

        <!-- 商铺卡片 4 - 摊贩类型 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="vendor" data-action="openShopDetail" data-params='{"id":"shop4"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=50&h=50&fit=crop&crop=center" 
                         alt="小吃摊" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="老王煎饼果子早餐摊">
                                老王煎饼果子早餐摊
                            </h3>
                            <span class="bg-orange-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">摊贩</span>
                        </div>
                        
                        <!-- 第二行：已认证+距离+营业状态+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-green-400 flex items-center space-x-1">
                                <i data-lucide="shield-check" class="w-3 h-3"></i>
                                <span>已认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>150m</span>
                            </span>
                            <span class="text-green-500 flex items-center space-x-1">
                                <i data-lucide="clock" class="w-3 h-3"></i>
                                <span>营业中</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" onclick="event.stopPropagation(); contactShop('shop4')">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="煎饼果子、豆浆、包子、油条、咸菜">
                        主营：煎饼果子、豆浆、包子、油条、咸菜
                    </p>
                </div>
                
                <!-- 产品缩略图（放大并居中） -->
                <div class="flex justify-center">
                    <div class="grid grid-cols-3 gap-3 w-full max-w-sm">
                        <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop&crop=center" 
                             alt="煎饼果子" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop&crop=center" 
                             alt="豆浆包子" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=300&fit=crop&crop=center" 
                             alt="早餐套餐" class="w-full h-20 rounded-lg object-cover shadow-md">
                    </div>
                </div>
            </div>
        </div>

        <!-- 商铺卡片5 - 网店类型 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="online" data-action="openShopDetail" data-params='{"id":"shop5"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=50&h=50&fit=crop&crop=center" 
                         alt="网店" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="优选数码配件专营店旗舰店官方直营">
                                优选数码配件专营店旗舰店官方直营
                            </h3>
                            <span class="bg-red-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">网店</span>
                        </div>
                        
                        <!-- 第二行：已认证+距离+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-green-400 flex items-center space-x-1">
                                <i data-lucide="shield-check" class="w-3 h-3"></i>
                                <span>已认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>2.5km</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" onclick="event.stopPropagation(); contactShop('shop5')">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="手机配件、数码产品、电子设备销售，支持全国包邮">
                        主营：手机配件、数码产品、电子设备销售，支持全国包邮
                    </p>
                </div>
                
                <!-- 产品缩略图（放大并居中） -->
                <div class="flex justify-center">
                    <div class="grid grid-cols-3 gap-3 w-full max-w-sm">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=300&fit=crop&crop=center" 
                             alt="数码配件" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop&crop=center" 
                             alt="手机配件" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=300&h=300&fit=crop&crop=center" 
                             alt="电子设备" class="w-full h-20 rounded-lg object-cover shadow-md">
                    </div>
                </div>
            </div>
        </div>

        <!-- 商铺卡片6 - 摊贩类型 -->
        <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-type="vendor" data-action="openShopDetail" data-params='{"id":"shop6"}'>
            <div class="flex flex-col space-y-3">
                <!-- 顶部：头像、名称+类型 -->
                <div class="flex items-start space-x-3">
                    <!-- 商铺头像（小尺寸） -->
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=50&h=50&fit=crop&crop=center" 
                         alt="水果摊" class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                    
                    <!-- 名称和类型在一行 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-2">
                            <h3 class="font-semibold text-slate-100 text-lg leading-tight line-clamp-1" title="老王家新鲜水果摊">
                                老王家新鲜水果摊
                            </h3>
                            <span class="bg-orange-600 text-white text-sm px-3 py-1 rounded-full font-medium flex-shrink-0">摊贩</span>
                        </div>
                        
                        <!-- 第二行：未认证+距离+营业状态+优惠券+电话 -->
                        <div class="flex items-center space-x-[6px] text-xs">
                            <span class="text-slate-400 flex items-center space-x-1">
                                <i data-lucide="alert-circle" class="w-3 h-3"></i>
                                <span>未认证</span>
                            </span>
                            <span class="text-orange-400 font-medium flex items-center space-x-1">
                                <i data-lucide="map-pin" class="w-3 h-3"></i>
                                <span>500m</span>
                            </span>
                            <span class="text-green-500 flex items-center space-x-1">
                                <i data-lucide="clock" class="w-3 h-3"></i>
                                <span>营业中</span>
                            </span>
                            <span class="text-red-400 flex items-center space-x-1">
                                <i data-lucide="ticket" class="w-3 h-3"></i>
                                <span>优惠券</span>
                            </span>
                            <button class="text-blue-400 flex items-center space-x-1 hover:text-blue-300 transition-colors" onclick="event.stopPropagation(); contactShop('shop6')">
                                <i data-lucide="phone" class="w-3 h-3"></i>
                                <span>电话</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 主营范围 -->
                <div class="px-1">
                    <p class="text-slate-300 text-sm line-clamp-1" title="新鲜水果、时令蔬菜、干果零食">
                        主营：新鲜水果、时令蔬菜、干果零食
                    </p>
                </div>
                
                <!-- 产品缩略图（放大并居中） -->
                <div class="flex justify-center">
                    <div class="grid grid-cols-3 gap-3 w-full max-w-sm">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=300&fit=crop&crop=center" 
                             alt="新鲜水果" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1540420773420-3366772f4999?w=300&h=300&fit=crop&crop=center" 
                             alt="时令蔬菜" class="w-full h-20 rounded-lg object-cover shadow-md">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop&crop=center" 
                             alt="干果零食" class="w-full h-20 rounded-lg object-cover shadow-md">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务免责声明 -->
    <div id="shop-reminder" class="max-w-md mx-auto px-4 mb-2">
        <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-3">
            <div class="flex items-start space-x-2">
                <i data-lucide="info" class="w-4 h-4 text-slate-400 mt-0.5 flex-shrink-0"></i>
                <div class="flex-1">
                    <p class="text-xs text-slate-400 leading-relaxed mb-2">
                        <strong class="text-slate-300">服务提醒：</strong>本平台为商家信息展示平台，具体服务由商家提供。平台不参与商家经营活动，不对服务质量承担责任。
                    </p>
                    <p class="text-xs text-slate-400 leading-relaxed">
                        <strong class="text-slate-300">消费建议：</strong>消费前请确认服务内容和价格，保留消费凭证，如有纠纷请先与商家协商。
                    </p>
                    <div class="flex space-x-3 mt-2">
                        <button class="text-xs text-blue-400 hover:text-blue-300 underline" onclick="showComplaintGuide()">投诉指南</button>
                        <button class="text-xs text-blue-400 hover:text-blue-300 underline" onclick="showServiceDisclaimer()">服务免责声明</button>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <span id="reminder-countdown-timer" class="text-xs text-slate-400 font-mono">3s</span>
                    <button id="close-reminder-btn" class="text-slate-500 hover:text-slate-300">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 优惠券弹窗模态框 -->
    <div id="couponModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-4">
                    <h3 class="text-lg font-semibold text-slate-900 mb-2">优惠券详情</h3>
                    <div class="coupon-tag text-white px-4 py-2 rounded-lg inline-block">
                        <div class="font-semibold">满50减10元</div>
                        <div class="text-xs opacity-90">新用户专享</div>
                    </div>
                </div>
                
                <div class="space-y-3 text-sm text-slate-600 mb-6">
                    <div class="flex justify-between">
                        <span>使用条件：</span>
                        <span>满50元可用</span>
                    </div>
                    <div class="flex justify-between">
                        <span>有效期：</span>
                        <span>2024.12.31前</span>
                    </div>
                    <div class="flex justify-between">
                        <span>适用范围：</span>
                        <span>全店通用</span>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="closeCouponModal()" class="flex-1 py-2 px-4 border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors">
                        取消
                    </button>
                    <button class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        立即领取
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布本地信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿任务</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
        
        // 显示AI帮助
        function showAIHelp() {
            window.location.href = 'ai-help.html?section=shops';
        }
        
        // 搜索功能
        function performSearch() {
            const searchInput = document.querySelector('input[placeholder="搜索商铺服务..."]');
            const query = searchInput.value.trim();
            if (query) {
                console.log('搜索:', query);
                // 这里可以添加实际的搜索逻辑
            }
        }
        
        // 扫一扫功能 - 专用于平台服务二维码扫描
        function openQRScanner() {
            console.log('打开平台服务二维码扫描');
            // 专用于扫描平台生成的呼叫服务二维码，用于防伪验证和快速获取服务信息
            // 包括：商家身份验证、服务真实性验证、快速联系等
            alert('平台服务二维码扫描\n\n专用于扫描平台生成的呼叫服务二维码：\n• 商家身份验证二维码\n• 服务真实性验证二维码\n• 快速呼叫服务二维码\n\n此功能用于防伪验证，确保服务安全可靠');
        }
        
        // 广告详情功能
        function openAdDetail(adId) {
            console.log('打开广告详情:', adId);
            // 跳转到广告详情页或商铺页面
            window.location.href = `shop-detail.html?from=ad&id=${adId}`;
        }
        
        function viewAdDetail(adId) {
            console.log('查看广告详情:', adId);
            // 显示广告详情弹窗或跳转
            openAdDetail(adId);
        }
        
        // 显示商家选择指南
        function showMerchantGuide() {
            const merchantGuide = `
商家选择指南：

【认证标识说明】
• 实体认证：已验证营业执照的正规商家
• 信用良好：平台评估的优质商家
• 新店开业：新入驻的商家，服务待验证

【选择建议】
• 优先选择有认证标识的商家
• 查看商家评价和服务记录
• 确认营业时间和服务范围
• 了解收费标准和服务条款

【注意事项】
• 大额消费建议先咨询确认
• 保留消费凭证和聊天记录
• 如有疑问及时与商家沟通
• 发现问题及时反馈平台

客服热线：400-123-4567
            `;
            alert(merchantGuide);
        }

        // 显示投诉指南
        function showComplaintGuide() {
            const complaintGuide = `
投诉指南：

【投诉范围】
• 服务质量问题
• 价格争议
• 虚假宣传
• 态度恶劣
• 其他服务纠纷

【投诉流程】
1. 先与商家协商解决
2. 协商无果可向平台投诉
3. 提供相关证据材料
4. 平台调查核实
5. 给出处理结果

【投诉方式】
• 商家页面点击投诉按钮
• 拨打客服热线：400-123-4567
• 在线客服投诉
• 邮件投诉：<EMAIL>

【处理时效】
• 24小时内响应
• 3-7个工作日处理完成
            `;
            alert(complaintGuide);
        }

        // 显示服务免责声明
        function showServiceDisclaimer() {
            const disclaimer = `
服务免责声明：

1. 平台性质：本平台为商家信息展示平台，不参与具体服务。

2. 服务提供：所有服务由入驻商家独立提供，商家对服务质量负责。

3. 平台责任：平台不对商家服务质量、价格、时效等承担责任。

4. 用户责任：用户应自行判断商家服务，消费风险自担。

5. 争议处理：如有争议，建议先与商家协商，必要时寻求法律途径。

6. 信息准确性：商家信息由商家提供，平台不保证完全准确。

7. 服务变更：平台有权调整服务规则，商家有权调整服务内容。

详细条款请查看《用户协议》和《商家服务协议》
            `;
            alert(disclaimer);
        }
        
        // 城市选择
        function selectCity() {
            console.log('选择城市');
            // 这里可以打开城市选择页面
        }
        
        // 打开商铺详情页
        function openShopDetail(shopId) {
            console.log('打开商铺详情:', shopId);
            // 跳转到商铺详情页
            window.location.href = `shop-detail.html?id=${shopId}`;
        }
        
        // 联系商铺
        function contactShop(shopId) {
            console.log('联系商铺:', shopId);
            // 显示联系方式选择弹窗
            const choice = confirm('选择联系方式:\n1. 拨打电话\n2. 发送消息\n3. 查看位置');
            
            if (choice) {
                // 这里可以根据选择执行相应操作
                // 例如：调用微信小程序的拨打电话API
                console.log('执行联系操作');
            }
        }
        
        // 筛选功能
        let currentFilters = {
            type: 'all',
            distance: 'all',
            rating: 'all',
            status: 'all'
        };
        
        function filterByType(type) {
            currentFilters.type = type;
            
            // 重置所有分类按钮状态
            document.querySelectorAll('[onclick^="filterByType"]').forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            // 设置当前按钮为选中状态
            const currentBtn = document.querySelector(`[onclick="filterByType('${type}')"]`);
            if (currentBtn) {
                currentBtn.classList.remove('bg-slate-700', 'text-slate-300');
                currentBtn.classList.add('bg-blue-600', 'text-white');
            }
            
            // 筛选卡片
            const cards = document.querySelectorAll('.shop-card, [onclick^="openShopDetail"]');
            let visibleCount = 0;
            cards.forEach(card => {
                if (type === 'all' || card.dataset.type === type) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 如果没有匹配的卡片，显示提示
            if (visibleCount === 0 && type !== 'all') {
                console.log(`暂无${type}类型的商家`);
            }
            
            applyFilters();
        }
        
        function showAdvancedFilter() {
            const panel = document.getElementById('advancedFilter');
            panel.classList.toggle('hidden');
        }
        
        function filterDistance(distance) {
            currentFilters.distance = distance;
            console.log('筛选距离:', distance);
        }
        
        function filterRating(rating) {
            currentFilters.rating = rating;
            console.log('筛选评分:', rating);
        }
        
        function filterPrice(price) {
            currentFilters.price = price;
            console.log('筛选价格:', price);
            alert(`价格筛选\n\n已选择：${price === 'low' ? '经济实惠' : price === 'medium' ? '中等价位' : price === 'high' ? '高端消费' : '不限价格'}\n\n筛选结果将显示符合条件的商家`);
        }
        
        function filterStatus(status) {
            currentFilters.status = status;
            console.log('筛选状态:', status);
        }
        
        function toggleFilter(filterType) {
            console.log('切换筛选面板:', filterType);
            // 这里可以实现不同筛选类型的弹窗逻辑
            alert(`${filterType}筛选选项\n\n已在高级筛选面板中集成所有筛选功能\n请使用"筛选"按钮查看更多选项`);
            showAdvancedFilter();
        }
        
        function resetFilters() {
            currentFilters = {
                type: 'all',
                distance: 'all',
                rating: 'all',
                price: 'all',
                status: 'all'
            };
            updateFilterButtons();
            applyFilters();
        }
        
        function applyFilters() {
            console.log('应用筛选条件:', currentFilters);
            // 这里实现筛选逻辑
            // 在实际应用中会根据筛选条件过滤商家列表
            document.getElementById('advancedFilter').classList.add('hidden');
        }
        
        function updateFilterButtons() {
            // 更新筛选按钮的激活状态
            const typeButtons = document.querySelectorAll('[onclick^="filterByType"]');
            typeButtons.forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            // 激活当前选中的类型按钮
            const activeTypeBtn = document.querySelector(`[onclick="filterByType('${currentFilters.type}')"]`);
            if (activeTypeBtn) {
                activeTypeBtn.classList.remove('bg-slate-700', 'text-slate-300');
                activeTypeBtn.classList.add('bg-blue-600', 'text-white');
            }
        }
        
        // 收藏功能
        function toggleFavorite(shopId) {
            console.log('切换收藏状态:', shopId);
            // 这里实现收藏/取消收藏逻辑
            alert('收藏功能\n\n已添加到我的收藏\n可在个人中心查看收藏的商家');
        }
        
        // 商家详情页
        function openShopDetail(shopId) {
            console.log('打开商家详情:', shopId);
            alert(`商家详情页\n\n商家ID：${shopId}\n\n详情页面功能包括：\n• 商家基本信息和认证状态\n• 服务项目和价格\n• 用户评价和评分\n• 营业时间和地址\n• 联系方式和在线咨询\n• 服务图片展示\n• 收藏和分享功能`);
            // 这里可以跳转到具体的商家详情页面
        }
        
        // 联系商家
        function contactShop(phone) {
            console.log('联系商家:', phone);
            // 直接拨号
            window.location.href = `tel:${phone}`;
        }
        
        // 距离点击跳转导航功能已移至公共脚本 common.js
        
        // 绑定距离导航事件
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有距离图标添加点击事件
            document.querySelectorAll('[data-lucide="map-pin"]').forEach(icon => {
                const parent = icon.parentElement;
                if (parent && parent.textContent.includes('m') || parent.textContent.includes('km')) {
                    parent.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const shopCard = this.closest('[onclick^="openShopDetail"]');
                        const shopId = shopCard ? shopCard.getAttribute('onclick').match(/openShopDetail\('([^']+)'\)/)[1] : 'unknown';
                        openNavigation(shopId);
                    });
                }
            });
        });
        
        // 广告详情
        function openAdDetail(adId) {
            console.log('打开广告详情:', adId);
            window.location.href = 'group_buy.html';
        }
        
        function viewAdDetail(adId) {
            openAdDetail(adId);
        }
        
        // 发布弹窗功能
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }
        
        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }
        
        function navigateToPublish(type) {
            closePublishModal();
            window.location.href = `publish-${type}.html`;
        }
        
        // 优惠券弹窗功能
        function showCouponModal() {
            document.getElementById('couponModal').classList.remove('hidden');
        }
        
        function closeCouponModal() {
            document.getElementById('couponModal').classList.add('hidden');
        }
        
        // 绑定优惠券按钮点击事件
        document.querySelectorAll('.coupon-tag').forEach(button => {
            button.addEventListener('click', showCouponModal);
        });
        
        // 板块切换功能
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabName = this.dataset.tab;
                console.log('切换到板块:', tabName);
                // 这里可以添加实际的页面跳转逻辑
            });
        });
        
        // 分类筛选功能
        document.querySelectorAll('.category-filter').forEach(filter => {
            filter.addEventListener('click', function() {
                // 移除所有活跃状态
                document.querySelectorAll('.category-filter').forEach(f => {
                    f.classList.remove('bg-blue-500', 'text-white');
                    f.classList.add('bg-slate-100', 'text-slate-600');
                });
                
                // 添加当前活跃状态
                this.classList.remove('bg-slate-100', 'text-slate-600');
                this.classList.add('bg-blue-500', 'text-white');
                
                console.log('筛选分类:', this.textContent);
            });
        });
        
        // 电话按钮点击
        document.querySelectorAll('[data-lucide="phone"]').forEach(button => {
            button.parentElement.addEventListener('click', function() {
                console.log('拨打电话');
                // 这里可以添加实际的拨号功能
            });
        });
        
        // 产品缩略图点击
        document.querySelectorAll('.w-16.h-12').forEach(img => {
            img.addEventListener('click', function() {
                console.log('查看产品详情');
                // 这里可以添加跳转到产品详情的逻辑
            });
        });
        
        // 浏览模式切换功能
        let isWaterfallMode = false;
        
        function toggleViewMode() {
            isWaterfallMode = !isWaterfallMode;
            const shopList = document.getElementById('shopList');
            const toggleBtn = document.querySelector('[onclick="toggleViewMode()"]');
            const toggleIcon = toggleBtn.querySelector('i');
            
            if (isWaterfallMode) {
                // 切换到瀑布流模式
                shopList.innerHTML = createWaterfallView();
                shopList.className = 'max-w-md mx-auto px-4 py-2 pb-24';
                toggleIcon.setAttribute('data-lucide', 'list');
                toggleBtn.title = '切换到卡片模式';
            } else {
                // 切换到卡片模式
                location.reload(); // 简单重新加载页面恢复卡片模式
            }
            
            lucide.createIcons();
        }
        
        function createWaterfallView() {
            const products = [
                { name: '麻辣香锅', price: '￥28', distance: '500m', shop: '川味小厨', image: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=300&fit=crop&crop=center' },
                { name: '煎饼果子', price: '￥8', distance: '150m', shop: '老王早餐摊', image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop&crop=center' },
                { name: '手机壳', price: '￥15', distance: '线上', shop: '数码配件店', image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=300&fit=crop&crop=center' },
                { name: '新鲜苹果', price: '￥12/斤', distance: '500m', shop: '老王水果摊', image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=300&fit=crop&crop=center' },
                { name: '宫保鸡丁', price: '￥22', distance: '500m', shop: '川味小厨', image: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=300&fit=crop&crop=center' },
                { name: '豆浆包子', price: '￥6', distance: '150m', shop: '老王早餐摊', image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop&crop=center' },
                { name: '数据线', price: '￥25', distance: '线上', shop: '数码配件店', image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop&crop=center' },
                { name: '香蕉', price: '￥8/斤', distance: '500m', shop: '老王水果摊', image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=300&h=300&fit=crop&crop=center' },
                { name: '回锅肉', price: '￥26', distance: '500m', shop: '川味小厨', image: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=300&fit=crop&crop=center' },
                { name: '油条', price: '￥3', distance: '150m', shop: '老王早餐摊', image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop&crop=center' }
            ];
            
            let html = '<div class="grid grid-cols-2 gap-3">';
            
            products.forEach((product, index) => {
                html += `
                    <div class="bg-slate-800 rounded-lg overflow-hidden border border-slate-700 cursor-pointer hover:border-slate-600 transition-colors" onclick="openProductDetail('${index}')">
                        <img src="${product.image}" alt="${product.name}" class="w-full h-32 object-cover">
                        <div class="p-3">
                            <h3 class="text-slate-100 font-medium text-sm mb-1 line-clamp-1">${product.name}</h3>
                            <p class="text-orange-400 font-semibold text-sm mb-1">${product.price}</p>
                            <div class="flex items-center justify-between text-xs text-slate-400">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                                    <span>${product.distance}</span>
                                </span>
                                <span class="line-clamp-1">${product.shop}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }
        
        function openProductDetail(productIndex) {
            console.log('打开产品详情:', productIndex);
            alert('产品详情\n\n点击产品可查看详细信息\n包括：产品介绍、价格详情、商家信息、用户评价等');
        }
        
        // 商家快捷入口
        function quickMerchantAccess() {
            // 检查用户是否已有商铺（模拟检测逻辑）
            const hasShop = localStorage.getItem('userHasShop') === 'true' || Math.random() > 0.6;
            
            if (hasShop) {
                // 已有商铺，直接跳转到产品管理
                window.location.href = 'my-shop-products.html';
            } else {
                // 无商铺，询问用户是否要创建
                const createShop = confirm('您还没有商铺\n\n是否立即创建商铺开始经营？\n\n点击"确定"创建商铺\n点击"取消"返回浏览');
                if (createShop) {
                    window.location.href = 'publish-shop.html';
                }
            }
        }
        
        // 回车搜索和初始化筛选
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[placeholder="搜索商铺服务..."]');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });
            }
            
            // 页面加载时执行初始筛选
            filterByType('all');
        });

        // 自动关闭提醒
        document.addEventListener('DOMContentLoaded', () => {
            const reminder = document.getElementById('shop-reminder');
            if (!reminder) return;

            const closeBtn = document.getElementById('close-reminder-btn');
            const countdownTimer = document.getElementById('reminder-countdown-timer');
            let countdown = 3;
            let interval;
            
            // 隐藏提醒
            function hideReminder() {
                reminder.style.opacity = '0';
                setTimeout(() => {
                    reminder.style.display = 'none';
                }, 300);
            }

            const startCountdown = () => {
                interval = setInterval(() => {
                    countdown--;
                    if (countdownTimer) {
                        countdownTimer.textContent = `${countdown}s`;
                    }
                    if (countdown <= 0) {
                        clearInterval(interval);
                        hideReminder();
                    }
                }, 1000);
            };

            const hideReminder = () => {
                clearInterval(interval);
                reminder.style.transition = 'opacity 0.5s ease';
                reminder.style.opacity = '0';
                setTimeout(() => reminder.style.display = 'none', 500);
            };

            if (closeBtn) {
                closeBtn.addEventListener('click', hideReminder);
            }

            const observer = new IntersectionObserver((entries) => {
                if (entries[0].isIntersecting) {
                    startCountdown();
                    observer.disconnect();
                }
            });

            observer.observe(reminder);
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const banner = document.getElementById('info-banner');
            if (!banner) return;

            const closeBtn = document.getElementById('close-banner-btn');
            const countdownTimer = document.getElementById('countdown-timer');
            
            let countdown = 5;
            
            const interval = setInterval(() => {
                countdown--;
                if (countdownTimer) {
                    countdownTimer.textContent = `${countdown}s`;
                }
                if (countdown <= 0) {
                    clearInterval(interval);
                    banner.style.transition = 'opacity 0.5s ease';
                    banner.style.opacity = '0';
                    setTimeout(() => banner.style.display = 'none', 500);
                }
            }, 1000);

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    clearInterval(interval);
                    banner.style.transition = 'opacity 0.5s ease';
                    banner.style.opacity = '0';
                    setTimeout(() => banner.style.display = 'none', 500);
                });
            }
            
            lucide.createIcons();
        });
    </script>
</body>
</html>