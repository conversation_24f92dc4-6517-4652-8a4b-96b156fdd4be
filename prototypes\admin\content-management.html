<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容管理 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .tab-active {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
        }
        .status-active { color: #10b981; }
        .status-inactive { color: #ef4444; }
        .status-pending { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 顶部导航 -->
    <nav class="gradient-bg p-4 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i data-lucide="layout-dashboard" class="w-8 h-8"></i>
                <h1 class="text-xl font-bold">本地助手管理后台</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm">管理员</span>
                <i data-lucide="user" class="w-6 h-6"></i>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold mb-2">四大板块内容管理</h2>
            <p class="text-gray-400">管理商铺、信息、出行、跑腿四大板块的所有内容</p>
        </div>

        <!-- 板块选择标签 -->
        <div class="flex space-x-2 mb-6">
            <button onclick="switchTab('shops')" id="tab-shops" class="tab-active px-4 py-2 rounded-lg font-medium transition-all">
                <i data-lucide="store" class="w-4 h-4 inline mr-2"></i>
                商铺板块
            </button>
            <button onclick="switchTab('info')" id="tab-info" class="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-lg font-medium transition-all">
                <i data-lucide="info" class="w-4 h-4 inline mr-2"></i>
                信息板块
            </button>
            <button onclick="switchTab('transport')" id="tab-transport" class="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-lg font-medium transition-all">
                <i data-lucide="car" class="w-4 h-4 inline mr-2"></i>
                出行板块
            </button>
            <button onclick="switchTab('errands')" id="tab-errands" class="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-lg font-medium transition-all">
                <i data-lucide="package" class="w-4 h-4 inline mr-2"></i>
                跑腿板块
            </button>
        </div>

        <!-- 商铺板块管理 -->
        <div id="content-shops" class="tab-content">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">商铺管理</h3>
                    <div class="flex space-x-2">
                        <input type="text" placeholder="搜索商铺..." class="px-3 py-2 bg-gray-700 rounded-lg text-white placeholder-gray-400">
                        <button class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                            <i data-lucide="search" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">总商铺数</p>
                                <p class="text-2xl font-bold">1,234</p>
                            </div>
                            <i data-lucide="store" class="w-8 h-8 text-blue-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">已认证</p>
                                <p class="text-2xl font-bold text-green-400">856</p>
                            </div>
                            <i data-lucide="shield-check" class="w-8 h-8 text-green-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">待审核</p>
                                <p class="text-2xl font-bold text-yellow-400">123</p>
                            </div>
                            <i data-lucide="clock" class="w-8 h-8 text-yellow-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">已封禁</p>
                                <p class="text-2xl font-bold text-red-400">45</p>
                            </div>
                            <i data-lucide="ban" class="w-8 h-8 text-red-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 商铺列表 -->
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="pb-3 text-gray-400">商铺信息</th>
                                <th class="pb-3 text-gray-400">分类</th>
                                <th class="pb-3 text-gray-400">状态</th>
                                <th class="pb-3 text-gray-400">创建时间</th>
                                <th class="pb-3 text-gray-400">操作</th>
                            </tr>
                        </thead>
                        <tbody id="shopsTable">
                            <!-- 商铺数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 信息板块管理 -->
        <div id="content-info" class="tab-content hidden">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">信息管理</h3>
                    <div class="flex space-x-2">
                        <select class="px-3 py-2 bg-gray-700 rounded-lg text-white">
                            <option>全部分类</option>
                            <option>招聘</option>
                            <option>出租</option>
                            <option>求租</option>
                            <option>出售</option>
                            <option>闲置</option>
                            <option>求购</option>
                            <option>求职</option>
                            <option>其他</option>
                        </select>
                        <input type="text" placeholder="搜索信息..." class="px-3 py-2 bg-gray-700 rounded-lg text-white placeholder-gray-400">
                        <button class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                            <i data-lucide="search" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 信息统计 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">总信息数</p>
                                <p class="text-2xl font-bold">5,678</p>
                            </div>
                            <i data-lucide="info" class="w-8 h-8 text-blue-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">今日新增</p>
                                <p class="text-2xl font-bold text-green-400">89</p>
                            </div>
                            <i data-lucide="plus-circle" class="w-8 h-8 text-green-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">违规处理</p>
                                <p class="text-2xl font-bold text-red-400">12</p>
                            </div>
                            <i data-lucide="triangle-alert" class="w-8 h-8 text-red-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 信息列表 -->
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="pb-3 text-gray-400">信息标题</th>
                                <th class="pb-3 text-gray-400">分类</th>
                                <th class="pb-3 text-gray-400">发布者</th>
                                <th class="pb-3 text-gray-400">状态</th>
                                <th class="pb-3 text-gray-400">发布时间</th>
                                <th class="pb-3 text-gray-400">操作</th>
                            </tr>
                        </thead>
                        <tbody id="infoTable">
                            <!-- 信息数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 出行板块管理 -->
        <div id="content-transport" class="tab-content hidden">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">出行管理</h3>
                    <div class="flex space-x-2">
                        <select class="px-3 py-2 bg-gray-700 rounded-lg text-white">
                            <option>全部类型</option>
                            <option>车找人</option>
                            <option>人找车</option>
                            <option>货找车</option>
                            <option>车找货</option>
                            <option>代驾服务</option>
                        </select>
                        <input type="text" placeholder="搜索出行信息..." class="px-3 py-2 bg-gray-700 rounded-lg text-white placeholder-gray-400">
                        <button class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                            <i data-lucide="search" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 出行统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">总出行信息</p>
                                <p class="text-2xl font-bold">2,345</p>
                            </div>
                            <i data-lucide="car" class="w-8 h-8 text-blue-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">车找人</p>
                                <p class="text-2xl font-bold text-green-400">856</p>
                            </div>
                            <i data-lucide="users" class="w-8 h-8 text-green-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">人找车</p>
                                <p class="text-2xl font-bold text-yellow-400">789</p>
                            </div>
                            <i data-lucide="user-search" class="w-8 h-8 text-yellow-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">安全风险</p>
                                <p class="text-2xl font-bold text-red-400">8</p>
                            </div>
                            <i data-lucide="alert-triangle" class="w-8 h-8 text-red-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 出行列表 -->
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="pb-3 text-gray-400">路线信息</th>
                                <th class="pb-3 text-gray-400">类型</th>
                                <th class="pb-3 text-gray-400">发布者</th>
                                <th class="pb-3 text-gray-400">联系方式</th>
                                <th class="pb-3 text-gray-400">状态</th>
                                <th class="pb-3 text-gray-400">操作</th>
                            </tr>
                        </thead>
                        <tbody id="transportTable">
                            <!-- 出行数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 跑腿板块管理 -->
        <div id="content-errands" class="tab-content hidden">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">跑腿管理</h3>
                    <div class="flex space-x-2">
                        <select class="px-3 py-2 bg-gray-700 rounded-lg text-white">
                            <option>全部类型</option>
                            <option>代买代购</option>
                            <option>代送服务</option>
                            <option>代办事务</option>
                            <option>其他跑腿</option>
                        </select>
                        <input type="text" placeholder="搜索跑腿信息..." class="px-3 py-2 bg-gray-700 rounded-lg text-white placeholder-gray-400">
                        <button class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                            <i data-lucide="search" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 跑腿统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">总跑腿信息</p>
                                <p class="text-2xl font-bold">1,567</p>
                            </div>
                            <i data-lucide="package" class="w-8 h-8 text-blue-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">已认证服务者</p>
                                <p class="text-2xl font-bold text-green-400">234</p>
                            </div>
                            <i data-lucide="user-check" class="w-8 h-8 text-green-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">待认证</p>
                                <p class="text-2xl font-bold text-yellow-400">45</p>
                            </div>
                            <i data-lucide="clock" class="w-8 h-8 text-yellow-400"></i>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">完成撮合</p>
                                <p class="text-2xl font-bold text-purple-400">3,456</p>
                            </div>
                            <i data-lucide="circle-check" class="w-8 h-8 text-purple-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 跑腿列表 -->
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="pb-3 text-gray-400">服务内容</th>
                                <th class="pb-3 text-gray-400">类型</th>
                                <th class="pb-3 text-gray-400">发布者</th>
                                <th class="pb-3 text-gray-400">服务者</th>
                                <th class="pb-3 text-gray-400">状态</th>
                                <th class="pb-3 text-gray-400">操作</th>
                            </tr>
                        </thead>
                        <tbody id="errandsTable">
                            <!-- 跑腿数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 标签切换功能
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有标签样式
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.className = 'bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-lg font-medium transition-all';
            });
            
            // 显示选中的内容
            document.getElementById(`content-${tabName}`).classList.remove('hidden');
            
            // 激活选中的标签
            document.getElementById(`tab-${tabName}`).className = 'tab-active px-4 py-2 rounded-lg font-medium transition-all';
            
            // 加载对应的数据
            loadTabData(tabName);
        }

        // 加载标签数据
        function loadTabData(tabName) {
            switch(tabName) {
                case 'shops':
                    loadShopsData();
                    break;
                case 'info':
                    loadInfoData();
                    break;
                case 'transport':
                    loadTransportData();
                    break;
                case 'errands':
                    loadErrandsData();
                    break;
            }
        }

        // 加载商铺数据
        function loadShopsData() {
            const shopsData = [
                { name: '老王川菜馆', category: '实体店铺', status: 'active', time: '2024-01-15', phone: '138****5678' },
                { name: '小李美发店', category: '实体店铺', status: 'pending', time: '2024-01-14', phone: '139****1234' },
                { name: '张师傅修车', category: '上门师傅', status: 'active', time: '2024-01-13', phone: '137****9876' },
                { name: '网络科技公司', category: '在线网店', status: 'banned', time: '2024-01-12', phone: '136****5432' },
                { name: '流动水果摊', category: '流动摊贩', status: 'active', time: '2024-01-11', phone: '135****8765' }
            ];
            
            const tbody = document.getElementById('shopsTable');
            tbody.innerHTML = shopsData.map(shop => `
                <tr class="border-b border-gray-700 hover:bg-gray-700">
                    <td class="py-3">
                        <div>
                            <p class="font-medium">${shop.name}</p>
                            <p class="text-sm text-gray-400">${shop.phone}</p>
                        </div>
                    </td>
                    <td class="py-3">
                        <span class="px-2 py-1 bg-blue-600 text-xs rounded">${shop.category}</span>
                    </td>
                    <td class="py-3">
                        <span class="${shop.status === 'active' ? 'status-active' : shop.status === 'pending' ? 'status-pending' : 'status-inactive'}">
                            ${shop.status === 'active' ? '正常' : shop.status === 'pending' ? '待审核' : '已封禁'}
                        </span>
                    </td>
                    <td class="py-3 text-gray-400">${shop.time}</td>
                    <td class="py-3">
                        <div class="flex space-x-2">
                            <button class="text-blue-400 hover:text-blue-300" onclick="viewShop('${shop.name}')">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button class="text-green-400 hover:text-green-300" onclick="editShop('${shop.name}')">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300" onclick="banShop('${shop.name}')">
                                <i data-lucide="ban" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 加载信息数据
        function loadInfoData() {
            const infoData = [
                { title: '招聘前端开发工程师', category: '招聘', publisher: '科技公司', status: 'active', time: '2024-01-15' },
                { title: '出租两室一厅', category: '出租', publisher: '张女士', status: 'active', time: '2024-01-14' },
                { title: '求购二手电脑', category: '求购', publisher: '李先生', status: 'pending', time: '2024-01-13' },
                { title: '出售闲置家具', category: '闲置', publisher: '王女士', status: 'active', time: '2024-01-12' },
                { title: '求职会计岗位', category: '求职', publisher: '陈小姐', status: 'active', time: '2024-01-11' }
            ];
            
            const tbody = document.getElementById('infoTable');
            tbody.innerHTML = infoData.map(info => `
                <tr class="border-b border-gray-700 hover:bg-gray-700">
                    <td class="py-3">
                        <p class="font-medium">${info.title}</p>
                    </td>
                    <td class="py-3">
                        <span class="px-2 py-1 bg-purple-600 text-xs rounded">${info.category}</span>
                    </td>
                    <td class="py-3 text-gray-400">${info.publisher}</td>
                    <td class="py-3">
                        <span class="${info.status === 'active' ? 'status-active' : 'status-pending'}">
                            ${info.status === 'active' ? '正常' : '待审核'}
                        </span>
                    </td>
                    <td class="py-3 text-gray-400">${info.time}</td>
                    <td class="py-3">
                        <div class="flex space-x-2">
                            <button class="text-blue-400 hover:text-blue-300">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button class="text-green-400 hover:text-green-300">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 加载出行数据
        function loadTransportData() {
            const transportData = [
                { route: '北京 → 天津', type: '车找人', publisher: '司机老李', contact: '138****5678', status: 'active' },
                { route: '上海 → 杭州', type: '人找车', publisher: '乘客小王', contact: '139****1234', status: 'active' },
                { route: '广州 → 深圳', type: '货找车', publisher: '物流公司', contact: '137****9876', status: 'pending' },
                { route: '成都市内', type: '代驾服务', publisher: '代驾师傅', contact: '136****5432', status: 'active' },
                { route: '重庆 → 成都', type: '车找货', publisher: '货车司机', contact: '135****8765', status: 'active' }
            ];
            
            const tbody = document.getElementById('transportTable');
            tbody.innerHTML = transportData.map(transport => `
                <tr class="border-b border-gray-700 hover:bg-gray-700">
                    <td class="py-3">
                        <p class="font-medium">${transport.route}</p>
                    </td>
                    <td class="py-3">
                        <span class="px-2 py-1 bg-green-600 text-xs rounded">${transport.type}</span>
                    </td>
                    <td class="py-3 text-gray-400">${transport.publisher}</td>
                    <td class="py-3 text-gray-400">${transport.contact}</td>
                    <td class="py-3">
                        <span class="${transport.status === 'active' ? 'status-active' : 'status-pending'}">
                            ${transport.status === 'active' ? '正常' : '待审核'}
                        </span>
                    </td>
                    <td class="py-3">
                        <div class="flex space-x-2">
                            <button class="text-blue-400 hover:text-blue-300">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button class="text-green-400 hover:text-green-300">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300">
                                <i data-lucide="triangle-alert" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 加载跑腿数据
        function loadErrandsData() {
            const errandsData = [
                { content: '代买生鲜蔬菜', type: '代买代购', publisher: '张女士', runner: '跑腿小哥A', status: 'completed' },
                { content: '代送文件到公司', type: '代送服务', publisher: '李先生', runner: '跑腿小哥B', status: 'in_progress' },
                { content: '代办银行业务', type: '代办事务', publisher: '王先生', runner: '未接单', status: 'pending' },
                { content: '代取快递包裹', type: '代送服务', publisher: '陈女士', runner: '跑腿小哥C', status: 'completed' },
                { content: '其他跑腿服务', type: '其他跑腿', publisher: '赵先生', runner: '跑腿小哥D', status: 'in_progress' }
            ];
            
            const tbody = document.getElementById('errandsTable');
            tbody.innerHTML = errandsData.map(errand => `
                <tr class="border-b border-gray-700 hover:bg-gray-700">
                    <td class="py-3">
                        <p class="font-medium">${errand.content}</p>
                    </td>
                    <td class="py-3">
                        <span class="px-2 py-1 bg-orange-600 text-xs rounded">${errand.type}</span>
                    </td>
                    <td class="py-3 text-gray-400">${errand.publisher}</td>
                    <td class="py-3 text-gray-400">${errand.runner}</td>
                    <td class="py-3">
                        <span class="${errand.status === 'completed' ? 'status-active' : errand.status === 'in_progress' ? 'status-pending' : 'status-inactive'}">
                            ${errand.status === 'completed' ? '已完成' : errand.status === 'in_progress' ? '进行中' : '待接单'}
                        </span>
                    </td>
                    <td class="py-3">
                        <div class="flex space-x-2">
                            <button class="text-blue-400 hover:text-blue-300">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button class="text-green-400 hover:text-green-300">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-purple-400 hover:text-purple-300">
                                <i data-lucide="user-check" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 商铺操作函数
        function viewShop(name) {
            alert(`查看商铺：${name}`);
        }

        function editShop(name) {
            alert(`编辑商铺：${name}`);
        }

        function banShop(name) {
            if (confirm(`确定要封禁商铺：${name}？`)) {
                alert(`已封禁商铺：${name}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadShopsData(); // 默认加载商铺数据
        });
    </script>
</body>
</html>