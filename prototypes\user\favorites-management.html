<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的收藏 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .tab-active {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
        .favorite-card {
            transition: all 0.3s ease;
        }
        .favorite-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="history.back()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                <h1 class="text-lg font-semibold text-slate-200">我的收藏</h1>
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" onclick="searchFavorites()">
                    <i data-lucide="search" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 收藏统计 -->
    <div class="max-w-md mx-auto px-4 py-6">
        <div class="bg-slate-800 rounded-xl p-6 mb-6">
            <div class="grid grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-400">23</div>
                    <div class="text-sm text-slate-400 mt-1">商铺</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-400">15</div>
                    <div class="text-sm text-slate-400 mt-1">信息</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-400">8</div>
                    <div class="text-sm text-slate-400 mt-1">出行</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-orange-400">12</div>
                    <div class="text-sm text-slate-400 mt-1">跑腿</div>
                </div>
            </div>
        </div>

        <!-- 标签页切换 -->
        <div class="flex bg-slate-800 rounded-lg p-1 mb-6 overflow-x-auto">
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium transition-all tab-active" data-tab="all">
                全部 (58)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="shops">
                商铺 (23)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="information">
                信息 (15)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="transportation">
                出行 (8)
            </button>
            <button class="tab-btn flex-shrink-0 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="errands">
                跑腿 (12)
            </button>
        </div>

        <!-- 全部收藏 -->
        <div id="all" class="tab-content space-y-4">
            <!-- 商铺收藏 -->
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=80&h=80&fit=crop&crop=center" alt="餐厅" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">老北京炸酱面馆</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-500 bg-opacity-20 text-blue-400">
                                        <i data-lucide="store" class="w-3 h-3 mr-1"></i>
                                        商铺
                                    </span>
                                    <div class="flex items-center text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm ml-1">4.8</span>
                                    </div>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">正宗老北京味道，手工制作炸酱面</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <i data-lucide="map-pin" class="w-4 h-4 mr-1"></i>
                                    <span>距离 0.8km</span>
                                    <span class="mx-2">•</span>
                                    <span>收藏于 2024-01-15</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('shop1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 信息收藏 -->
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=80&h=80&fit=crop&crop=center" alt="房屋" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">精装两室一厅出租</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-500 bg-opacity-20 text-green-400">
                                        <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                                        信息
                                    </span>
                                    <span class="text-sm text-slate-400">房屋租赁</span>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">地铁口附近，交通便利，家具齐全</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <i data-lucide="map-pin" class="w-4 h-4 mr-1"></i>
                                    <span>朝阳区</span>
                                    <span class="mx-2">•</span>
                                    <span class="text-orange-400 font-medium">¥3500/月</span>
                                    <span class="mx-2">•</span>
                                    <span>收藏于 2024-01-12</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('info1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 出行收藏 -->
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">机场拼车服务</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-500 bg-opacity-20 text-purple-400">
                                        <i data-lucide="car" class="w-3 h-3 mr-1"></i>
                                        出行
                                    </span>
                                    <div class="flex items-center text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm ml-1">4.9</span>
                                    </div>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">朝阳区 → 首都机场T3航站楼</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <span class="text-blue-400 font-medium">¥45/人</span>
                                    <span class="mx-2">•</span>
                                    <span>约45分钟</span>
                                    <span class="mx-2">•</span>
                                    <span>收藏于 2024-01-10</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('transport1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 跑腿收藏 -->
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">代取快递服务</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-500 bg-opacity-20 text-orange-400">
                                        <i data-lucide="package" class="w-3 h-3 mr-1"></i>
                                        跑腿
                                    </span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-500 bg-opacity-20 text-green-400">
                                        急单
                                    </span>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">帮忙代取菜鸟驿站快递，送到楼下</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <span class="text-green-400 font-medium">¥8</span>
                                    <span class="mx-2">•</span>
                                    <span>预计30分钟</span>
                                    <span class="mx-2">•</span>
                                    <span>收藏于 2024-01-08</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('errand1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 更多收藏项... -->
            <div class="text-center py-8">
                <button class="bg-slate-700 hover:bg-slate-600 text-slate-300 px-6 py-3 rounded-lg transition-colors" onclick="loadMore()">
                    加载更多收藏
                </button>
            </div>
        </div>

        <!-- 商铺收藏 -->
        <div id="shops" class="tab-content space-y-4 hidden">
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=80&h=80&fit=crop&crop=center" alt="餐厅" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">老北京炸酱面馆</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <div class="flex items-center text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm ml-1">4.8</span>
                                    </div>
                                    <span class="text-sm text-slate-400">中式餐厅</span>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">正宗老北京味道，手工制作炸酱面</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <i data-lucide="map-pin" class="w-4 h-4 mr-1"></i>
                                    <span>距离 0.8km</span>
                                    <span class="mx-2">•</span>
                                    <span>营业中</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('shop1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息收藏 -->
        <div id="information" class="tab-content space-y-4 hidden">
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=80&h=80&fit=crop&crop=center" alt="房屋" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">精装两室一厅出租</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="text-sm text-slate-400">房屋租赁</span>
                                    <span class="text-orange-400 font-medium">¥3500/月</span>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">地铁口附近，交通便利，家具齐全</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <i data-lucide="map-pin" class="w-4 h-4 mr-1"></i>
                                    <span>朝阳区</span>
                                    <span class="mx-2">•</span>
                                    <span>发布于 2024-01-12</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('info1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 出行收藏 -->
        <div id="transportation" class="tab-content space-y-4 hidden">
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">机场拼车服务</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <div class="flex items-center text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm ml-1">4.9</span>
                                    </div>
                                    <span class="text-blue-400 font-medium">¥45/人</span>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">朝阳区 → 首都机场T3航站楼</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <span>约45分钟</span>
                                    <span class="mx-2">•</span>
                                    <span>每日发车</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('transport1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 跑腿收藏 -->
        <div id="errands" class="tab-content space-y-4 hidden">
            <div class="favorite-card bg-slate-800 rounded-xl p-4">
                <div class="flex items-start space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-slate-200 truncate">代取快递服务</h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-500 bg-opacity-20 text-green-400">
                                        急单
                                    </span>
                                    <span class="text-green-400 font-medium">¥8</span>
                                </div>
                                <p class="text-sm text-slate-400 mt-2">帮忙代取菜鸟驿站快递，送到楼下</p>
                                <div class="flex items-center text-sm text-slate-500 mt-1">
                                    <span>预计30分钟</span>
                                    <span class="mx-2">•</span>
                                    <span>可立即接单</span>
                                </div>
                            </div>
                            <button class="text-red-400 hover:text-red-300 transition-colors" onclick="removeFavorite('errand1')">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="text-center py-16 hidden">
            <div class="w-24 h-24 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="heart" class="w-12 h-12 text-slate-500"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-300 mb-2">暂无收藏</h3>
            <p class="text-slate-500 mb-6">快去收藏你感兴趣的内容吧</p>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors" onclick="goToHome()">
                去首页看看
            </button>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.dataset.tab;
                
                // 更新按钮状态
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('tab-active');
                    b.classList.add('text-slate-400');
                });
                btn.classList.add('tab-active');
                btn.classList.remove('text-slate-400');
                
                // 显示对应内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tab).classList.remove('hidden');
            });
        });

        // 搜索收藏
        function searchFavorites() {
            alert('正在打开搜索功能...');
        }

        // 移除收藏
        function removeFavorite(id) {
            if (confirm('确定要取消收藏吗？')) {
                // 这里应该调用API移除收藏
                alert('已取消收藏');
            }
        }

        // 加载更多
        function loadMore() {
            alert('正在加载更多收藏...');
        }

        // 去首页
        function goToHome() {
            window.location.href = '../index.html';
        }
    </script>
</body>
</html>