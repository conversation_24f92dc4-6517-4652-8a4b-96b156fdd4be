/* ==========================================================================
   本地助手 - 统一设计系统 v2.0
   Design System for Local Assistant Platform
   ========================================================================== */

/* CSS 自定义属性 (CSS Variables) */
:root {
  /* 主题色彩系统 - 深色主题 */
  --primary-bg: #0f0f23;
  --secondary-bg: #1a1a2e;
  --tertiary-bg: #16213e;
  --card-bg: #1e1e3f;
  --card-secondary: #252547;
  --card-hover: #2a2d4a;
  --border-color: #334155;
  --border-light: #475569;
  
  /* 品牌主色系统 */
  --brand-blue: #3b82f6;
  --brand-blue-dark: #2563eb;
  --brand-blue-darker: #1d4ed8;
  --brand-purple: #8b5cf6;
  --brand-purple-dark: #7c3aed;
  --brand-purple-darker: #6d28d9;
  --brand-cyan: #06b6d4;
  --brand-green: #10b981;
  
  /* 渐变色系统 */
  --gradient-primary: linear-gradient(135deg, #3b82f6, #8b5cf6);
  --gradient-secondary: linear-gradient(135deg, #06b6d4, #3b82f6);
  --gradient-success: linear-gradient(135deg, #10b981, #059669);
  --gradient-warning: linear-gradient(135deg, #f59e0b, #f97316);
  --gradient-bg: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  
  /* 功能色彩系统 */
  --success: #22c55e;
  --success-dark: #16a34a;
  --warning: #f59e0b;
  --warning-dark: #d97706;
  --error: #ef4444;
  --error-dark: #dc2626;
  --info: #06b6d4;
  --info-dark: #0891b2;
  
  /* 文字颜色系统 */
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-disabled: #64748b;
  --text-inverse: #1e293b;
  
  /* 间距系统 (4px 基础网格) */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;
  
  /* 圆角系统 */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  
  /* 字体系统 */
  --font-family-sans: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', 'Consolas', monospace;
  
  /* 字号系统 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;
  --text-5xl: 48px;
  
  /* 字重系统 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==========================================================================
   基础样式重置
   ========================================================================== */

/* 响应式基础样式 */
html, body {
  max-width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: var(--font-family-sans);
  background-color: var(--primary-bg);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

*, *::before, *::after {
  box-sizing: border-box;
}

img, .container, .wrapper {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* ==========================================================================
   组件样式系统
   ========================================================================== */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: var(--leading-tight);
  transition: var(--transition-normal);
  cursor: pointer;
  border: none;
  text-decoration: none;
  user-select: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--card-hover);
  border-color: var(--border-light);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

/* 卡片组件 */
.card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background-color: var(--card-hover);
}

.card-compact {
  padding: var(--space-md);
}

/* 表单组件 */
.form-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--brand-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: var(--card-hover);
}

.form-input::placeholder {
  color: var(--text-disabled);
}

/* 导航组件 */
.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  cursor: pointer;
}

.nav-item:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--gradient-primary);
  color: white;
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
}

.badge-success {
  background: var(--gradient-success);
  color: white;
}

.badge-warning {
  background: var(--gradient-warning);
  color: white;
}

.badge-info {
  background-color: var(--info);
  color: white;
}

.badge-error {
  background-color: var(--error);
  color: white;
}

/* 工具类 */
.gradient-bg {
  background: var(--gradient-bg);
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==========================================================================
   响应式断点
   ========================================================================== */

/* 移动端优先设计 */
@media (min-width: 640px) {
  .sm\:text-base { font-size: var(--text-base); }
  .sm\:p-6 { padding: var(--space-lg); }
}

@media (min-width: 768px) {
  .md\:text-lg { font-size: var(--text-lg); }
  .md\:p-8 { padding: var(--space-xl); }
}

@media (min-width: 1024px) {
  .lg\:text-xl { font-size: var(--text-xl); }
  .lg\:p-10 { padding: 40px; }
}

/* ==========================================================================
   无障碍访问性
   ========================================================================== */

/* 聚焦指示器 */
.focus\:ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* 无障碍辅助 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 键盘导航样式 */
body.keyboard-navigation *:focus {
    outline: 2px solid var(--brand-blue);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

body:not(.keyboard-navigation) *:focus {
    outline: none;
}

/* 聚焦指示器 */
*:focus-visible {
    outline: 2px solid var(--brand-blue);
    outline-offset: 2px;
}

/* 加载状态样式 */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid var(--brand-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

.loading .loading-text {
    opacity: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮加载状态 */
button.loading {
    color: transparent;
}

button.loading::after {
    border-top-color: currentColor;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading::after {
        animation: none;
        border: 2px solid var(--brand-blue);
        border-radius: 50%;
    }
}
  --font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Consolas', monospace;
  
  /* 字号系统 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;
  
  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}

/* 基础重置样式 */
html, body {
  max-width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: var(--font-family);
  background: var(--primary-bg);
  color: var(--text-primary);
}

*, *::before, *::after {
  box-sizing: border-box;
}

img, .container, .wrapper {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.container {
  width: 100%;
  max-width: 100%;
}

.wrapper {
  width: 100%;
  max-width: 100%;
}

/* 渐变背景系统 */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.gradient-brand {
  background: linear-gradient(135deg, var(--brand-blue) 0%, var(--brand-purple) 100%);
}

.gradient-tech {
  background: linear-gradient(135deg, var(--tech-blue) 0%, var(--tech-purple) 50%, var(--brand-cyan) 100%);
}

.gradient-success {
  background: linear-gradient(45deg, var(--success), #059669);
}

.gradient-warning {
  background: linear-gradient(45deg, var(--warning), #d97706);
}

.gradient-error {
  background: linear-gradient(45deg, var(--error), #dc2626);
}

/* 卡片组件系统 */
.card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-secondary {
  background: var(--card-secondary);
}

.card-compact {
  padding: var(--space-md);
}

/* 按钮组件系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--text-sm);
  line-height: var(--leading-tight);
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background: var(--brand-blue);
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--card-secondary);
  border-color: var(--brand-blue);
}

.btn-gradient {
  background: var(--gradient-brand);
  color: white;
}

.btn-sm {
  padding: 6px var(--space-sm);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
}

/* 表单组件系统 */
.form-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--brand-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* 标签组件系统 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 4px var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  line-height: var(--leading-tight);
}

.tag-primary {
  background: rgba(59, 130, 246, 0.1);
  color: var(--brand-blue);
}

.tag-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.tag-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.tag-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

/* 导航组件系统 */
.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: var(--card-bg);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--brand-blue);
  color: white;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.space-x-2 > * + * { margin-left: var(--space-xs); }
.space-x-4 > * + * { margin-left: var(--space-sm); }
.space-y-2 > * + * { margin-top: var(--space-xs); }
.space-y-4 > * + * { margin-top: var(--space-sm); }

.mb-2 { margin-bottom: var(--space-xs); }
.mb-4 { margin-bottom: var(--space-sm); }
.mb-6 { margin-bottom: var(--space-md); }
.mb-8 { margin-bottom: var(--space-lg); }

.p-2 { padding: var(--space-xs); }
.p-4 { padding: var(--space-sm); }
.p-6 { padding: var(--space-md); }
.p-8 { padding: var(--space-lg); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* 响应式断点 */
@media (min-width: 640px) {
  .sm\:text-lg { font-size: var(--text-lg); }
  .sm\:p-6 { padding: var(--space-md); }
}

@media (min-width: 768px) {
  .md\:text-xl { font-size: var(--text-xl); }
  .md\:p-8 { padding: var(--space-lg); }
}

@media (min-width: 1024px) {
  .lg\:text-2xl { font-size: var(--text-2xl); }
  .lg\:p-10 { padding: var(--space-xl); }
}

/* 动画系统 */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:translate-y-1:hover {
  transform: translateY(-4px);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 特殊效果 */
.backdrop-blur {
  backdrop-filter: blur(8px);
}

.glass-effect {
  background: rgba(30, 30, 63, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: light) {
  :root {
    --primary-bg: #ffffff;
    --secondary-bg: #f8fafc;
    --card-bg: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #475569;
  }
}

/* 无障碍访问 */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #ffffff;
    --text-muted: #ffffff;
  }
}