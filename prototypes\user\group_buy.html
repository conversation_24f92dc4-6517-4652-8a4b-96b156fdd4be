<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>团购专区 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
        }
        .group-card {
            background: rgba(30, 30, 63, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .group-card:hover {
            border-color: rgba(59, 130, 246, 0.6);
            transform: translateY(-2px);
        }
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
        }
        .countdown {
            background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
        }
    </style>
</head>
<body class="text-white">
    <!-- 顶部导航 -->
    <div class="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <button onclick="goBack()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6"></i>
            </button>
            <h1 class="text-lg font-semibold">团购专区</h1>
            <button onclick="window.location.href='publish_group_buy.html'" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="plus" class="w-6 h-6"></i>
            </button>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="p-4">
        <div class="flex space-x-3 mb-4">
            <div class="flex-1 relative">
                <input type="text" placeholder="搜索团购商品..." class="w-full bg-slate-700 border border-slate-600 rounded-lg pl-10 pr-4 py-3 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none">
                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400"></i>
            </div>
            <button onclick="showFilters()" class="bg-slate-700 hover:bg-slate-600 p-3 rounded-lg transition-colors">
                <i data-lucide="filter" class="w-5 h-5"></i>
            </button>
        </div>
        
        <!-- 分类标签 -->
        <div class="flex space-x-2 overflow-x-auto">
            <button onclick="filterCategory('all')" class="category-btn active bg-blue-600 text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">
                全部
            </button>
            <button onclick="filterCategory('food')" class="category-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                美食
            </button>
            <button onclick="filterCategory('fresh')" class="category-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                生鲜
            </button>
            <button onclick="filterCategory('daily')" class="category-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                日用品
            </button>
            <button onclick="filterCategory('electronics')" class="category-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                数码
            </button>
        </div>
    </div>

    <!-- 热门团购横幅 -->
    <div class="px-4 mb-6">
        <div class="bg-gradient-to-r from-orange-500 to-red-600 rounded-lg p-4 relative overflow-hidden">
            <div class="relative z-10">
                <h2 class="text-xl font-bold mb-2">🔥 限时秒杀</h2>
                <p class="text-sm opacity-90 mb-3">精选商品，超低价格，手慢无！</p>
                <div class="flex items-center space-x-4">
                    <div class="countdown px-3 py-1 rounded-full text-xs font-medium">
                        <span id="countdown-timer">02:45:30</span> 后结束
                    </div>
                    <button onclick="viewFlashSale()" class="bg-white text-red-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                        立即抢购
                    </button>
                </div>
            </div>
            <div class="absolute right-0 top-0 w-32 h-32 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
        </div>
    </div>

    <!-- 团购列表 -->
    <div class="p-4 space-y-4">
        <!-- 团购商品1 -->
        <div class="group-card rounded-lg p-4 transition-all duration-300" data-category="food">
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=120&h=120&fit=crop" alt="披萨" class="w-20 h-20 rounded-lg object-cover">
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-semibold text-white truncate">意式玛格丽特披萨</h3>
                        <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full ml-2">热销</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">正宗意式手工披萨，新鲜芝士配番茄酱</p>
                    
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-red-400">¥39</span>
                            <span class="text-sm text-slate-400 line-through">¥68</span>
                        </div>
                        <div class="text-sm text-slate-300">
                            已拼 <span class="text-blue-400 font-medium">156</span> 件
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="mb-3">
                        <div class="flex justify-between text-xs text-slate-400 mb-1">
                            <span>进度：156/200</span>
                            <span>78%</span>
                        </div>
                        <div class="w-full bg-slate-700 rounded-full h-2">
                            <div class="progress-bar h-2 rounded-full" style="width: 78%"></div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 text-sm text-slate-400">
                            <i data-lucide="clock" class="w-4 h-4"></i>
                            <span>剩余 <span class="text-orange-400">2天15小时</span></span>
                        </div>
                        <button onclick="joinGroup('pizza')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            立即参团
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 团购商品2 -->
        <div class="group-card rounded-lg p-4 transition-all duration-300" data-category="fresh">
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1610832958506-aa56368176cf?w=120&h=120&fit=crop" alt="苹果" class="w-20 h-20 rounded-lg object-cover">
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-semibold text-white truncate">新疆阿克苏苹果</h3>
                        <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full ml-2">新品</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">5斤装，脆甜多汁，产地直发</p>
                    
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-red-400">¥29.9</span>
                            <span class="text-sm text-slate-400 line-through">¥45</span>
                        </div>
                        <div class="text-sm text-slate-300">
                            已拼 <span class="text-blue-400 font-medium">89</span> 件
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="mb-3">
                        <div class="flex justify-between text-xs text-slate-400 mb-1">
                            <span>进度：89/150</span>
                            <span>59%</span>
                        </div>
                        <div class="w-full bg-slate-700 rounded-full h-2">
                            <div class="progress-bar h-2 rounded-full" style="width: 59%"></div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 text-sm text-slate-400">
                            <i data-lucide="clock" class="w-4 h-4"></i>
                            <span>剩余 <span class="text-orange-400">1天8小时</span></span>
                        </div>
                        <button onclick="joinGroup('apple')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            立即参团
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 团购商品3 -->
        <div class="group-card rounded-lg p-4 transition-all duration-300" data-category="daily">
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=120&h=120&fit=crop" alt="洗衣液" class="w-20 h-20 rounded-lg object-cover">
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-semibold text-white truncate">蓝月亮洗衣液套装</h3>
                        <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full ml-2">爆款</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">3kg*2瓶装，深层洁净，护色护形</p>
                    
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-red-400">¥59.9</span>
                            <span class="text-sm text-slate-400 line-through">¥89</span>
                        </div>
                        <div class="text-sm text-slate-300">
                            已拼 <span class="text-blue-400 font-medium">234</span> 件
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="mb-3">
                        <div class="flex justify-between text-xs text-slate-400 mb-1">
                            <span>进度：234/300</span>
                            <span>78%</span>
                        </div>
                        <div class="w-full bg-slate-700 rounded-full h-2">
                            <div class="progress-bar h-2 rounded-full" style="width: 78%"></div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 text-sm text-slate-400">
                            <i data-lucide="clock" class="w-4 h-4"></i>
                            <span>剩余 <span class="text-orange-400">3天12小时</span></span>
                        </div>
                        <button onclick="joinGroup('detergent')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            立即参团
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 团购商品4 -->
        <div class="group-card rounded-lg p-4 transition-all duration-300" data-category="electronics">
            <div class="flex space-x-4">
                <img src="https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=120&h=120&fit=crop" alt="耳机" class="w-20 h-20 rounded-lg object-cover">
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-semibold text-white truncate">无线蓝牙耳机</h3>
                        <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full ml-2">限量</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">降噪版，续航30小时，快充技术</p>
                    
                    <div class="flex items-center space-x-4 mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-red-400">¥199</span>
                            <span class="text-sm text-slate-400 line-through">¥299</span>
                        </div>
                        <div class="text-sm text-slate-300">
                            已拼 <span class="text-blue-400 font-medium">67</span> 件
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="mb-3">
                        <div class="flex justify-between text-xs text-slate-400 mb-1">
                            <span>进度：67/100</span>
                            <span>67%</span>
                        </div>
                        <div class="w-full bg-slate-700 rounded-full h-2">
                            <div class="progress-bar h-2 rounded-full" style="width: 67%"></div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 text-sm text-slate-400">
                            <i data-lucide="clock" class="w-4 h-4"></i>
                            <span>剩余 <span class="text-orange-400">5天6小时</span></span>
                        </div>
                        <button onclick="joinGroup('earphones')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            立即参团
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部提示 -->
    <div class="p-4 text-center text-sm text-slate-400">
        <p>更多精彩团购，敬请期待...</p>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 倒计时功能
        function updateCountdown() {
            const timer = document.getElementById('countdown-timer');
            if (timer) {
                const now = new Date().getTime();
                const end = now + (2 * 60 * 60 + 45 * 60 + 30) * 1000; // 2小时45分30秒后
                
                setInterval(() => {
                    const now = new Date().getTime();
                    const distance = end - now;
                    
                    if (distance > 0) {
                        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                        
                        timer.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    } else {
                        timer.textContent = '已结束';
                    }
                }, 1000);
            }
        }

        function filterCategory(category) {
            const cards = document.querySelectorAll('.group-card');
            const buttons = document.querySelectorAll('.category-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            event.target.classList.add('active', 'bg-blue-600', 'text-white');
            event.target.classList.remove('bg-slate-700', 'text-slate-300');
            
            // 筛选卡片
            cards.forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function joinGroup(productId) {
            alert(`正在加入团购：${productId}\n\n请稍候...`);
        }

        function viewFlashSale() {
            alert('正在跳转到限时秒杀页面...');
        }

        function showFilters() {
            alert('筛选功能开发中...');
        }

        function goBack() {
            window.history.back();
        }

        // 页面加载时启动倒计时
        updateCountdown();
    </script>
</body>
</html>