<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容审核 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
        }
        .content-card {
            transition: all 0.2s ease;
        }
        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech">
    <!-- 顶部导航栏 -->
    <nav class="bg-dark-card border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 tech-gradient rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">审</span>
                </div>
                <h1 class="text-xl font-bold">内容审核</h1>
                <span class="px-3 py-1 bg-yellow-500 bg-opacity-20 text-yellow-400 text-sm rounded-full">47 待审核</span>
            </div>
            <div class="flex items-center space-x-4">
                <button class="px-4 py-2 bg-tech-blue rounded-lg text-white text-sm hover:bg-opacity-80 transition-colors">
                    批量操作
                </button>
                <div class="w-8 h-8 bg-tech-purple rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">管</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="p-6">
        <!-- 筛选区域 -->
        <div class="bg-dark-card rounded-xl p-6 mb-6 border border-gray-700">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- 内容类型 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">内容类型</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="">全部类型</option>
                        <option value="shop">商铺信息</option>
                        <option value="life">生活信息</option>
                        <option value="transport">出行服务</option>
                        <option value="errand">跑腿服务</option>
                    </select>
                </div>

                <!-- 审核状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">审核状态</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="pending">待审核</option>
                        <option value="approved">已通过</option>
                        <option value="rejected">已拒绝</option>
                        <option value="all">全部状态</option>
                    </select>
                </div>

                <!-- 提交时间 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">提交时间</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="all">全部时间</option>
                    </select>
                </div>

                <!-- 优先级 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">优先级</label>
                    <select class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        <option value="">全部优先级</option>
                        <option value="high">高优先级</option>
                        <option value="normal">普通</option>
                        <option value="low">低优先级</option>
                    </select>
                </div>

                <!-- 搜索 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">搜索内容</label>
                    <div class="relative">
                        <input type="text" placeholder="搜索标题或内容" 
                               class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-tech-purple">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">待审核</h3>
                    <div class="w-8 h-8 bg-yellow-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">47</p>
            </div>

            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">今日已审</h3>
                    <div class="w-8 h-8 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">156</p>
            </div>

            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">通过率</h3>
                    <div class="w-8 h-8 bg-tech-blue bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-tech-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">87.5%</p>
            </div>

            <div class="bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-400">平均用时</h3>
                    <div class="w-8 h-8 bg-tech-cyan bg-opacity-20 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-tech-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-2xl font-bold text-white">2.3分</p>
            </div>
        </div>

        <!-- 待审核内容列表 -->
        <div class="space-y-4">
            <!-- 内容项1 -->
            <div class="content-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-tech-blue bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-tech-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-white">张记小面馆 - 正宗重庆小面</h3>
                                <span class="px-2 py-1 bg-tech-blue bg-opacity-20 text-tech-blue text-xs rounded-full">商铺</span>
                                <span class="px-2 py-1 bg-yellow-500 bg-opacity-20 text-yellow-400 text-xs rounded-full">待审核</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">地址：市中心步行街123号 | 联系电话：138****5678</p>
                            <p class="text-gray-400 text-sm leading-relaxed">正宗重庆小面，传承老工艺，选用优质面条和秘制调料。营业时间：早上7点到晚上9点，欢迎品尝。店内环境干净整洁，价格实惠，是您用餐的好选择...</p>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-400">
                        <p>提交时间</p>
                        <p>2024-01-15 14:30</p>
                        <p class="mt-2">提交用户</p>
                        <p class="text-tech-cyan">张三 (ID: 10001)</p>
                    </div>
                </div>
                
                <!-- 图片预览 -->
                <div class="mb-4">
                    <p class="text-sm text-gray-400 mb-2">上传图片 (3张)</p>
                    <div class="flex space-x-3">
                        <div class="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center">
                            <span class="text-gray-400 text-xs">店面</span>
                        </div>
                        <div class="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center">
                            <span class="text-gray-400 text-xs">菜品</span>
                        </div>
                        <div class="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center">
                            <span class="text-gray-400 text-xs">环境</span>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-green-500 bg-opacity-20 border border-green-500 text-green-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            通过审核
                        </button>
                        <button class="px-4 py-2 bg-red-500 bg-opacity-20 border border-red-500 text-red-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            拒绝审核
                        </button>
                        <button class="px-4 py-2 bg-tech-blue bg-opacity-20 border border-tech-blue text-tech-blue rounded-lg hover:bg-opacity-30 transition-colors">
                            查看详情
                        </button>
                    </div>
                    <div class="text-sm text-gray-400">
                        优先级: <span class="text-yellow-400">普通</span>
                    </div>
                </div>
            </div>

            <!-- 内容项2 -->
            <div class="content-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-tech-purple bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-tech-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-white">急招服务员 - 月薪4000-6000</h3>
                                <span class="px-2 py-1 bg-tech-purple bg-opacity-20 text-tech-purple text-xs rounded-full">生活</span>
                                <span class="px-2 py-1 bg-yellow-500 bg-opacity-20 text-yellow-400 text-xs rounded-full">待审核</span>
                                <span class="px-2 py-1 bg-red-500 bg-opacity-20 text-red-400 text-xs rounded-full">高优先级</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">工作地点：市中心商业区 | 联系方式：李经理 139****1234</p>
                            <p class="text-gray-400 text-sm leading-relaxed">招聘餐厅服务员，要求：18-35岁，有服务行业经验优先，工作认真负责，沟通能力强。待遇：底薪3000+提成，包吃住，月休4天，有社保...</p>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-400">
                        <p>提交时间</p>
                        <p>2024-01-15 13:45</p>
                        <p class="mt-2">提交用户</p>
                        <p class="text-tech-cyan">李四 (ID: 10002)</p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-green-500 bg-opacity-20 border border-green-500 text-green-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            通过审核
                        </button>
                        <button class="px-4 py-2 bg-red-500 bg-opacity-20 border border-red-500 text-red-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            拒绝审核
                        </button>
                        <button class="px-4 py-2 bg-tech-blue bg-opacity-20 border border-tech-blue text-tech-blue rounded-lg hover:bg-opacity-30 transition-colors">
                            查看详情
                        </button>
                    </div>
                    <div class="text-sm text-gray-400">
                        优先级: <span class="text-red-400">高</span>
                    </div>
                </div>
            </div>

            <!-- 内容项3 -->
            <div class="content-card bg-dark-card rounded-xl p-6 border border-gray-700">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-tech-cyan bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-tech-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-white">明天上午拼车去机场</h3>
                                <span class="px-2 py-1 bg-tech-cyan bg-opacity-20 text-tech-cyan text-xs rounded-full">出行</span>
                                <span class="px-2 py-1 bg-yellow-500 bg-opacity-20 text-yellow-400 text-xs rounded-full">待审核</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">出发时间：明天8:00 | 联系方式：王先生 137****9876</p>
                            <p class="text-gray-400 text-sm leading-relaxed">明天早上8点从市中心出发去机场，还有2个空位，费用AA制，大概每人50元。有需要的朋友请联系，要求准时，不要迟到...</p>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-400">
                        <p>提交时间</p>
                        <p>2024-01-15 16:20</p>
                        <p class="mt-2">提交用户</p>
                        <p class="text-tech-cyan">王五 (ID: 10003)</p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-green-500 bg-opacity-20 border border-green-500 text-green-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            通过审核
                        </button>
                        <button class="px-4 py-2 bg-red-500 bg-opacity-20 border border-red-500 text-red-400 rounded-lg hover:bg-opacity-30 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            拒绝审核
                        </button>
                        <button class="px-4 py-2 bg-tech-blue bg-opacity-20 border border-tech-blue text-tech-blue rounded-lg hover:bg-opacity-30 transition-colors">
                            查看详情
                        </button>
                    </div>
                    <div class="text-sm text-gray-400">
                        优先级: <span class="text-yellow-400">普通</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-400">
                显示 1-3 条，共 47 条待审核内容
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">上一页</button>
                <button class="px-3 py-1 bg-tech-blue text-white text-sm rounded">1</button>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">2</button>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">3</button>
                <span class="text-gray-400">...</span>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">16</button>
                <button class="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded hover:bg-gray-600 transition-colors">下一页</button>
            </div>
        </div>
    </div>

    <script>
        // 审核操作
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('通过审核')) {
                button.addEventListener('click', function() {
                    if (confirm('确认通过此内容的审核？')) {
                        alert('审核通过 - 实际开发中将更新数据库状态');
                        // 移除当前卡片或更新状态
                        this.closest('.content-card').style.opacity = '0.5';
                    }
                });
            } else if (button.textContent.includes('拒绝审核')) {
                button.addEventListener('click', function() {
                    const reason = prompt('请输入拒绝理由：');
                    if (reason) {
                        alert(`审核拒绝，理由：${reason} - 实际开发中将更新数据库状态`);
                        this.closest('.content-card').style.opacity = '0.5';
                    }
                });
            } else if (button.textContent.includes('查看详情')) {
                button.addEventListener('click', function() {
                    alert('打开详情页面 - 实际开发中将显示完整内容信息');
                });
            }
        });

        // 批量操作
        document.querySelector('nav button').addEventListener('click', function() {
            alert('批量操作功能 - 实际开发中将提供批量审核选项');
        });
    </script>
</body>
</html>