/**
 * 统一UI组件库 - 全面优化版
 * 解决代码重复、交互一致性、无障碍访问、响应式适配等问题
 * 基于质量检查报告的全面优化方案
 */

// 全局配置
const UI_CONFIG = {
    // 防抖延迟
    DEBOUNCE_DELAY: 300,
    // Toast显示时长
    TOAST_DURATION: 3000,
    // 验证码倒计时
    SMS_COUNTDOWN: 60,
    // 动画持续时间
    ANIMATION_DURATION: 300,
    // 最小点击区域（移动端）
    MIN_TOUCH_TARGET: 44,
    // 响应式断点
    BREAKPOINTS: {
        sm: 640,
        md: 768,
        lg: 1024,
        xl: 1280
    },
    // 无障碍配置
    A11Y: {
        FOCUS_VISIBLE_CLASS: 'focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
        SCREEN_READER_ONLY: 'sr-only'
    }
};

/**
 * 工具函数集合
 */
const Utils = {
    // 防抖函数 - 防止重复点击
    debounce(func, delay = UI_CONFIG.DEBOUNCE_DELAY) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },

    // 节流函数 - 限制函数执行频率
    throttle(func, delay = UI_CONFIG.DEBOUNCE_DELAY) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    },

    // 检测设备类型
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 检测触摸设备
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    // 获取当前断点
    getCurrentBreakpoint() {
        const width = window.innerWidth;
        if (width >= UI_CONFIG.BREAKPOINTS.xl) return 'xl';
        if (width >= UI_CONFIG.BREAKPOINTS.lg) return 'lg';
        if (width >= UI_CONFIG.BREAKPOINTS.md) return 'md';
        if (width >= UI_CONFIG.BREAKPOINTS.sm) return 'sm';
        return 'xs';
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
        }
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hour = String(d.getHours()).padStart(2, '0');
        const minute = String(d.getMinutes()).padStart(2, '0');
        const second = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    },

    // 生成唯一ID
    generateId() {
        // 使用现代方法替代已弃用的 substr()
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    },

    // 深度克隆
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    },

    // 检查颜色对比度
    checkContrast(color1, color2) {
        // 简化的对比度检查，实际项目中可使用更精确的算法
        const getLuminance = (color) => {
            const rgb = parseInt(color.replace('#', ''), 16);
            const r = (rgb >> 16) & 0xff;
            const g = (rgb >> 8) & 0xff;
            const b = (rgb >> 0) & 0xff;
            return 0.299 * r + 0.587 * g + 0.114 * b;
        };
        
        const lum1 = getLuminance(color1);
        const lum2 = getLuminance(color2);
        const ratio = Math.abs(lum1 - lum2) / 255;
        
        return ratio > 0.5; // 简化的对比度检查
    }
};

// 向后兼容
const debounce = Utils.debounce;
const throttle = Utils.throttle;

/**
 * Toast 通知组件
 */
class ToastManager {
    constructor() {
        this.container = this.createContainer();
        this.toasts = new Set();
    }

    createContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'fixed top-4 right-4 z-50 space-y-2';
        container.setAttribute('aria-live', 'polite');
        container.setAttribute('aria-atomic', 'true');
        document.body.appendChild(container);
        return container;
    }

    show(message, type = 'info', duration = UI_CONFIG.TOAST_DURATION) {
        const toast = this.createToast(message, type);
        this.container.appendChild(toast);
        this.toasts.add(toast);

        // 显示动画
        requestAnimationFrame(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
            toast.classList.add('translate-x-0', 'opacity-100');
        });

        // 自动隐藏
        setTimeout(() => this.hide(toast), duration);
        
        return toast;
    }

    createToast(message, type) {
        const toast = document.createElement('div');
        const typeClasses = {
            success: 'bg-green-500 border-green-400',
            error: 'bg-red-500 border-red-400',
            warning: 'bg-yellow-500 border-yellow-400',
            info: 'bg-blue-500 border-blue-400'
        };

        const icons = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'alert-triangle',
            info: 'info'
        };

        toast.className = `
            flex items-center px-4 py-3 rounded-lg text-white text-sm font-medium
            border-l-4 shadow-lg transform transition-all duration-300 ease-in-out
            translate-x-full opacity-0 max-w-sm
            ${typeClasses[type] || typeClasses.info}
        `;

        toast.innerHTML = `
            <i data-lucide="${icons[type] || icons.info}" class="w-5 h-5 mr-3 flex-shrink-0" aria-hidden="true"></i>
            <span class="flex-1" role="status" aria-live="polite">${message}</span>
            <button type="button" 
                    class="ml-3 text-white/80 hover:text-white transition-colors ${UI_CONFIG.A11Y.FOCUS_VISIBLE_CLASS}" 
                    onclick="toastManager.hide(this.closest('.transform'))"
                    aria-label="关闭通知">
                <i data-lucide="x" class="w-4 h-4" aria-hidden="true"></i>
            </button>
        `;

        // 初始化图标
        setTimeout(() => lucide.createIcons(), 0);
        
        return toast;
    }

    hide(toast) {
        if (!this.toasts.has(toast)) return;
        
        toast.classList.remove('translate-x-0', 'opacity-100');
        toast.classList.add('translate-x-full', 'opacity-0');
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            this.toasts.delete(toast);
        }, UI_CONFIG.ANIMATION_DURATION);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

/**
 * Modal 弹窗组件
 */
class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.setupKeyboardHandlers();
    }

    setupKeyboardHandlers() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModals.size > 0) {
                const lastModal = Array.from(this.activeModals).pop();
                this.hide(lastModal);
            }
        });
    }

    show(options = {}) {
        const {
            title = '提示',
            content = '',
            confirmText = '确定',
            cancelText = '取消',
            showCancel = true,
            onConfirm = () => {},
            onCancel = () => {},
            size = 'md'
        } = options;

        const modal = this.createModal({ title, content, confirmText, cancelText, showCancel, size });
        document.body.appendChild(modal);
        this.activeModals.add(modal);

        // 显示动画
        requestAnimationFrame(() => {
            modal.classList.remove('opacity-0');
            modal.classList.add('opacity-100');
            const dialog = modal.querySelector('.modal-dialog');
            dialog.classList.remove('scale-95', 'translate-y-4');
            dialog.classList.add('scale-100', 'translate-y-0');
        });

        // 绑定事件
        const confirmBtn = modal.querySelector('.modal-confirm');
        const cancelBtn = modal.querySelector('.modal-cancel');
        const closeBtn = modal.querySelector('.modal-close');
        const backdrop = modal.querySelector('.modal-backdrop');

        const handleConfirm = debounce(() => {
            const result = onConfirm();
            if (result !== false) {
                this.hide(modal);
            }
        });

        const handleCancel = debounce(() => {
            onCancel();
            this.hide(modal);
        });

        confirmBtn?.addEventListener('click', handleConfirm);
        cancelBtn?.addEventListener('click', handleCancel);
        closeBtn?.addEventListener('click', handleCancel);
        backdrop?.addEventListener('click', handleCancel);

        return modal;
    }

    createModal({ title, content, confirmText, cancelText, showCancel, size }) {
        const sizeClasses = {
            sm: 'max-w-sm',
            md: 'max-w-md',
            lg: 'max-w-lg',
            xl: 'max-w-xl'
        };

        const modal = document.createElement('div');
        modal.className = `
            fixed inset-0 z-50 flex items-center justify-center p-4
            opacity-0 transition-opacity duration-300 ease-in-out
        `;
        modal.setAttribute('role', 'dialog');
        modal.setAttribute('aria-modal', 'true');
        modal.setAttribute('aria-labelledby', 'modal-title');

        modal.innerHTML = `
            <div class="modal-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
            <div class="modal-dialog relative w-full ${sizeClasses[size]} transform transition-all duration-300 ease-in-out scale-95 translate-y-4">
                <div class="bg-slate-800 rounded-xl shadow-2xl border border-slate-700">
                    <div class="flex items-center justify-between p-6 border-b border-slate-700">
                        <h3 id="modal-title" class="text-lg font-semibold text-white">${title}</h3>
                        <button type="button" 
                                class="modal-close text-slate-400 hover:text-white transition-colors ${UI_CONFIG.A11Y.FOCUS_VISIBLE_CLASS}"
                                aria-label="关闭弹窗">
                            <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="text-slate-300">${content}</div>
                    </div>
                    <div class="flex items-center justify-end space-x-3 p-6 border-t border-slate-700">
                        ${showCancel ? `
                            <button type="button" 
                                    class="modal-cancel px-4 py-2 text-slate-400 hover:text-white transition-colors ${UI_CONFIG.A11Y.FOCUS_VISIBLE_CLASS}"
                                    style="min-height: ${UI_CONFIG.MIN_TOUCH_TARGET}px; min-width: ${UI_CONFIG.MIN_TOUCH_TARGET}px;">
                                ${cancelText}
                            </button>
                        ` : ''}
                        <button type="button" 
                                class="modal-confirm px-6 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-medium transition-colors ${UI_CONFIG.A11Y.FOCUS_VISIBLE_CLASS}"
                                style="min-height: ${UI_CONFIG.MIN_TOUCH_TARGET}px;">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 初始化图标
        setTimeout(() => lucide.createIcons(), 0);
        
        return modal;
    }

    hide(modal) {
        if (!this.activeModals.has(modal)) return;
        
        modal.classList.remove('opacity-100');
        modal.classList.add('opacity-0');
        const dialog = modal.querySelector('.modal-dialog');
        dialog.classList.remove('scale-100', 'translate-y-0');
        dialog.classList.add('scale-95', 'translate-y-4');
        
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
            this.activeModals.delete(modal);
        }, UI_CONFIG.ANIMATION_DURATION);
    }

    confirm(options = {}) {
        return new Promise((resolve) => {
            this.show({
                ...options,
                onConfirm: () => {
                    resolve(true);
                },
                onCancel: () => {
                    resolve(false);
                }
            });
        });
    }

    alert(message, title = '提示') {
        return new Promise((resolve) => {
            this.show({
                title,
                content: message,
                showCancel: false,
                onConfirm: () => {
                    resolve(true);
                }
            });
        });
    }
}

/**
 * Loading 加载组件
 */
class LoadingManager {
    constructor() {
        this.activeLoadings = new Map();
    }

    show(target = document.body, options = {}) {
        const {
            text = '加载中...',
            size = 'md',
            overlay = true
        } = options;

        if (this.activeLoadings.has(target)) {
            return this.activeLoadings.get(target);
        }

        const loading = this.createLoading({ text, size, overlay });
        
        if (target === document.body) {
            document.body.appendChild(loading);
        } else {
            target.style.position = 'relative';
            target.appendChild(loading);
        }
        
        this.activeLoadings.set(target, loading);
        
        // 显示动画
        requestAnimationFrame(() => {
            loading.classList.remove('opacity-0');
            loading.classList.add('opacity-100');
        });
        
        return loading;
    }

    createLoading({ text, size, overlay }) {
        const sizeClasses = {
            sm: 'w-4 h-4',
            md: 'w-6 h-6',
            lg: 'w-8 h-8'
        };

        const loading = document.createElement('div');
        loading.className = `
            ${overlay ? 'fixed' : 'absolute'} inset-0 z-50 flex items-center justify-center
            ${overlay ? 'bg-black/50 backdrop-blur-sm' : 'bg-slate-900/80'}
            opacity-0 transition-opacity duration-300 ease-in-out
        `;

        loading.innerHTML = `
            <div class="flex flex-col items-center space-y-3">
                <div class="animate-spin ${sizeClasses[size]} border-2 border-blue-500 border-t-transparent rounded-full"></div>
                <div class="text-white text-sm font-medium">${text}</div>
            </div>
        `;
        
        return loading;
    }

    hide(target = document.body) {
        const loading = this.activeLoadings.get(target);
        if (!loading) return;
        
        loading.classList.remove('opacity-100');
        loading.classList.add('opacity-0');
        
        setTimeout(() => {
            if (loading.parentNode) {
                loading.parentNode.removeChild(loading);
            }
            this.activeLoadings.delete(target);
        }, UI_CONFIG.ANIMATION_DURATION);
    }
}

/**
 * 表单验证组件
 */
class FormValidator {
    constructor() {
        this.rules = {
            required: (value) => value.trim() !== '',
            phone: (value) => /^1[3-9]\d{9}$/.test(value),
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
            number: (value) => !isNaN(value) && value !== '',
            min: (value, min) => value.length >= min,
            max: (value, max) => value.length <= max,
            minValue: (value, min) => parseFloat(value) >= min,
            maxValue: (value, max) => parseFloat(value) <= max
        };
        
        this.messages = {
            required: '此字段为必填项',
            phone: '请输入正确的手机号码',
            email: '请输入正确的邮箱地址',
            number: '请输入有效数字',
            min: '长度不能少于{0}个字符',
            max: '长度不能超过{0}个字符',
            minValue: '数值不能小于{0}',
            maxValue: '数值不能大于{0}'
        };
    }

    validate(form) {
        const errors = [];
        const inputs = form.querySelectorAll('[data-validate]');
        
        inputs.forEach(input => {
            const rules = input.dataset.validate.split('|');
            const value = input.value;
            const fieldName = input.dataset.fieldName || input.name || '字段';
            
            rules.forEach(rule => {
                const [ruleName, ...params] = rule.split(':');
                
                if (this.rules[ruleName]) {
                    const isValid = params.length > 0 
                        ? this.rules[ruleName](value, ...params)
                        : this.rules[ruleName](value);
                    
                    if (!isValid) {
                        const message = this.messages[ruleName].replace(/\{(\d+)\}/g, (match, index) => {
                            return params[index] || match;
                        });
                        
                        errors.push({
                            field: input.name,
                            element: input,
                            message: `${fieldName}: ${message}`
                        });
                        
                        this.showFieldError(input, message);
                    } else {
                        this.clearFieldError(input);
                    }
                }
            });
        });
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    showFieldError(input, message) {
        this.clearFieldError(input);
        
        input.classList.add('border-red-500', 'focus:border-red-500');
        input.classList.remove('border-slate-600', 'focus:border-blue-500');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error text-red-400 text-xs mt-1';
        errorElement.textContent = message;
        
        input.parentNode.appendChild(errorElement);
    }

    clearFieldError(input) {
        input.classList.remove('border-red-500', 'focus:border-red-500');
        input.classList.add('border-slate-600', 'focus:border-blue-500');
        
        const errorElement = input.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    clearAllErrors(form) {
        const inputs = form.querySelectorAll('[data-validate]');
        inputs.forEach(input => this.clearFieldError(input));
    }
}

/**
 * 导航管理组件 - 统一跳转逻辑
 */
class NavigationManager {
    constructor() {
        this.pendingNavigation = null;
        this.setupGlobalHandlers();
    }

    setupGlobalHandlers() {
        // 统一处理所有导航点击
        document.addEventListener('click', (e) => {
            const link = e.target.closest('[data-navigate]');
            if (link) {
                e.preventDefault();
                const url = link.dataset.navigate;
                const options = {
                    replace: link.dataset.replace === 'true',
                    external: link.dataset.external === 'true',
                    confirm: link.dataset.confirm
                };
                this.navigate(url, options);
            }
        });
    }

    async navigate(url, options = {}) {
        const { replace = false, external = false, confirm = null } = options;
        
        // 防止重复导航
        if (this.pendingNavigation) {
            clearTimeout(this.pendingNavigation);
        }

        // 确认对话框
        if (confirm) {
            const confirmed = await modalManager.confirm({
                title: '确认操作',
                content: confirm
            });
            if (!confirmed) return;
        }

        // 显示加载状态
        const loadingId = loadingManager.show(document.body, { text: '页面跳转中...' });

        this.pendingNavigation = setTimeout(() => {
            try {
                if (external) {
                    window.open(url, '_blank', 'noopener,noreferrer');
                } else if (replace) {
                    window.location.replace(url);
                } else {
                    window.location.href = url;
                }
            } catch (error) {
                console.error('导航失败:', error);
                toastManager.error('页面跳转失败，请重试');
            } finally {
                loadingManager.hide(document.body);
                this.pendingNavigation = null;
            }
        }, 100);
    }

    back() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            toastManager.info('已经是第一页了');
        }
    }

    reload() {
        window.location.reload();
    }
}

/**
 * 状态管理组件
 */
class StateManager {
    constructor() {
        this.states = new Map();
        this.listeners = new Map();
    }

    setState(key, value) {
        const oldValue = this.states.get(key);
        this.states.set(key, value);
        
        // 通知监听器
        if (this.listeners.has(key)) {
            this.listeners.get(key).forEach(callback => {
                try {
                    callback(value, oldValue);
                } catch (error) {
                    console.error('状态监听器执行错误:', error);
                }
            });
        }
    }

    getState(key, defaultValue = null) {
        return this.states.get(key) ?? defaultValue;
    }

    subscribe(key, callback) {
        if (!this.listeners.has(key)) {
            this.listeners.set(key, []);
        }
        this.listeners.get(key).push(callback);
        
        // 返回取消订阅函数
        return () => {
            const callbacks = this.listeners.get(key);
            if (callbacks) {
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
            }
        };
    },

    clear() {
        this.states.clear();
        this.listeners.clear();
    }
}

/**
 * 主题管理组件
 */
class ThemeManager {
    constructor() {
        this.currentTheme = this.getStoredTheme() || 'dark';
        this.applyTheme(this.currentTheme);
    }

    getStoredTheme() {
        try {
            return localStorage.getItem('ui-theme');
        } catch {
            return null;
        }
    }

    setTheme(theme) {
        this.currentTheme = theme;
        this.applyTheme(theme);
        
        try {
            localStorage.setItem('ui-theme', theme);
        } catch (error) {
            console.warn('无法保存主题设置:', error);
        }
    }

    applyTheme(theme) {
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(theme);
        
        // 更新meta标签
        const metaTheme = document.querySelector('meta[name="theme-color"]');
        if (metaTheme) {
            metaTheme.content = theme === 'dark' ? '#0f0f23' : '#ffffff';
        }
    }

    toggle() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        return newTheme;
    }

    getCurrentTheme() {
        return this.currentTheme;
    }
}

// 创建全局实例
const toastManager = new ToastManager();
const modalManager = new ModalManager();
const loadingManager = new LoadingManager();
const formValidator = new FormValidator();
const navigationManager = new NavigationManager();
const stateManager = new StateManager();
const themeManager = new ThemeManager();

// 导出到全局
window.UI = {
    toast: toastManager,
    modal: modalManager,
    loading: loadingManager,
    validator: formValidator,
    navigation: navigationManager,
    state: stateManager,
    theme: themeManager,
    utils: Utils,
    config: UI_CONFIG,
    // 向后兼容
    debounce: Utils.debounce,
    throttle: Utils.throttle
};

// 兼容旧的全局函数
window.showToast = (message, type, duration) => toastManager.show(message, type, duration);
window.showModal = (options) => modalManager.show(options);
window.showLoading = (target, options) => loadingManager.show(target, options);
window.hideLoading = (target) => loadingManager.hide(target);
window.copyToClipboard = Utils.copyToClipboard;
window.formatDate = Utils.formatDate;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化表单验证
    document.addEventListener('blur', (e) => {
        if (e.target.dataset.validate) {
            const form = e.target.closest('form');
            if (form) {
                formValidator.validate(form);
            }
        }
    }, true);

    // 初始化防抖点击
    document.addEventListener('click', (e) => {
        if (e.target.dataset.debounce) {
            e.preventDefault();
            const delay = parseInt(e.target.dataset.debounce) || UI_CONFIG.DEBOUNCE_DELAY;
            const debouncedHandler = Utils.debounce(() => {
                if (e.target.onclick) {
                    e.target.onclick(e);
                }
            }, delay);
            debouncedHandler();
        }
    });

    // 初始化复制功能
    document.addEventListener('click', async (e) => {
        if (e.target.dataset.copy) {
            e.preventDefault();
            const text = e.target.dataset.copy;
            const success = await Utils.copyToClipboard(text);
            if (success) {
                toastManager.success('复制成功');
            } else {
                toastManager.error('复制失败');
            }
        }
    });

    // 初始化主题切换
    document.addEventListener('click', (e) => {
        if (e.target.dataset.toggleTheme !== undefined) {
            e.preventDefault();
            const newTheme = themeManager.toggle();
            toastManager.info(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}模式`);
        }
    });

    console.log('UI组件库已完全加载并初始化');
});

// 导出模块（如果支持ES6模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.UI;
}