<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .status-active { color: #10b981; }
        .status-inactive { color: #ef4444; }
        .status-warning { color: #f59e0b; }
        .status-banned { color: #dc2626; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 顶部导航 -->
    <nav class="gradient-bg p-4 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i data-lucide="layout-dashboard" class="w-8 h-8"></i>
                <h1 class="text-xl font-bold">本地助手管理后台</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm">管理员</span>
                <i data-lucide="user" class="w-6 h-6"></i>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold mb-2">用户管理系统</h2>
            <p class="text-gray-400">管理平台所有用户信息、状态和违规处理</p>
        </div>

        <!-- 用户统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">总用户数</p>
                        <p class="text-3xl font-bold">8,456</p>
                        <p class="text-green-400 text-sm mt-1">↗ +156 本月</p>
                    </div>
                    <div class="bg-blue-600 p-3 rounded-lg">
                        <i data-lucide="users" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">活跃用户</p>
                        <p class="text-3xl font-bold text-green-400">6,123</p>
                        <p class="text-green-400 text-sm mt-1">72.4% 活跃率</p>
                    </div>
                    <div class="bg-green-600 p-3 rounded-lg">
                        <i data-lucide="user-check" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">违规用户</p>
                        <p class="text-3xl font-bold text-yellow-400">45</p>
                        <p class="text-red-400 text-sm mt-1">需要处理</p>
                    </div>
                    <div class="bg-yellow-600 p-3 rounded-lg">
                        <i data-lucide="triangle-alert" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">封禁用户</p>
                        <p class="text-3xl font-bold text-red-400">12</p>
                        <p class="text-gray-400 text-sm mt-1">永久/临时</p>
                    </div>
                    <div class="bg-red-600 p-3 rounded-lg">
                        <i data-lucide="user-minus" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索用户（手机号、昵称、ID）" 
                               class="pl-10 pr-4 py-2 bg-gray-700 rounded-lg text-white placeholder-gray-400 w-full md:w-64">
                        <i data-lucide="search" class="w-5 h-5 absolute left-3 top-2.5 text-gray-400"></i>
                    </div>
                    
                    <select id="statusFilter" class="px-4 py-2 bg-gray-700 rounded-lg text-white">
                        <option value="">全部状态</option>
                        <option value="active">正常</option>
                        <option value="warning">警告</option>
                        <option value="banned">封禁</option>
                        <option value="inactive">不活跃</option>
                    </select>
                    
                    <select id="typeFilter" class="px-4 py-2 bg-gray-700 rounded-lg text-white">
                        <option value="">全部类型</option>
                        <option value="normal">普通用户</option>
                        <option value="merchant">商家用户</option>
                        <option value="runner">跑腿员</option>
                        <option value="driver">司机</option>
                    </select>
                </div>
                
                <div class="flex space-x-2">
                    <button onclick="searchUsers()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        搜索
                    </button>
                    <button onclick="exportUsers()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="bg-gray-800 rounded-lg overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <h3 class="text-lg font-semibold">用户列表</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                用户信息
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                用户类型
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                注册时间
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                最后活跃
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                状态
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                违规次数
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody" class="bg-gray-800 divide-y divide-gray-700">
                        <!-- 用户数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="px-6 py-3 bg-gray-700 border-t border-gray-600">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-400">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">20</span> 条，共 <span class="font-medium">12,456</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm transition-colors">
                            上一页
                        </button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm transition-colors">2</button>
                        <button class="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm transition-colors">3</button>
                        <button class="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm transition-colors">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div id="userDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">用户详情</h3>
                        <button onclick="closeUserDetail()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6" id="userDetailContent">
                    <!-- 用户详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 违规处理模态框 -->
    <div id="violationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-md w-full">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">违规处理</h3>
                        <button onclick="closeViolationModal()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">处理类型</label>
                            <select id="violationType" class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                                <option value="warning">警告</option>
                                <option value="temp_ban">临时封禁</option>
                                <option value="permanent_ban">永久封禁</option>
                                <option value="content_delete">删除违规内容</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">封禁时长（天）</label>
                            <input type="number" id="banDuration" placeholder="仅临时封禁需要" 
                                   class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">处理原因</label>
                            <textarea id="violationReason" rows="3" placeholder="请输入处理原因..." 
                                      class="w-full px-3 py-2 bg-gray-700 rounded-lg"></textarea>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3 mt-6">
                        <button onclick="submitViolation()" class="flex-1 bg-red-600 hover:bg-red-700 py-2 rounded-lg transition-colors">
                            确认处理
                        </button>
                        <button onclick="closeViolationModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 rounded-lg transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 模拟用户数据
        const usersData = [
            {
                id: 'U001',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
                nickname: '张三',
                phone: '138****5678',
                type: 'normal',
                registerTime: '2024-01-15',
                lastActive: '2024-01-20 14:30',
                status: 'active',
                violations: 0,
                publishCount: 23,
                orderCount: 45
            },
            {
                id: 'U002',
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
                nickname: '李四',
                phone: '139****1234',
                type: 'merchant',
                registerTime: '2024-01-10',
                lastActive: '2024-01-19 09:15',
                status: 'active',
                violations: 1,
                publishCount: 156,
                orderCount: 234
            },
            {
                id: 'U003',
                avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
                nickname: '王五',
                phone: '137****9876',
                type: 'runner',
                registerTime: '2024-01-08',
                lastActive: '2024-01-18 16:45',
                status: 'warning',
                violations: 2,
                publishCount: 12,
                orderCount: 89
            },
            {
                id: 'U004',
                avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
                nickname: '赵六',
                phone: '136****5432',
                type: 'driver',
                registerTime: '2024-01-05',
                lastActive: '2024-01-15 11:20',
                status: 'banned',
                violations: 5,
                publishCount: 67,
                orderCount: 123
            },
            {
                id: 'U005',
                avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
                nickname: '孙七',
                phone: '135****8765',
                type: 'normal',
                registerTime: '2024-01-03',
                lastActive: '2024-01-12 08:30',
                status: 'inactive',
                violations: 0,
                publishCount: 5,
                orderCount: 8
            }
        ];

        // 渲染用户列表
        function renderUsers(users = usersData) {
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = users.map(user => `
                <tr class="hover:bg-gray-700 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <img class="h-10 w-10 rounded-full object-cover" src="${user.avatar}" alt="${user.nickname}">
                            <div class="ml-4">
                                <div class="text-sm font-medium text-white">${user.nickname}</div>
                                <div class="text-sm text-gray-400">${user.phone}</div>
                                <div class="text-xs text-gray-500">ID: ${user.id}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded-full ${
                            user.type === 'normal' ? 'bg-blue-600' :
                            user.type === 'merchant' ? 'bg-purple-600' :
                            user.type === 'runner' ? 'bg-green-600' : 'bg-orange-600'
                        }">
                            ${getTypeLabel(user.type)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        ${user.registerTime}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        ${user.lastActive}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded-full ${
                            user.status === 'active' ? 'bg-green-600' :
                            user.status === 'warning' ? 'bg-yellow-600' :
                            user.status === 'banned' ? 'bg-red-600' : 'bg-gray-600'
                        }">
                            ${getStatusLabel(user.status)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm ${
                            user.violations === 0 ? 'text-green-400' :
                            user.violations <= 2 ? 'text-yellow-400' : 'text-red-400'
                        }">
                            ${user.violations} 次
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <div class="flex space-x-2">
                            <button onclick="viewUserDetail('${user.id}')" 
                                    class="text-blue-400 hover:text-blue-300 transition-colors">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button onclick="editUser('${user.id}')" 
                                    class="text-green-400 hover:text-green-300 transition-colors">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button onclick="handleViolation('${user.id}')" 
                                    class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                <i data-lucide="triangle-alert" class="w-4 h-4"></i>
                            </button>
                            <button onclick="banUser('${user.id}')" 
                                    class="text-red-400 hover:text-red-300 transition-colors">
                                <i data-lucide="ban" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 获取用户类型标签
        function getTypeLabel(type) {
            const labels = {
                'normal': '普通用户',
                'merchant': '商家用户',
                'runner': '跑腿员',
                'driver': '司机'
            };
            return labels[type] || type;
        }

        // 获取状态标签
        function getStatusLabel(status) {
            const labels = {
                'active': '正常',
                'warning': '警告',
                'banned': '封禁',
                'inactive': '不活跃'
            };
            return labels[status] || status;
        }

        // 搜索用户
        function searchUsers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            let filteredUsers = usersData;
            
            if (searchTerm) {
                filteredUsers = filteredUsers.filter(user => 
                    user.nickname.toLowerCase().includes(searchTerm) ||
                    user.phone.includes(searchTerm) ||
                    user.id.toLowerCase().includes(searchTerm)
                );
            }
            
            if (statusFilter) {
                filteredUsers = filteredUsers.filter(user => user.status === statusFilter);
            }
            
            if (typeFilter) {
                filteredUsers = filteredUsers.filter(user => user.type === typeFilter);
            }
            
            renderUsers(filteredUsers);
        }

        // 查看用户详情
        function viewUserDetail(userId) {
            const user = usersData.find(u => u.id === userId);
            if (!user) return;
            
            const content = `
                <div class="space-y-6">
                    <div class="flex items-center space-x-4">
                        <img class="h-16 w-16 rounded-full object-cover" src="${user.avatar}" alt="${user.nickname}">
                        <div>
                            <h4 class="text-lg font-semibold">${user.nickname}</h4>
                            <p class="text-gray-400">${user.phone}</p>
                            <p class="text-sm text-gray-500">用户ID: ${user.id}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <p class="text-gray-400 text-sm">用户类型</p>
                            <p class="font-semibold">${getTypeLabel(user.type)}</p>
                        </div>
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <p class="text-gray-400 text-sm">账户状态</p>
                            <p class="font-semibold">${getStatusLabel(user.status)}</p>
                        </div>
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <p class="text-gray-400 text-sm">注册时间</p>
                            <p class="font-semibold">${user.registerTime}</p>
                        </div>
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <p class="text-gray-400 text-sm">最后活跃</p>
                            <p class="font-semibold">${user.lastActive}</p>
                        </div>
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <p class="text-gray-400 text-sm">发布数量</p>
                            <p class="font-semibold">${user.publishCount} 条</p>
                        </div>
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <p class="text-gray-400 text-sm">撮合次数</p>
                            <p class="font-semibold">${user.orderCount} 次</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h5 class="font-semibold mb-2">违规记录</h5>
                        <p class="text-gray-400">累计违规次数: <span class="text-red-400">${user.violations}</span> 次</p>
                        ${user.violations > 0 ? '<p class="text-sm text-gray-500 mt-2">最近违规: 发布违规内容 (2024-01-18)</p>' : '<p class="text-sm text-green-400 mt-2">暂无违规记录</p>'}
                    </div>
                </div>
            `;
            
            document.getElementById('userDetailContent').innerHTML = content;
            document.getElementById('userDetailModal').classList.remove('hidden');
        }

        // 关闭用户详情
        function closeUserDetail() {
            document.getElementById('userDetailModal').classList.add('hidden');
        }

        // 处理违规
        function handleViolation(userId) {
            window.currentUserId = userId;
            document.getElementById('violationModal').classList.remove('hidden');
        }

        // 关闭违规处理模态框
        function closeViolationModal() {
            document.getElementById('violationModal').classList.add('hidden');
            window.currentUserId = null;
        }

        // 提交违规处理
        function submitViolation() {
            const type = document.getElementById('violationType').value;
            const duration = document.getElementById('banDuration').value;
            const reason = document.getElementById('violationReason').value;
            
            if (!reason.trim()) {
                alert('请输入处理原因');
                return;
            }
            
            // 这里应该发送到后端处理
            alert(`违规处理已提交：\n类型: ${type}\n原因: ${reason}${duration ? `\n时长: ${duration}天` : ''}`);
            
            closeViolationModal();
        }

        // 编辑用户
        function editUser(userId) {
            alert(`编辑用户: ${userId}`);
        }

        // 封禁用户
        function banUser(userId) {
            if (confirm('确定要封禁此用户吗？')) {
                alert(`用户 ${userId} 已被封禁`);
            }
        }

        // 导出用户数据
        function exportUsers() {
            alert('用户数据导出功能');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderUsers();
        });
    </script>
</body>
</html>