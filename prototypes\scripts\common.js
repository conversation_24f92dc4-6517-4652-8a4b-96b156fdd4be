/**
 * 本地助手 - 公共JavaScript库
 * 统一管理事件绑定、页面切换、状态管理等功能
 */

// 全局配置
const AppConfig = {
    // 颜色配置
    colors: {
        primary: '#3b82f6',
        secondary: '#8b5cf6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#06b6d4'
    },
    // 动画配置
    animations: {
        duration: 300,
        easing: 'ease'
    },
    // 无障碍配置
    accessibility: {
        minClickArea: 44, // 最小点击区域44px
        focusOutlineWidth: 2
    }
};

// 工具函数
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes);
    }
};

/**
 * 打开导航到指定目的地
 * @param {string} destination - 目的地名称
 */
function openNavigation(destination) {
    console.log('打开导航到:', destination);
    // 尝试打开高德地图导航
    const amapUrl = `amapuri://route/plan/?dlat=&dlon=&dname=${encodeURIComponent(destination)}&dev=0&t=0`;
    // 备用百度地图导航
    const baiduUrl = `baidumap://map/direction?destination=${encodeURIComponent(destination)}&mode=driving`;
    // 备用腾讯地图导航
    const qqmapUrl = `qqmap://map/routeplan?type=drive&to=${encodeURIComponent(destination)}`;
    
    // 尝试打开原生地图应用
    try {
        window.location.href = amapUrl;
    } catch (e) {
        try {
            window.location.href = baiduUrl;
        } catch (e) {
            try {
                window.location.href = qqmapUrl;
            } catch (e) {
                // 如果都失败，打开网页版地图
                window.open(`https://uri.amap.com/navigation?to=${encodeURIComponent(destination)}`, '_blank');
            }
        }
    }
}

/**
 * 拨打电话
 * @param {string} phone - 电话号码
 */
function callPhone(phone) {
    console.log('拨打电话:', phone);
    // 直接拨号
    window.location.href = `tel:${phone}`;
}

// 页面管理器
const PageManager = {
    currentPage: 'home',
    
    // 切换页面
    switchPage(pageId, element = null) {
        try {
            // 隐藏所有页面内容
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
                page.setAttribute('aria-hidden', 'true');
            });
            
            // 移除所有导航项的激活状态
            document.querySelectorAll('.bottom-nav .nav-item, .nav-item').forEach(item => {
                item.classList.remove('active');
                item.setAttribute('aria-selected', 'false');
            });
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId + '-page');
            if (targetPage) {
                targetPage.classList.add('active');
                targetPage.setAttribute('aria-hidden', 'false');
                this.currentPage = pageId;
            } else {
                // 如果是真实页面跳转
                this.navigateToPage(pageId);
                return;
            }
            
            // 激活对应的导航项
            if (element) {
                element.classList.add('active');
                element.setAttribute('aria-selected', 'true');
            }
            
            // 触发页面切换事件
            document.dispatchEvent(new CustomEvent('pageChanged', {
                detail: { pageId, previousPage: this.currentPage }
            }));
            
        } catch (error) {
            console.error('页面切换失败:', error);
            showToast('页面切换失败', 'error');
        }
    },
    
    // 导航到真实页面
    navigateToPage(pageId) {
        const pageMap = {
            'orders': 'orders-management.html',
            'messages': 'message.html',
            'profile': 'profile.html',
            'shops': 'shops.html',
            'information': 'information.html',
            'transportation': 'transportation.html',
            'errands': 'errands.html',
            'login': 'login.html',
            'settings': 'settings.html',
            'help': 'help.html'
        };
        
        const targetPage = pageMap[pageId];
        if (targetPage) {
            window.location.href = targetPage;
        } else {
            showToast('页面不存在', 'error');
        }
    },
    
    // 返回上一页
    goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            this.navigateToPage('home');
        }
    }
};

// 模块管理器
const ModuleManager = {
    currentModule: 'shops',
    
    // 切换模块
    switchModule(moduleId, element = null) {
        try {
            // 移除所有模块导航的激活状态
            document.querySelectorAll('.module-nav .nav-item, .nav-item').forEach(item => {
                item.classList.remove('active');
                item.setAttribute('aria-selected', 'false');
            });
            
            // 激活对应的模块导航项
            if (element) {
                element.classList.add('active');
                element.setAttribute('aria-selected', 'true');
            }
            
            // 隐藏所有模块内容
            document.querySelectorAll('.module-content').forEach(content => {
                content.classList.add('hidden');
                content.setAttribute('aria-hidden', 'true');
            });
            
            // 显示目标模块内容
            const targetModule = document.getElementById(moduleId + '-module');
            if (targetModule) {
                targetModule.classList.remove('hidden');
                targetModule.setAttribute('aria-hidden', 'false');
                this.currentModule = moduleId;
            }
            
            // 触发模块切换事件
            document.dispatchEvent(new CustomEvent('moduleChanged', {
                detail: { moduleId, previousModule: this.currentModule }
            }));
            
        } catch (error) {
            console.error('模块切换失败:', error);
            showToast('模块切换失败', 'error');
        }
    }
};

// 标签页管理器
const TabManager = {
    // 切换标签页
    switchTab(tabId, element = null) {
        try {
            const tabGroup = element ? element.closest('[role="tablist"]') : document;
            
            // 移除同组所有标签的激活状态
            tabGroup.querySelectorAll('.tab-btn, [role="tab"]').forEach(tab => {
                tab.classList.remove('tab-active', 'active');
                tab.classList.add('text-slate-400');
                tab.setAttribute('aria-selected', 'false');
            });
            
            // 激活当前标签
            if (element) {
                element.classList.add('tab-active', 'active');
                element.classList.remove('text-slate-400');
                element.setAttribute('aria-selected', 'true');
            }
            
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content, [role="tabpanel"]').forEach(content => {
                content.classList.add('hidden');
                content.setAttribute('aria-hidden', 'true');
            });
            
            // 显示目标标签内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.remove('hidden');
                targetContent.setAttribute('aria-hidden', 'false');
            }
            
        } catch (error) {
            console.error('标签切换失败:', error);
            showToast('标签切换失败', 'error');
        }
    }
};

/**
 * 显示Toast提示
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型 (success, error, warning, info)
 */
function showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 transform translate-x-full`;
    
    // 根据类型设置颜色
    switch(type) {
        case 'success':
            toast.classList.add('bg-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            break;
        case 'warning':
            toast.classList.add('bg-yellow-500');
            break;
        default:
            toast.classList.add('bg-blue-500');
    }
    
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * 通用筛选函数 - 重置按钮状态
 * @param {string} selector - 按钮选择器
 * @param {string} activeClass - 激活状态的CSS类
 * @param {string} inactiveClass - 非激活状态的CSS类
 */
function resetFilterButtons(selector, activeClass, inactiveClass) {
    document.querySelectorAll(selector).forEach(btn => {
        btn.classList.remove(...activeClass.split(' '));
        btn.classList.add(...inactiveClass.split(' '));
    });
}

/**
 * 通用筛选函数 - 设置按钮激活状态
 * @param {Element} element - 要激活的按钮元素
 * @param {string} activeClass - 激活状态的CSS类
 * @param {string} inactiveClass - 非激活状态的CSS类
 */
function setActiveFilterButton(element, activeClass, inactiveClass) {
    element.classList.remove(...inactiveClass.split(' '));
    element.classList.add(...activeClass.split(' '));
}

/**
 * 收藏功能切换
 * @param {Element} button - 收藏按钮元素
 * @param {string} message - 提示消息前缀
 */
function toggleFavorite(button, message = '') {
    const icon = button.querySelector('i') || button.querySelector('[data-lucide="heart"]');
    const isFavorited = icon.classList.contains('text-red-400') || icon.classList.contains('fill-current');
    
    if (isFavorited) {
        icon.classList.remove('text-red-400', 'fill-current');
        icon.classList.add('text-slate-400');
        button.classList.remove('bg-red-50', 'text-red-600');
        button.classList.add('bg-slate-100', 'text-slate-600');
        showToast(`${message}已取消收藏`);
    } else {
        icon.classList.remove('text-slate-400');
        icon.classList.add('text-red-400', 'fill-current');
        button.classList.remove('bg-slate-100', 'text-slate-600');
        button.classList.add('bg-red-50', 'text-red-600');
        showToast(`${message}已添加到收藏`);
    }
}

/**
 * 展开/收起描述文字
 * @param {Element} button - 展开按钮
 */
function toggleDescription(button) {
    const container = button.closest('.description-container');
    const textElement = container.querySelector('.description-text');
    
    if (textElement.classList.contains('line-clamp-2')) {
        textElement.classList.remove('line-clamp-2');
        button.textContent = '收起';
    } else {
        textElement.classList.add('line-clamp-2');
        button.textContent = '展开';
    }
}

/**
 * 初始化描述文字展开功能
 */
function initDescriptionToggle() {
    document.querySelectorAll('.description-container').forEach(container => {
        const textElement = container.querySelector('.description-text');
        const expandBtn = container.querySelector('.expand-btn');
        
        // 检查文字是否被截断
        if (textElement && expandBtn && textElement.scrollHeight > textElement.clientHeight) {
            expandBtn.classList.remove('hidden');
        }
    });
}

// 事件管理器
const EventManager = {
    // 初始化所有事件监听器
    init() {
        this.initNavigationEvents();
        this.initTabEvents();
        this.initFormEvents();
        this.initAccessibilityEvents();
        this.initCommonEvents();
    },
    
    // 初始化导航事件
    initNavigationEvents() {
        // 底部导航事件 - 使用事件委托
        document.addEventListener('click', (e) => {
            const navItem = e.target.closest('.bottom-nav .nav-item');
            if (navItem && !navItem.hasAttribute('data-processed')) {
                e.preventDefault();
                const pageId = navItem.getAttribute('data-page') || 
                             this.getPageIdFromNavItem(navItem);
                if (pageId) {
                    PageManager.switchPage(pageId, navItem);
                }
            }
        });
        
        // 模块导航事件
        document.addEventListener('click', (e) => {
            const moduleItem = e.target.closest('.module-nav .nav-item, .nav-item[data-module]');
            if (moduleItem && !moduleItem.hasAttribute('data-processed')) {
                e.preventDefault();
                const moduleId = moduleItem.getAttribute('data-module') ||
                               this.getModuleIdFromNavItem(moduleItem);
                if (moduleId) {
                    ModuleManager.switchModule(moduleId, moduleItem);
                }
            }
        });
        
        // 返回按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.back-btn, [data-action="back"]')) {
                e.preventDefault();
                PageManager.goBack();
            }
        });
    },
    
    // 初始化标签页事件
    initTabEvents() {
        document.addEventListener('click', (e) => {
            const tabBtn = e.target.closest('.tab-btn, [role="tab"]');
            if (tabBtn) {
                e.preventDefault();
                const tabId = tabBtn.getAttribute('data-tab') || tabBtn.getAttribute('aria-controls');
                if (tabId) {
                    TabManager.switchTab(tabId, tabBtn);
                }
            }
        });
    },
    
    // 初始化表单事件
    initFormEvents() {
        // 表单提交事件
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                const requiredFields = form.querySelectorAll('[required]');
                let hasErrors = false;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        hasErrors = true;
                        this.showFieldError(field, '此字段为必填项');
                    } else {
                        this.clearFieldError(field);
                    }
                });
                
                if (hasErrors) {
                    e.preventDefault();
                    showToast('请填写所有必填字段', 'error');
                }
            }
        });
        
        // 输入框实时验证
        document.addEventListener('blur', (e) => {
            const field = e.target;
            if (field.hasAttribute('required') && !field.value.trim()) {
                this.showFieldError(field, '此字段为必填项');
            } else {
                this.clearFieldError(field);
            }
        });
    },
    
    // 初始化无障碍事件
    initAccessibilityEvents() {
        // 键盘导航支持
        document.addEventListener('keydown', (e) => {
            // Tab键导航
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
            
            // Enter和Space键激活按钮
            if (e.key === 'Enter' || e.key === ' ') {
                const target = e.target;
                if (target.tagName === 'BUTTON' || target.getAttribute('role') === 'button') {
                    e.preventDefault();
                    target.click();
                }
            }
            
            // 方向键导航标签页
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                const currentTab = e.target;
                if (currentTab.getAttribute('role') === 'tab') {
                    e.preventDefault();
                    const tabList = currentTab.closest('[role="tablist"]');
                    const tabs = Array.from(tabList.querySelectorAll('[role="tab"]'));
                    const currentIndex = tabs.indexOf(currentTab);
                    
                    let nextIndex;
                    if (e.key === 'ArrowLeft') {
                        nextIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                    } else {
                        nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                    }
                    
                    tabs[nextIndex].focus();
                    tabs[nextIndex].click();
                }
            }
        });
        
        // 鼠标使用时移除键盘导航样式
        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    },
    
    // 初始化通用事件
    initCommonEvents() {
        // 使用事件委托统一处理所有 data-action 点击事件
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;
            
            e.preventDefault();
            const action = target.dataset.action;
            const orderId = target.dataset.orderId;
            
            // 处理不同类型的操作
            switch (action) {
                // 发布相关
                case 'publish':
                    this.showPublishOptions();
                    break;
                    
                // 扫码相关
                case 'scan':
                    this.openQRScanner();
                    break;
                    
                // 收藏功能
                case 'favorite':
                    toggleFavorite(target);
                    break;
                    
                // 分享功能
                case 'share':
                    this.handleShare(target);
                    break;
                    
                // 呼叫服务
                case 'call':
                    const phone = target.dataset.phone;
                    if (phone) {
                        callPhone(phone);
                    } else {
                        showToast('电话号码不可用', 'error');
                    }
                    break;
                    
                // 订单操作
                case 'view-detail':
                case 'cancel-order':
                case 'contact-runner':
                case 'track-order':
                case 'rate-order':
                case 'reorder':
                    if (orderId) {
                        this.handleOrderAction(action, orderId, target);
                    } else {
                        showToast('订单信息不完整', 'error');
                    }
                    break;
                    
                // 加载更多
                case 'load-more':
                    this.handleLoadMore(target);
                    break;
                    
                // 其他未定义的操作
                default:
                    console.log(`未处理的操作: ${action}`);
                    showToast('功能开发中，敬请期待', 'info');
            }
        });
        
        // 处理悬浮按钮（兼容旧版本类名）
        document.addEventListener('click', (e) => {
            if (e.target.closest('.floating-action') && !e.target.closest('[data-action]')) {
                e.preventDefault();
                this.showPublishOptions();
            }
        });
    },
    
    // 处理分享功能
    handleShare(element) {
        const title = element.dataset.title || document.title;
        const url = element.dataset.url || window.location.href;
        const text = element.dataset.text || '分享一个不错的内容';
        
        if (navigator.share) {
            navigator.share({
                title: title,
                text: text,
                url: url
            }).then(() => {
                showToast('分享成功', 'success');
            }).catch((error) => {
                console.log('分享失败:', error);
                this.fallbackShare(title, url);
            });
        } else {
            this.fallbackShare(title, url);
        }
    },
    
    // 分享功能降级处理
    fallbackShare(title, url) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                showToast('链接已复制到剪贴板', 'success');
            }).catch(() => {
                showToast('分享功能暂不可用', 'error');
            });
        } else {
            showToast('分享功能暂不可用', 'error');
        }
    },
    
    // 获取导航项对应的页面ID
    getPageIdFromNavItem(navItem) {
        const span = navItem.querySelector('span');
        if (span) {
            const text = span.textContent.trim();
            const pageMap = {
                '首页': 'home',
                '订单': 'orders',
                '消息': 'messages',
                '我的': 'profile'
            };
            return pageMap[text] || text.toLowerCase();
        }
        return null;
    },
    
    // 获取导航项对应的模块ID
    getModuleIdFromNavItem(navItem) {
        const span = navItem.querySelector('span');
        if (span) {
            const text = span.textContent.trim();
            const moduleMap = {
                '商铺': 'shops',
                '信息': 'information',
                '出行': 'transportation',
                '跑腿': 'errands'
            };
            return moduleMap[text] || text.toLowerCase();
        }
        return null;
    },
    
    // 显示字段错误
    showFieldError(field, message) {
        field.classList.add('border-red-500', 'focus:ring-red-500');
        field.classList.remove('border-slate-600', 'focus:ring-blue-500');
        
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-400 text-xs mt-1';
            errorElement.setAttribute('role', 'alert');
            field.parentNode.appendChild(errorElement);
        }
        errorElement.textContent = message;
    },
    
    // 清除字段错误
    clearFieldError(field) {
        field.classList.remove('border-red-500', 'focus:ring-red-500');
        field.classList.add('border-slate-600', 'focus:ring-blue-500');
        
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    },
    
    // 显示发布选项
    showPublishOptions() {
        // 创建发布选项弹窗
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50';
        modal.innerHTML = `
            <div class="bg-slate-800 rounded-t-xl w-full max-w-md p-6 transform translate-y-0 transition-transform duration-300">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-slate-200">选择发布类型</h3>
                    <button class="close-modal w-8 h-8 flex items-center justify-center rounded-lg hover:bg-slate-700 transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <a href="publish-shop.html" class="flex flex-col items-center p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-blue-400 mb-2"></i>
                        <span class="text-sm text-slate-200">发布商铺</span>
                    </a>
                    <a href="publish-information.html" class="flex flex-col items-center p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-green-400 mb-2"></i>
                        <span class="text-sm text-slate-200">发布信息</span>
                    </a>
                    <a href="publish-transportation.html" class="flex flex-col items-center p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-purple-400 mb-2"></i>
                        <span class="text-sm text-slate-200">发布出行</span>
                    </a>
                    <a href="publish-errands.html" class="flex flex-col items-center p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                        <i data-lucide="package" class="w-8 h-8 text-orange-400 mb-2"></i>
                        <span class="text-sm text-slate-200">发布跑腿</span>
                    </a>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        // 关闭弹窗事件
        modal.addEventListener('click', (e) => {
            if (e.target === modal || e.target.closest('.close-modal')) {
                modal.remove();
            }
        });
    },
    
    // 打开二维码扫描
    openQRScanner() {
        showToast('正在启动扫码功能...', 'info');
        
        // 检查是否支持摄像头
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            // 模拟扫码启动过程
            setTimeout(() => {
                showToast('扫码功能开发中，敬请期待', 'warning');
            }, 1000);
        } else {
            showToast('您的设备不支持摄像头功能', 'error');
        }
    },

    // 处理订单操作
    handleOrderAction(action, orderId, element) {
        console.log(`🎯 处理订单操作: ${action}, 订单ID: ${orderId}`);
        
        switch (action) {
            case 'view-detail':
                window.location.href = `order-detail.html?id=${orderId}`;
                break;
            case 'cancel-order':
                this.showConfirmDialog(
                    '确认取消订单？',
                    '取消后无法恢复，确定要取消这个订单吗？',
                    () => {
                        App.setLoadingState(true, element);
                        setTimeout(() => {
                            App.setLoadingState(false, element);
                            showToast('订单已取消', 'success');
                            // 刷新订单列表
                            this.refreshOrderList();
                        }, 1500);
                    }
                );
                break;
            case 'contact-runner':
                const phone = element.dataset.phone || '************';
                callPhone(phone);
                break;
            case 'track-order':
                window.location.href = `order-tracking.html?id=${orderId}`;
                break;
            case 'rate-order':
                window.location.href = `order-rating.html?id=${orderId}`;
                break;
            case 'reorder':
                App.setLoadingState(true, element);
                setTimeout(() => {
                    App.setLoadingState(false, element);
                    showToast('已添加到购物车', 'success');
                }, 1000);
                break;
            default:
                showToast('功能开发中，敬请期待', 'info');
        }
    },

    // 显示确认对话框
    showConfirmDialog(title, message, onConfirm, onCancel = null) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.innerHTML = `
            <div class="bg-slate-800 rounded-xl max-w-sm w-full p-6 transform scale-95 transition-transform duration-200">
                <h3 class="text-lg font-semibold text-slate-200 mb-2">${title}</h3>
                <p class="text-slate-400 mb-6">${message}</p>
                <div class="flex gap-3">
                    <button class="cancel-btn flex-1 px-4 py-2 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors">
                        取消
                    </button>
                    <button class="confirm-btn flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        确认
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 显示动画
        setTimeout(() => {
            modal.querySelector('div').style.transform = 'scale(1)';
        }, 10);
        
        // 事件处理
        modal.querySelector('.cancel-btn').addEventListener('click', () => {
            this.closeModal(modal);
            if (onCancel) onCancel();
        });
        
        modal.querySelector('.confirm-btn').addEventListener('click', () => {
            this.closeModal(modal);
            if (onConfirm) onConfirm();
        });
        
        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
                if (onCancel) onCancel();
            }
        });
    },

    // 关闭模态框
    closeModal(modal) {
        const content = modal.querySelector('div');
        content.style.transform = 'scale(0.95)';
        setTimeout(() => {
            if (document.body.contains(modal)) {
                document.body.removeChild(modal);
            }
        }, 200);
    },

    // 刷新订单列表
    refreshOrderList() {
        console.log('🔄 刷新订单列表');
        // 实际项目中这里会调用API刷新数据
        const orderList = document.querySelector('.order-list');
        if (orderList) {
            // 模拟数据刷新
            setTimeout(() => {
                console.log('✅ 订单列表已刷新');
            }, 500);
        }
    },

    // 处理加载更多
    handleLoadMore(element) {
        App.setLoadingState(true, element);
        
        // 模拟加载更多数据
        setTimeout(() => {
            App.setLoadingState(false, element);
            
            // 更新按钮文本
            const loadingText = element.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = '加载更多';
            }
            
            showToast('已加载更多内容', 'success');
        }, 2000);
    }
};

// 应用初始化
const App = {
    // 应用状态
    state: {
        currentPage: 'home',
        currentModule: 'shops',
        isLoading: false,
        user: null,
        notifications: []
    },

    init() {
        console.log('🚀 LocalHelper App 初始化中...');
        
        // 初始化事件管理器
        EventManager.init();
        
        // 初始化Lucide图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        // 初始化描述文字展开功能
        initDescriptionToggle();
        
        // 添加无障碍样式
        this.addAccessibilityStyles();
        
        // 初始化键盘导航检测
        this.initKeyboardDetection();
        
        // 初始化响应式处理
        this.initResponsiveHandling();
        
        // 恢复保存的状态
        this.restoreState();
        
        // 页面加载完成事件
        document.dispatchEvent(new CustomEvent('appReady'));
        
        console.log('✅ LocalHelper App 初始化完成');
    },

    // 初始化键盘导航检测
    initKeyboardDetection() {
        let isKeyboardUser = false;
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                isKeyboardUser = true;
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            if (isKeyboardUser) {
                isKeyboardUser = false;
                document.body.classList.remove('keyboard-navigation');
            }
        });
    },

    // 初始化响应式处理
    initResponsiveHandling() {
        const handleResize = () => {
            const isMobile = window.innerWidth < 768;
            document.body.classList.toggle('mobile', isMobile);
            document.body.classList.toggle('desktop', !isMobile);
        };
        
        handleResize();
        window.addEventListener('resize', Utils.throttle(handleResize, 250));
    },

    // 恢复保存的状态
    restoreState() {
        try {
            const savedState = localStorage.getItem('localhelper-state');
            if (savedState) {
                const state = JSON.parse(savedState);
                this.state = { ...this.state, ...state };
                console.log('📦 状态已恢复:', this.state);
            }
        } catch (e) {
            console.warn('⚠️ 无法恢复保存的状态:', e);
        }
    },

    // 保存状态
    saveState() {
        try {
            localStorage.setItem('localhelper-state', JSON.stringify(this.state));
        } catch (e) {
            console.warn('⚠️ 无法保存状态:', e);
        }
    },

    // 设置加载状态
    setLoadingState(isLoading, target = null) {
        this.state.isLoading = isLoading;
        
        if (target) {
            if (isLoading) {
                target.classList.add('loading');
                target.disabled = true;
                target.setAttribute('aria-busy', 'true');
            } else {
                target.classList.remove('loading');
                target.disabled = false;
                target.setAttribute('aria-busy', 'false');
            }
        } else {
            // 全局加载状态
            document.querySelectorAll('[data-loading]').forEach(element => {
                if (isLoading) {
                    element.classList.add('loading');
                    element.disabled = true;
                } else {
                    element.classList.remove('loading');
                    element.disabled = false;
                }
            });
        }
    },
    
    // 添加无障碍样式
    addAccessibilityStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 键盘导航样式 */
            .keyboard-navigation *:focus {
                outline: 2px solid #3b82f6 !important;
                outline-offset: 2px !important;
            }
            
            /* 最小点击区域 */
            button, .nav-item, .tab-btn, [role="button"], [role="tab"] {
                min-width: 44px;
                min-height: 44px;
            }
            
            /* 提高文字对比度 */
            .text-slate-400 {
                color: #94a3b8 !important;
            }
            
            /* 错误状态样式 */
            .field-error {
                font-size: 0.75rem;
                color: #f87171;
                margin-top: 0.25rem;
            }
            
            /* 加载状态 */
            .loading {
                pointer-events: none;
                opacity: 0.7;
            }
            
            /* 动画减少偏好 */
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        `;
        document.head.appendChild(style);
    }
};

// 导出全局对象
window.LocalHelper = {
    App,
    PageManager,
    ModuleManager,
    TabManager,
    EventManager,
    Utils,
    AppConfig
};

// 兼容旧版本的全局函数
window.switchPage = (pageId) => PageManager.switchPage(pageId);
window.switchModule = (moduleId) => ModuleManager.switchModule(moduleId);
window.switchTab = (tabId) => TabManager.switchTab(tabId);
window.showPublishOptions = () => EventManager.showPublishOptions();
window.openQRScanner = () => EventManager.openQRScanner();

// 自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => App.init());
} else {
    App.init();
}