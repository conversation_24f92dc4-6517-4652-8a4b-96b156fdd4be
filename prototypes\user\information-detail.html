<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息详情 - 同城服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/common.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
        }
        .tech-glow {
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.1);
        }
        .info-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .verified-tag {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .distance-tag {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }
        .price-tag {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        .contact-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .contact-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
        .action-btn {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .action-btn:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(147, 51, 234, 0.3) 100%);
        }
        .safety-collapsible {
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .safety-collapsed .safety-content {
            max-height: 0;
            opacity: 0;
        }
        .safety-expanded .safety-content {
            max-height: 500px;
            opacity: 1;
        }
        .chevron-rotate {
            transition: transform 0.3s ease;
        }
        .chevron-rotate.rotated {
            transform: rotate(180deg);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 to-slate-800 min-h-screen text-white">
    <!-- 顶部导航 -->    
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="flex items-center space-x-2 text-slate-300 hover:text-white transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    <span>返回</span>
                </button>
                <h1 class="text-lg font-semibold text-white">信息详情</h1>
                <div class="w-6"></div>
            </div>
        </div>
    </div>

    <main class="max-w-md mx-auto px-4 pt-4 pb-24 space-y-4">
        <!-- 安全提醒 -->
        <div id="safetyReminder" class="tech-card tech-glow p-4 safety-collapsible safety-collapsed">
            <div class="flex items-center justify-between cursor-pointer" onclick="toggleSafetyContent()">
                <div class="flex items-center space-x-3">
                    <i data-lucide="shield-check" class="w-5 h-5 text-blue-400"></i>
                    <h4 class="text-sm font-medium text-blue-400">安全交易提醒</h4>
                </div>
                <div class="flex items-center space-x-2">
                    <span id="countdown-timer" class="text-xs text-blue-400 font-mono">5s</span>
                    <button onclick="event.stopPropagation(); closeSafetyReminder()" class="text-blue-400 hover:text-blue-200">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                    <i data-lucide="chevron-down" class="w-4 h-4 text-blue-400 chevron-rotate"></i>
                </div>
            </div>
            <div class="safety-content mt-3 transition-all duration-300">
                <div class="text-xs text-blue-300/90 space-y-2">
                    <div class="bg-blue-500/10 rounded-lg p-3">
                        <h5 class="font-medium text-blue-300 mb-2">交易安全指南</h5>
                        <ul class="space-y-1">
                            <li>• 选择公共场所进行交易，避免私人住所</li>
                            <li>• 当面验货，确认物品状态后再付款</li>
                            <li>• 警惕过低价格，谨防诈骗陷阱</li>
                            <li>• 保留交易记录和聊天截图</li>
                        </ul>
                    </div>
                    <div class="bg-red-500/10 rounded-lg p-3">
                        <h5 class="font-medium text-red-300 mb-2">紧急情况处理</h5>
                        <p>如遇紧急情况请立即报警：<span class="font-bold text-red-400">110</span></p>
                        <p>平台客服热线：<span class="font-bold text-blue-400">400-888-0000</span></p>
                    </div>
                    <div class="bg-slate-500/10 rounded-lg p-3">
                        <h5 class="font-medium text-slate-300 mb-1">平台免责声明</h5>
                        <p class="text-xs text-slate-400">本平台仅提供信息发布服务，交易风险由用户自行承担。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息详情卡片 -->
        <div id="infoDetailCard" class="tech-card tech-glow p-4">
            <!-- 标题和价格 -->
            <div class="mb-4">
                <div class="mb-2">
                    <h2 class="text-xl font-bold text-white inline">精装一室一厅出租</h2>
                    <span class="ml-3 bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded text-sm font-bold">
                        ¥2,800/月
                    </span>
                </div>
                <p class="text-sm text-slate-400 mb-3">发布时间：2024-01-15 14:30</p>
            </div>

            <!-- 标签和操作按钮 -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <span class="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs px-3 py-1.5 rounded-lg font-medium flex items-center space-x-1.5">
                        <i data-lucide="home" class="w-3.5 h-3.5"></i>
                        <span>租房</span>
                    </span>
                    <span class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white text-xs px-3 py-1.5 rounded-lg font-medium flex items-center space-x-1.5">
                        <i data-lucide="shield-check" class="w-3.5 h-3.5"></i>
                        <span>已认证</span>
                    </span>
                    <span class="bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs px-3 py-1.5 rounded-lg font-medium flex items-center space-x-1.5">
                        <i data-lucide="map-pin" class="w-3.5 h-3.5"></i>
                        <span>2.1km</span>
                    </span>
                    <button onclick="makeCall('13800138001')" class="bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs px-3 py-1.5 rounded-lg font-medium transition-all hover:from-green-600 hover:to-emerald-700 flex items-center space-x-1.5 ml-3">
                        <i data-lucide="phone" class="w-3.5 h-3.5"></i>
                        <span>电话</span>
                    </button>
                </div>
            </div>



            <!-- 描述 -->
            <div class="mb-4">
                <h3 class="text-lg font-semibold text-blue-400 mb-3 flex items-center space-x-2">
                    <i data-lucide="file-text" class="w-5 h-5"></i>
                    <span>详细描述</span>
                </h3>
                <p class="text-slate-300 leading-relaxed mb-3">精装修，家具家电齐全，拎包入住。小区环境优美，交通便利，近地铁站。房间朝南采光好，24小时热水，独立卫生间。配套设施完善，周边商超、学校、医院一应俱全。</p>
                <!-- 操作按钮 -->
                 <div class="flex items-center space-x-4">
                     <button onclick="toggleFavorite(this)" class="flex items-center space-x-1.5 px-3 py-2 rounded-lg bg-slate-700/50 hover:bg-red-500/20 text-slate-300 hover:text-red-400 transition-all duration-200 border border-slate-600 hover:border-red-400">
                         <i data-lucide="heart" class="w-4 h-4"></i>
                         <span class="text-sm font-medium">收藏</span>
                     </button>
                     <button onclick="shareInfo()" class="flex items-center space-x-1.5 px-3 py-2 rounded-lg bg-slate-700/50 hover:bg-blue-500/20 text-slate-300 hover:text-blue-400 transition-all duration-200 border border-slate-600 hover:border-blue-400">
                         <i data-lucide="share-2" class="w-4 h-4"></i>
                         <span class="text-sm font-medium">分享</span>
                     </button>
                     <button onclick="reportInfo()" class="flex items-center space-x-1.5 px-3 py-2 rounded-lg bg-slate-700/50 hover:bg-amber-500/20 text-slate-300 hover:text-amber-400 transition-all duration-200 border border-slate-600 hover:border-amber-400">
                         <i data-lucide="flag" class="w-4 h-4"></i>
                         <span class="text-sm font-medium">举报</span>
                     </button>
                 </div>
            </div>

            <!-- 图片展示 -->
             <div class="mb-4">
                 <div class="grid grid-cols-2 gap-3">
                     <img src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop" alt="房间" class="w-full h-32 rounded-lg object-cover cursor-pointer" onclick="openImageGallery(0)">
                     <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop" alt="客厅" class="w-full h-32 rounded-lg object-cover cursor-pointer" onclick="openImageGallery(1)">
                     <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop" alt="厨房" class="w-full h-32 rounded-lg object-cover cursor-pointer" onclick="openImageGallery(2)">
                 </div>
             </div>

            <!-- 位置信息 -->
            <div class="mb-4">
                <div class="text-slate-300">
                    <span class="text-slate-400">地址：</span>福田区车公庙地铁站附近小区
                </div>
            </div>



            <!-- 温馨提示 -->
            <div class="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                <div class="flex items-start space-x-2">
                    <i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0"></i>
                    <div class="text-xs text-yellow-300">
                        <p class="font-medium mb-1">温馨提示</p>
                        <p>请谨慎核实信息真实性，选择安全的交易方式，保护个人财产安全。</p>
                    </div>
                </div>
            </div>
         </div>

         <!-- 相关推荐 -->
         <div class="tech-card tech-glow p-4">
             <h3 class="text-lg font-semibold text-white mb-3 flex items-center space-x-2">
                 <i data-lucide="thumbs-up" class="w-5 h-5 text-blue-400"></i>
                 <span>相关推荐</span>
             </h3>
             <div class="space-y-3">
                 <div class="bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" onclick="viewRecommendation(1)">
                     <div class="flex justify-between items-start mb-2">
                         <h4 class="font-medium text-white text-sm">科技园一室一厅精装公寓</h4>
                         <span class="price-tag text-white text-xs px-2 py-1 rounded-lg font-bold">¥2,600/月</span>
                     </div>
                     <div class="flex items-center justify-between">
                         <div class="flex items-center space-x-2">
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="home" class="w-2.5 h-2.5"></i>
                                 <span>租房</span>
                             </span>
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="shield-check" class="w-2.5 h-2.5"></i>
                                 <span>已认证</span>
                             </span>
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="map-pin" class="w-2.5 h-2.5"></i>
                                 <span>1.8km</span>
                             </span>
                         </div>
                         <span class="text-xs text-slate-400">2小时前</span>
                     </div>
                 </div>
                 <div class="bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" onclick="viewRecommendation(2)">
                     <div class="flex justify-between items-start mb-2">
                         <h4 class="font-medium text-white text-sm">高新园两室一厅豪华装修</h4>
                         <span class="price-tag text-white text-xs px-2 py-1 rounded-lg font-bold">¥3,200/月</span>
                     </div>
                     <div class="flex items-center justify-between">
                         <div class="flex items-center space-x-2">
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="home" class="w-2.5 h-2.5"></i>
                                 <span>租房</span>
                             </span>
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="shield-check" class="w-2.5 h-2.5"></i>
                                 <span>已认证</span>
                             </span>
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="map-pin" class="w-2.5 h-2.5"></i>
                                 <span>3.5km</span>
                             </span>
                         </div>
                         <span class="text-xs text-slate-400">5小时前</span>
                     </div>
                 </div>
                 <div class="bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" onclick="viewRecommendation(3)">
                     <div class="flex justify-between items-start mb-2">
                         <h4 class="font-medium text-white text-sm">南山区精装单间出租</h4>
                         <span class="price-tag text-white text-xs px-2 py-1 rounded-lg font-bold">¥1,800/月</span>
                     </div>
                     <div class="flex items-center justify-between">
                         <div class="flex items-center space-x-2">
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="home" class="w-2.5 h-2.5"></i>
                                 <span>租房</span>
                             </span>
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="shield-check" class="w-2.5 h-2.5"></i>
                                 <span>已认证</span>
                             </span>
                             <span class="action-btn text-white text-xs px-1.5 py-0.5 rounded font-medium flex items-center space-x-1">
                                 <i data-lucide="map-pin" class="w-2.5 h-2.5"></i>
                                 <span>4.2km</span>
                             </span>
                         </div>
                         <span class="text-xs text-slate-400">1天前</span>
                     </div>
                 </div>
             </div>
         </div>
     </main>

    <!-- 图片查看模态框 -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center" onclick="closeImageModal()">
        <div class="relative max-w-full max-h-full p-4" onclick="event.stopPropagation()">
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain">
            <!-- 左右切换按钮 -->
            <button id="prevBtn" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-3 hover:bg-opacity-70 transition-all" onclick="previousImage()">
                <i data-lucide="chevron-left" class="w-6 h-6"></i>
            </button>
            <button id="nextBtn" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-50 rounded-full p-3 hover:bg-opacity-70 transition-all" onclick="nextImage()">
                <i data-lucide="chevron-right" class="w-6 h-6"></i>
            </button>
            <!-- 关闭按钮 -->
            <button class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-70 transition-all" onclick="closeImageModal()">
                <i data-lucide="x" class="w-6 h-6"></i>
            </button>
            <!-- 图片指示器 -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                <div id="indicator0" class="w-2 h-2 bg-white bg-opacity-50 rounded-full cursor-pointer" onclick="goToImage(0)"></div>
                <div id="indicator1" class="w-2 h-2 bg-white bg-opacity-50 rounded-full cursor-pointer" onclick="goToImage(1)"></div>
                <div id="indicator2" class="w-2 h-2 bg-white bg-opacity-50 rounded-full cursor-pointer" onclick="goToImage(2)"></div>
            </div>
        </div>
    </div>

    <script>
        // 安全提醒相关功能
        function toggleSafetyContent() {
            const reminder = document.getElementById('safetyReminder');
            const chevron = reminder.querySelector('.chevron-rotate');
            
            if (reminder.classList.contains('safety-collapsed')) {
                reminder.classList.remove('safety-collapsed');
                reminder.classList.add('safety-expanded');
                chevron.classList.add('rotated');
            } else {
                reminder.classList.remove('safety-expanded');
                reminder.classList.add('safety-collapsed');
                chevron.classList.remove('rotated');
            }
        }

        function closeSafetyReminder() {
            document.getElementById('safetyReminder').style.display = 'none';
        }

        function startCountdown() {
            let seconds = 5;
            const timer = document.getElementById('countdown-timer');
            
            const countdown = setInterval(() => {
                seconds--;
                if (timer) {
                    timer.textContent = `${seconds}s`;
                }
                
                if (seconds <= 0) {
                    clearInterval(countdown);
                    closeSafetyReminder();
                }
            }, 1000);
        }

        // 其他功能
        function goBack() {
            window.history.back();
        }

        // 收藏功能已移至公共脚本 common.js
        function toggleFavoriteLocal(button) {
            const icon = button.querySelector('i');
            const text = button.querySelector('span');
            const isFavorited = button.classList.contains('favorited');
            
            if (isFavorited) {
                // 取消收藏
                button.classList.remove('favorited');
                button.classList.remove('bg-red-500/30', 'border-red-400', 'text-red-400');
                button.classList.add('bg-slate-700/50', 'border-slate-600', 'text-slate-300');
                text.textContent = '收藏';
                showToast('已取消收藏');
            } else {
                // 添加收藏
                button.classList.add('favorited');
                button.classList.remove('bg-slate-700/50', 'border-slate-600', 'text-slate-300');
                button.classList.add('bg-red-500/30', 'border-red-400', 'text-red-400');
                text.textContent = '已收藏';
                showToast('已添加到收藏');
            }
        }

        function shareInfo() {
            // 创建分享选项模态框
            const shareModal = document.createElement('div');
            shareModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            shareModal.innerHTML = `
                <div class="bg-slate-800 rounded-lg p-6 max-w-sm mx-4 border border-slate-600">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                        <i data-lucide="share-2" class="w-5 h-5 text-blue-400"></i>
                        <span>分享到</span>
                    </h3>
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <button onclick="shareToWeChat()" class="flex flex-col items-center space-y-2 p-3 rounded-lg bg-green-500/20 hover:bg-green-500/30 transition-colors">
                            <i data-lucide="message-circle" class="w-6 h-6 text-green-400"></i>
                            <span class="text-xs text-green-400">微信</span>
                        </button>
                        <button onclick="shareToWeibo()" class="flex flex-col items-center space-y-2 p-3 rounded-lg bg-red-500/20 hover:bg-red-500/30 transition-colors">
                            <i data-lucide="twitter" class="w-6 h-6 text-red-400"></i>
                            <span class="text-xs text-red-400">微博</span>
                        </button>
                        <button onclick="copyLink()" class="flex flex-col items-center space-y-2 p-3 rounded-lg bg-blue-500/20 hover:bg-blue-500/30 transition-colors">
                            <i data-lucide="link" class="w-6 h-6 text-blue-400"></i>
                            <span class="text-xs text-blue-400">复制链接</span>
                        </button>
                    </div>
                    <button onclick="closeShareModal()" class="w-full py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
                        取消
                    </button>
                </div>
            `;
            shareModal.onclick = (e) => {
                if (e.target === shareModal) closeShareModal();
            };
            document.body.appendChild(shareModal);
            window.currentShareModal = shareModal;
            
            // 初始化图标
            lucide.createIcons();
        }

        function closeShareModal() {
            if (window.currentShareModal) {
                document.body.removeChild(window.currentShareModal);
                window.currentShareModal = null;
            }
        }

        function shareToWeChat() {
            showToast('正在打开微信分享...');
            closeShareModal();
        }

        function shareToWeibo() {
            showToast('正在打开微博分享...');
            closeShareModal();
        }

        function copyLink() {
            const url = window.location.href;
            navigator.clipboard.writeText(url).then(() => {
                showToast('链接已复制到剪贴板');
            }).catch(() => {
                showToast('复制失败，请手动复制');
            });
            closeShareModal();
        }

        function reportInfo() {
            // 创建举报选项模态框
            const reportModal = document.createElement('div');
            reportModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            reportModal.innerHTML = `
                <div class="bg-slate-800 rounded-lg p-6 max-w-sm mx-4 border border-slate-600">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                        <i data-lucide="flag" class="w-5 h-5 text-amber-400"></i>
                        <span>举报原因</span>
                    </h3>
                    <div class="space-y-3 mb-4">
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="radio" name="reportReason" value="fraud" class="text-amber-400">
                            <span class="text-slate-300">虚假信息/诈骗</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="radio" name="reportReason" value="spam" class="text-amber-400">
                            <span class="text-slate-300">垃圾信息/广告</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="radio" name="reportReason" value="inappropriate" class="text-amber-400">
                            <span class="text-slate-300">不当内容</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="radio" name="reportReason" value="other" class="text-amber-400">
                            <span class="text-slate-300">其他原因</span>
                        </label>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="submitReport()" class="flex-1 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-lg transition-colors">
                            提交举报
                        </button>
                        <button onclick="closeReportModal()" class="flex-1 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            `;
            reportModal.onclick = (e) => {
                if (e.target === reportModal) closeReportModal();
            };
            document.body.appendChild(reportModal);
            window.currentReportModal = reportModal;
            
            // 初始化图标
            lucide.createIcons();
        }

        function closeReportModal() {
            if (window.currentReportModal) {
                document.body.removeChild(window.currentReportModal);
                window.currentReportModal = null;
            }
        }

        function submitReport() {
            const selectedReason = document.querySelector('input[name="reportReason"]:checked');
            if (selectedReason) {
                const reasons = {
                    'fraud': '虚假信息/诈骗',
                    'spam': '垃圾信息/广告', 
                    'inappropriate': '不当内容',
                    'other': '其他原因'
                };
                showToast(`举报已提交：${reasons[selectedReason.value]}`);
                closeReportModal();
            } else {
                showToast('请选择举报原因');
            }
        }

        function makeCall(phone) {
            showToast(`正在拨打 ${phone}`);
        }
        
        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 transform translate-x-full`;
            
            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    toast.classList.add('bg-green-500');
                    break;
                case 'error':
                    toast.classList.add('bg-red-500');
                    break;
                case 'warning':
                    toast.classList.add('bg-yellow-500');
                    break;
                default:
                    toast.classList.add('bg-blue-500');
            }
            
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 图片画廊相关变量和函数
        const imageGallery = [
            'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
        ];
        let currentImageIndex = 0;

        function openImageGallery(index) {
            currentImageIndex = index;
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageGallery[currentImageIndex];
            updateIndicators();
            modal.classList.remove('hidden');
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.add('hidden');
        }

        function previousImage() {
            currentImageIndex = (currentImageIndex - 1 + imageGallery.length) % imageGallery.length;
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageGallery[currentImageIndex];
            updateIndicators();
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % imageGallery.length;
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageGallery[currentImageIndex];
            updateIndicators();
        }

        function goToImage(index) {
            currentImageIndex = index;
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageGallery[currentImageIndex];
            updateIndicators();
        }

        function updateIndicators() {
            for (let i = 0; i < imageGallery.length; i++) {
                const indicator = document.getElementById(`indicator${i}`);
                if (i === currentImageIndex) {
                    indicator.classList.remove('bg-opacity-50');
                    indicator.classList.add('bg-opacity-100');
                } else {
                    indicator.classList.remove('bg-opacity-100');
                    indicator.classList.add('bg-opacity-50');
                }
            }
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (!modal.classList.contains('hidden')) {
                if (e.key === 'ArrowLeft') {
                    previousImage();
                } else if (e.key === 'ArrowRight') {
                    nextImage();
                } else if (e.key === 'Escape') {
                    closeImageModal();
                }
            }
        });

        function viewImage(src) {
            document.getElementById('modalImage').src = src;
            document.getElementById('imageModal').classList.remove('hidden');
        }

        // showToast函数已移至公共脚本 common.js
         }

         function viewRecommendation(id) {
             showToast(`正在查看推荐房源 ${id}`);
         }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            setTimeout(startCountdown, 100);
        });
    </script>
</body>
</html>