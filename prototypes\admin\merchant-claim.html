<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家认领管理 - 本地助手管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .status-pending { color: #f59e0b; background-color: rgba(245, 158, 11, 0.1); }
        .status-reviewing { color: #3b82f6; background-color: rgba(59, 130, 246, 0.1); }
        .status-approved { color: #10b981; background-color: rgba(16, 185, 129, 0.1); }
        .status-rejected { color: #dc2626; background-color: rgba(220, 38, 38, 0.1); }
        .priority-high { border-left: 4px solid #dc2626; }
        .priority-medium { border-left: 4px solid #f59e0b; }
        .priority-low { border-left: 4px solid #10b981; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- 顶部导航 -->
    <nav class="gradient-bg p-4 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i data-lucide="layout-dashboard" class="w-8 h-8"></i>
                <h1 class="text-xl font-bold">本地助手管理后台</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm">管理员</span>
                <i data-lucide="user" class="w-6 h-6"></i>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold mb-2">商家认领管理</h2>
            <p class="text-gray-400">管理商家认领申请，审核认证材料，处理现场核实</p>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">待审核申请</p>
                        <p class="text-3xl font-bold text-yellow-400">12</p>
                        <p class="text-yellow-400 text-sm mt-1">需要处理</p>
                    </div>
                    <div class="bg-yellow-600 p-3 rounded-lg">
                        <i data-lucide="clock" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">审核中</p>
                        <p class="text-3xl font-bold text-blue-400">8</p>
                        <p class="text-blue-400 text-sm mt-1">现场核实中</p>
                    </div>
                    <div class="bg-blue-600 p-3 rounded-lg">
                        <i data-lucide="search" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">本月通过</p>
                        <p class="text-3xl font-bold text-green-400">45</p>
                        <p class="text-green-400 text-sm mt-1">↗ +15.2%</p>
                    </div>
                    <div class="bg-green-600 p-3 rounded-lg">
                        <i data-lucide="circle-check" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 p-6 rounded-lg card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">通过率</p>
                        <p class="text-3xl font-bold">78.5%</p>
                        <p class="text-gray-400 text-sm mt-1">近30天</p>
                    </div>
                    <div class="bg-purple-600 p-3 rounded-lg">
                        <i data-lucide="trending-up" class="w-8 h-8"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">申请状态</label>
                    <select class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                        <option>全部状态</option>
                        <option>待审核</option>
                        <option>审核中</option>
                        <option>已通过</option>
                        <option>已拒绝</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">商家类型</label>
                    <select class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                        <option>全部类型</option>
                        <option>餐饮店铺</option>
                        <option>零售商店</option>
                        <option>服务机构</option>
                        <option>其他</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">申请时间</label>
                    <select class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                        <option>全部时间</option>
                        <option>今天</option>
                        <option>近7天</option>
                        <option>近30天</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">搜索</label>
                    <div class="relative">
                        <input type="text" placeholder="商家名称、联系人" 
                               class="w-full px-3 py-2 pl-10 bg-gray-700 rounded-lg">
                        <i data-lucide="search" class="w-5 h-5 absolute left-3 top-2.5 text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between items-center mt-4">
                <div class="flex space-x-2">
                    <button class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="filter" class="w-4 h-4 inline mr-2"></i>
                        筛选
                    </button>
                    <button class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        刷新
                    </button>
                </div>
                
                <div class="flex space-x-2">
                    <button onclick="batchApprove()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="check" class="w-4 h-4 inline mr-2"></i>
                        批量通过
                    </button>
                    <button onclick="exportData()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 认领申请列表 -->
        <div class="bg-gray-800 rounded-lg overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <h3 class="text-lg font-semibold">认领申请列表</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" class="rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                商家信息
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                申请人
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                申请时间
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                状态
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                优先级
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="claimList" class="bg-gray-800 divide-y divide-gray-700">
                        <!-- 认领申请数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 申请详情模态框 -->
    <div id="claimDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">认领申请详情</h3>
                        <button onclick="closeClaimDetail()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6" id="claimDetailContent">
                    <!-- 申请详情将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 现场核实模态框 -->
    <div id="verificationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-2xl w-full">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">安排现场核实</h3>
                        <button onclick="closeVerification()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">核实人员</label>
                            <select id="verificationStaff" class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                                <option>选择核实人员</option>
                                <option value="staff1">张三 - 区域经理</option>
                                <option value="staff2">李四 - 业务专员</option>
                                <option value="staff3">王五 - 高级审核员</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">预约时间</label>
                            <input type="datetime-local" id="verificationTime" 
                                   class="w-full px-3 py-2 bg-gray-700 rounded-lg">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">核实地址</label>
                            <textarea id="verificationAddress" rows="3" 
                                      placeholder="请输入详细的核实地址" 
                                      class="w-full px-3 py-2 bg-gray-700 rounded-lg"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">核实要点</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-2 rounded">
                                    <span class="text-sm">营业执照核实</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-2 rounded">
                                    <span class="text-sm">经营场所确认</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-2 rounded">
                                    <span class="text-sm">负责人身份验证</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2 rounded">
                                    <span class="text-sm">经营状况评估</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">备注说明</label>
                            <textarea id="verificationNotes" rows="3" 
                                      placeholder="请输入核实备注" 
                                      class="w-full px-3 py-2 bg-gray-700 rounded-lg"></textarea>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3 mt-6">
                        <button onclick="submitVerification()" class="flex-1 bg-blue-600 hover:bg-blue-700 py-2 rounded-lg transition-colors">
                            安排核实
                        </button>
                        <button onclick="closeVerification()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 rounded-lg transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核结果模态框 -->
    <div id="reviewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-lg w-full">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">审核决定</h3>
                        <button onclick="closeReview()" class="text-gray-400 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">审核结果</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="reviewResult" value="approve" class="mr-2">
                                    <span class="text-sm text-green-400">通过认领</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="reviewResult" value="reject" class="mr-2">
                                    <span class="text-sm text-red-400">拒绝认领</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="reviewResult" value="pending" class="mr-2">
                                    <span class="text-sm text-yellow-400">需要补充材料</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">审核意见</label>
                            <textarea id="reviewComment" rows="4" 
                                      placeholder="请输入审核意见和建议" 
                                      class="w-full px-3 py-2 bg-gray-700 rounded-lg"></textarea>
                        </div>
                        
                        <div id="rejectReasons" class="hidden">
                            <label class="block text-sm font-medium mb-2">拒绝原因</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2 rounded">
                                    <span class="text-sm">营业执照不符</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2 rounded">
                                    <span class="text-sm">经营地址不符</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2 rounded">
                                    <span class="text-sm">身份信息不符</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2 rounded">
                                    <span class="text-sm">已停业或不存在</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3 mt-6">
                        <button onclick="submitReview()" class="flex-1 bg-blue-600 hover:bg-blue-700 py-2 rounded-lg transition-colors">
                            提交审核
                        </button>
                        <button onclick="closeReview()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 rounded-lg transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 模拟认领申请数据
        const claimApplications = [
            {
                id: 'C001',
                merchantName: '老王烧烤店',
                merchantType: '餐饮店铺',
                applicantName: '王大明',
                applicantPhone: '138****5678',
                applyTime: '2024-01-20 09:30:00',
                status: 'pending',
                priority: 'high',
                address: '北京市朝阳区三里屯街道工体北路8号',
                businessLicense: '**********',
                documents: ['营业执照', '身份证', '经营许可证']
            },
            {
                id: 'C002',
                merchantName: '美丽人生美容院',
                merchantType: '服务机构',
                applicantName: '李美丽',
                applicantPhone: '139****1234',
                applyTime: '2024-01-20 10:15:00',
                status: 'reviewing',
                priority: 'medium',
                address: '上海市浦东新区陆家嘴金融区世纪大道100号',
                businessLicense: '**********',
                documents: ['营业执照', '身份证', '卫生许可证']
            },
            {
                id: 'C003',
                merchantName: '便民超市',
                merchantType: '零售商店',
                applicantName: '张小明',
                applicantPhone: '137****9876',
                applyTime: '2024-01-19 16:45:00',
                status: 'approved',
                priority: 'low',
                address: '广州市天河区珠江新城花城大道85号',
                businessLicense: '**********',
                documents: ['营业执照', '身份证']
            },
            {
                id: 'C004',
                merchantName: '快修手机店',
                merchantType: '服务机构',
                applicantName: '赵师傅',
                applicantPhone: '135****4567',
                applyTime: '2024-01-19 14:20:00',
                status: 'rejected',
                priority: 'medium',
                address: '深圳市南山区科技园南区深南大道9988号',
                businessLicense: '**********',
                documents: ['营业执照', '身份证']
            },
            {
                id: 'C005',
                merchantName: '阳光洗衣店',
                merchantType: '服务机构',
                applicantName: '孙阿姨',
                applicantPhone: '136****7890',
                applyTime: '2024-01-18 11:30:00',
                status: 'pending',
                priority: 'high',
                address: '杭州市西湖区文三路259号',
                businessLicense: '**********',
                documents: ['营业执照', '身份证', '环保许可证']
            }
        ];

        // 渲染认领申请列表
        function renderClaimList() {
            const tbody = document.getElementById('claimList');
            tbody.innerHTML = claimApplications.map(claim => `
                <tr class="hover:bg-gray-700 transition-colors ${getPriorityClass(claim.priority)}">
                    <td class="px-6 py-4">
                        <input type="checkbox" class="rounded">
                    </td>
                    <td class="px-6 py-4">
                        <div>
                            <p class="font-medium">${claim.merchantName}</p>
                            <p class="text-sm text-gray-400">${claim.merchantType}</p>
                            <p class="text-xs text-gray-500 mt-1">${claim.address}</p>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div>
                            <p class="font-medium">${claim.applicantName}</p>
                            <p class="text-sm text-gray-400">${claim.applicantPhone}</p>
                        </div>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-400">
                        ${claim.applyTime}
                    </td>
                    <td class="px-6 py-4">
                        <span class="px-2 py-1 text-xs rounded ${getStatusClass(claim.status)}">
                            ${getStatusText(claim.status)}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <span class="px-2 py-1 text-xs rounded ${
                            claim.priority === 'high' ? 'bg-red-600' :
                            claim.priority === 'medium' ? 'bg-yellow-600' : 'bg-green-600'
                        }">
                            ${claim.priority === 'high' ? '高' : claim.priority === 'medium' ? '中' : '低'}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex space-x-2">
                            <button onclick="viewClaimDetail('${claim.id}')" 
                                    class="text-blue-400 hover:text-blue-300 transition-colors" title="查看详情">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            ${claim.status === 'pending' ? `
                                <button onclick="arrangeVerification('${claim.id}')" 
                                        class="text-purple-400 hover:text-purple-300 transition-colors" title="安排核实">
                                    <i data-lucide="map-pin" class="w-4 h-4"></i>
                                </button>
                            ` : ''}
                            ${claim.status === 'reviewing' ? `
                                <button onclick="reviewClaim('${claim.id}')" 
                                        class="text-green-400 hover:text-green-300 transition-colors" title="审核决定">
                                    <i data-lucide="circle-check" class="w-4 h-4"></i>
                                </button>
                            ` : ''}
                            <button onclick="contactApplicant('${claim.id}')" 
                                    class="text-yellow-400 hover:text-yellow-300 transition-colors" title="联系申请人">
                                <i data-lucide="phone" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            lucide.createIcons();
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'status-pending';
                case 'reviewing': return 'status-reviewing';
                case 'approved': return 'status-approved';
                case 'rejected': return 'status-rejected';
                default: return 'status-pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'reviewing': return '审核中';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取优先级样式类
        function getPriorityClass(priority) {
            switch(priority) {
                case 'high': return 'priority-high';
                case 'medium': return 'priority-medium';
                case 'low': return 'priority-low';
                default: return '';
            }
        }

        // 查看申请详情
        function viewClaimDetail(claimId) {
            const claim = claimApplications.find(c => c.id === claimId);
            if (!claim) return;
            
            const content = `
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-3">商家信息</h4>
                            <div class="space-y-2 text-sm">
                                <p><span class="text-gray-400">商家名称：</span>${claim.merchantName}</p>
                                <p><span class="text-gray-400">商家类型：</span>${claim.merchantType}</p>
                                <p><span class="text-gray-400">营业执照号：</span>${claim.businessLicense}</p>
                                <p><span class="text-gray-400">经营地址：</span>${claim.address}</p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-3">申请人信息</h4>
                            <div class="space-y-2 text-sm">
                                <p><span class="text-gray-400">姓名：</span>${claim.applicantName}</p>
                                <p><span class="text-gray-400">联系电话：</span>${claim.applicantPhone}</p>
                                <p><span class="text-gray-400">申请时间：</span>${claim.applyTime}</p>
                                <p><span class="text-gray-400">当前状态：</span>
                                    <span class="px-2 py-1 text-xs rounded ${getStatusClass(claim.status)}">
                                        ${getStatusText(claim.status)}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-3">提交材料</h4>
                            <div class="space-y-2">
                                ${claim.documents.map(doc => `
                                    <div class="flex items-center justify-between p-2 bg-gray-600 rounded">
                                        <span class="text-sm">${doc}</span>
                                        <button class="text-blue-400 hover:text-blue-300 text-sm">
                                            <i data-lucide="download" class="w-4 h-4 inline mr-1"></i>
                                            下载
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-3">审核记录</h4>
                            <div class="space-y-3">
                                <div class="flex items-start space-x-3">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium">申请提交</p>
                                        <p class="text-xs text-gray-400">${claim.applyTime}</p>
                                        <p class="text-xs text-gray-500">申请人提交认领申请</p>
                                    </div>
                                </div>
                                
                                ${claim.status !== 'pending' ? `
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium">初步审核</p>
                                            <p class="text-xs text-gray-400">2024-01-20 10:30:00</p>
                                            <p class="text-xs text-gray-500">材料初步审核通过，安排现场核实</p>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex space-x-3 mt-6 pt-6 border-t border-gray-700">
                    ${claim.status === 'pending' ? `
                        <button onclick="arrangeVerification('${claim.id}')" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors">
                            <i data-lucide="map-pin" class="w-4 h-4 inline mr-2"></i>
                            安排现场核实
                        </button>
                    ` : ''}
                    ${claim.status === 'reviewing' ? `
                        <button onclick="reviewClaim('${claim.id}')" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
                            <i data-lucide="circle-check" class="w-4 h-4 inline mr-2"></i>
                            审核决定
                        </button>
                    ` : ''}
                    <button onclick="contactApplicant('${claim.id}')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="phone" class="w-4 h-4 inline mr-2"></i>
                        联系申请人
                    </button>
                    <button onclick="closeClaimDetail()" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors">
                        关闭
                    </button>
                </div>
            `;
            
            document.getElementById('claimDetailContent').innerHTML = content;
            document.getElementById('claimDetailModal').classList.remove('hidden');
            lucide.createIcons();
        }

        // 关闭申请详情
        function closeClaimDetail() {
            document.getElementById('claimDetailModal').classList.add('hidden');
        }

        // 安排现场核实
        function arrangeVerification(claimId) {
            closeClaimDetail();
            document.getElementById('verificationModal').classList.remove('hidden');
        }

        // 关闭现场核实
        function closeVerification() {
            document.getElementById('verificationModal').classList.add('hidden');
        }

        // 提交现场核实安排
        function submitVerification() {
            const staff = document.getElementById('verificationStaff').value;
            const time = document.getElementById('verificationTime').value;
            const address = document.getElementById('verificationAddress').value;
            
            if (!staff || !time || !address) {
                alert('请填写完整的核实信息');
                return;
            }
            
            alert('现场核实已安排成功！');
            closeVerification();
        }

        // 审核申请
        function reviewClaim(claimId) {
            closeClaimDetail();
            document.getElementById('reviewModal').classList.remove('hidden');
            
            // 监听审核结果选择
            document.querySelectorAll('input[name="reviewResult"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const rejectReasons = document.getElementById('rejectReasons');
                    if (this.value === 'reject') {
                        rejectReasons.classList.remove('hidden');
                    } else {
                        rejectReasons.classList.add('hidden');
                    }
                });
            });
        }

        // 关闭审核
        function closeReview() {
            document.getElementById('reviewModal').classList.add('hidden');
        }

        // 提交审核结果
        function submitReview() {
            const result = document.querySelector('input[name="reviewResult"]:checked');
            const comment = document.getElementById('reviewComment').value;
            
            if (!result) {
                alert('请选择审核结果');
                return;
            }
            
            if (!comment.trim()) {
                alert('请输入审核意见');
                return;
            }
            
            alert(`审核结果已提交：${result.value === 'approve' ? '通过' : result.value === 'reject' ? '拒绝' : '需要补充材料'}`);
            closeReview();
        }

        // 联系申请人
        function contactApplicant(claimId) {
            const claim = claimApplications.find(c => c.id === claimId);
            if (claim) {
                alert(`正在联系申请人：${claim.applicantName}\n电话：${claim.applicantPhone}`);
            }
        }

        // 批量通过
        function batchApprove() {
            alert('批量通过功能');
        }

        // 导出数据
        function exportData() {
            alert('导出数据功能');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderClaimList();
        });
    </script>
</body>
</html>