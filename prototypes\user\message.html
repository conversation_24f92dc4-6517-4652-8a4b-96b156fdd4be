<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息中心 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .message-item {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        .message-item:hover {
            background-color: rgba(99, 102, 241, 0.1);
            border-color: rgba(99, 102, 241, 0.3);
        }
        .unread {
            border-left: 3px solid #06b6d4;
        }
    </style>
</head>
<body class="bg-dark-bg text-white">
    <!-- 顶部导航 -->
    <div class="tech-gradient p-4">
        <div class="flex items-center justify-between">
            <h1 class="text-lg font-bold">消息中心</h1>
            <button class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 消息类型切换 -->
    <div class="bg-dark-card border-b border-gray-700">
        <div class="flex">
            <button class="flex-1 py-4 px-2 text-center border-b-2 border-tech-cyan text-tech-cyan font-medium message-type active" data-type="chat">
                聊天消息
            </button>
            <button class="flex-1 py-4 px-2 text-center border-b-2 border-transparent text-gray-400 hover:text-white transition-colors message-type" data-type="system">
                系统通知
            </button>
        </div>
    </div>

    <!-- 聊天消息列表 -->
    <div id="chatMessages" class="flex-1">
        <!-- 消息项 -->
        <div class="message-item unread bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <!-- 头像 -->
                <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-white font-bold">川</span>
                </div>
                
                <!-- 消息内容 -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">川味小厨</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-400">10:30</span>
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                        </div>
                    </div>
                    
                    <p class="text-sm text-gray-300 truncate">您好，我们店里新推出了特色川菜套餐，欢迎品尝！</p>
                </div>
            </div>
        </div>

        <div class="message-item bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-white font-bold">美</span>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">时尚美发店</h3>
                        <span class="text-xs text-gray-400">昨天</span>
                    </div>
                    
                    <p class="text-sm text-gray-300 truncate">感谢您的预约，明天下午2点为您安排了Tony老师</p>
                </div>
            </div>
        </div>

        <div class="message-item bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-white font-bold">李</span>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">李师傅（跑腿）</h3>
                        <span class="text-xs text-gray-400">昨天</span>
                    </div>
                    
                    <p class="text-sm text-gray-300 truncate">您的跑腿任务已完成，请确认收货</p>
                </div>
            </div>
        </div>

        <div class="message-item unread bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-white font-bold">王</span>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">王师傅（拼车）</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-400">2天前</span>
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                        </div>
                    </div>
                    
                    <p class="text-sm text-gray-300 truncate">明天早上8点出发去市区，还有2个座位</p>
                </div>
            </div>
        </div>

        <div class="message-item bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-white font-bold">租</span>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white truncate">房东张先生</h3>
                        <span class="text-xs text-gray-400">3天前</span>
                    </div>
                    
                    <p class="text-sm text-gray-300 truncate">房子还在出租，您可以来看房</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统通知列表 -->
    <div id="systemMessages" class="flex-1 hidden">
        <div class="message-item unread bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-tech-blue to-tech-purple rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white">实名认证成功</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-400">今天</span>
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                        </div>
                    </div>
                    
                    <p class="text-sm text-gray-300">恭喜您完成实名认证，现在可以发布更多类型的信息了</p>
                </div>
            </div>
        </div>

        <div class="message-item bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white">信息发布成功</h3>
                        <span class="text-xs text-gray-400">昨天</span>
                    </div>
                    
                    <p class="text-sm text-gray-300">您发布的"二手电脑出售"信息已通过审核并上线</p>
                </div>
            </div>
        </div>

        <div class="message-item bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 9a3.02 3.02 0 0 0-3 3c0 1.642 1.358 3 3 3 1.641 0 3-1.358 3-3 0-1.641-1.359-3-3-3z"/>
                        <path d="M12 5c-7.633 0-9.927 6.617-9.948 6.684L1.946 12l.105.316C2.073 12.383 4.367 19 12 19s9.927-6.617 9.948-6.684L22.054 12l-.105-.316C21.927 11.617 19.633 5 12 5zm0 12c-2.206 0-4-1.794-4-4s1.794-4 4-4 4 1.794 4 4-1.794 4-4 4z"/>
                    </svg>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white">版本更新提醒</h3>
                        <span class="text-xs text-gray-400">2天前</span>
                    </div>
                    
                    <p class="text-sm text-gray-300">本地助手v2.1版本已发布，新增语音搜索功能</p>
                </div>
            </div>
        </div>

        <div class="message-item bg-dark-card p-4 border-b border-gray-700">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-red-400 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                    </svg>
                </div>
                
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-semibold text-white">安全提醒</h3>
                        <span class="text-xs text-gray-400">3天前</span>
                    </div>
                    
                    <p class="text-sm text-gray-300">请注意保护个人隐私，不要在聊天中透露银行卡等敏感信息</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button onclick="window.location.href='shops.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <div class="relative">
                        <i data-lucide="message-circle" class="w-5 h-5"></i>
                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-xs text-white font-bold">3</span>
                        </div>
                    </div>
                    <span class="text-xs font-medium">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 消息类型切换功能
        document.querySelectorAll('.message-type').forEach(type => {
            type.addEventListener('click', function() {
                // 移除所有活跃状态
                document.querySelectorAll('.message-type').forEach(t => {
                    t.classList.remove('active', 'text-tech-cyan', 'border-tech-cyan');
                    t.classList.add('text-gray-400', 'border-transparent');
                });
                
                // 添加当前活跃状态
                this.classList.add('active', 'text-tech-cyan', 'border-tech-cyan');
                this.classList.remove('text-gray-400', 'border-transparent');
                
                // 切换消息列表显示
                const messageType = this.dataset.type;
                if (messageType === 'chat') {
                    document.getElementById('chatMessages').classList.remove('hidden');
                    document.getElementById('systemMessages').classList.add('hidden');
                } else {
                    document.getElementById('chatMessages').classList.add('hidden');
                    document.getElementById('systemMessages').classList.remove('hidden');
                }
            });
        });
        
        // 初始化图标
        lucide.createIcons();
    </script>
</body>
</html>