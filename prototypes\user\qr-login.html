<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫码服务功能 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .tech-gradient {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
        .qr-container {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }
        .scanning-animation {
            animation: scan 2s ease-in-out infinite;
        }
        @keyframes scan {
            0%, 100% { transform: translateY(-100%); opacity: 0; }
            50% { transform: translateY(100%); opacity: 1; }
        }
        .pulse-ring {
            animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        @keyframes pulse-ring {
            0% { transform: scale(0.8); opacity: 1; }
            80%, 100% { transform: scale(1.2); opacity: 0; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- 主容器 -->    
    <div class="w-full max-w-sm mx-auto">
        <!-- 顶部导航 -->
        <div class="flex items-center justify-between mb-8">
            <button onclick="goBack()" class="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors">
                <i data-lucide="arrow-left" class="w-5 h-5"></i>
                <span>返回</span>
            </button>
            <h1 class="text-xl font-bold text-white">扫码服务</h1>
            <div class="w-16"></div> <!-- 占位符保持居中 -->
        </div>

        <!-- 二维码扫描区域 -->
        <div class="bg-slate-800 rounded-2xl p-6 mb-6">
            <!-- 扫描框 -->
            <div class="relative mx-auto mb-6" style="width: 280px; height: 280px;">
                <!-- 二维码背景 -->
                <div class="qr-container w-full h-full rounded-xl flex items-center justify-center relative overflow-hidden">
                    <!-- 模拟二维码 -->
                    <div class="w-48 h-48 bg-white rounded-lg flex items-center justify-center">
                        <div class="grid grid-cols-8 gap-1 w-40 h-40">
                            <!-- 模拟二维码点阵 -->
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-white"></div>
                            
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-black rounded-sm"></div>
                            
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                            <div class="bg-white"></div>
                            <div class="bg-black rounded-sm"></div>
                        </div>
                    </div>
                    
                    <!-- 扫描线动画 -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="scanning-animation w-48 h-0.5 bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
                    </div>
                </div>
                
                <!-- 四个角的扫描框 -->
                <div class="absolute top-0 left-0 w-8 h-8 border-l-4 border-t-4 border-blue-500 rounded-tl-lg"></div>
                <div class="absolute top-0 right-0 w-8 h-8 border-r-4 border-t-4 border-blue-500 rounded-tr-lg"></div>
                <div class="absolute bottom-0 left-0 w-8 h-8 border-l-4 border-b-4 border-blue-500 rounded-bl-lg"></div>
                <div class="absolute bottom-0 right-0 w-8 h-8 border-r-4 border-b-4 border-blue-500 rounded-br-lg"></div>
            </div>
            
            <!-- 扫描状态 -->
            <div class="text-center mb-4">
                <div class="flex items-center justify-center space-x-2 mb-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full pulse-ring"></div>
                    <span class="text-blue-400 text-sm font-medium">正在扫描中...</span>
                </div>
                <p class="text-gray-400 text-sm">请将平台生成的服务二维码放入扫描框内</p>
            </div>
        </div>
        
        <!-- 功能按钮 -->
        <div class="space-y-3 mb-6">
            <!-- 相册选择 -->
            <button onclick="selectFromGallery()" class="w-full bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2">
                <i data-lucide="image" class="w-5 h-5"></i>
                <span>从相册选择服务二维码</span>
            </button>
            
            <!-- 手电筒 -->
            <button onclick="toggleFlashlight()" id="flashlightBtn" class="w-full bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2">
                <i data-lucide="flashlight" class="w-5 h-5"></i>
                <span>开启手电筒</span>
            </button>
        </div>
        
        <!-- 其他登录方式 -->
        <div class="text-center">
            <p class="text-gray-400 text-sm mb-3">其他登录方式</p>
            <div class="flex space-x-3">
                <button onclick="goToPhoneLogin()" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2">
                    <i data-lucide="smartphone" class="w-4 h-4"></i>
                    <span class="text-sm">手机登录</span>
                </button>
                <button onclick="goToWechatLogin()" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2">
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-sm">微信登录</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 成功提示弹窗 -->
    <div id="successModal" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 hidden z-50">
        <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-sm text-center">
            <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="check" class="w-8 h-8 text-green-500"></i>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">服务二维码识别成功</h3>
            <p class="text-gray-400 text-sm mb-4">正在为您登录...</p>
            <div class="flex items-center justify-center space-x-1">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>
    </div>

    <script>
        let flashlightOn = false;
        
        // 初始化图标
        lucide.createIcons();
        
        // 返回上一页
        function goBack() {
            window.history.back();
        }
        
        // 从相册选择服务二维码
        function selectFromGallery() {
            // 模拟文件选择
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 模拟扫描成功
                    setTimeout(() => {
                        showSuccessModal();
                    }, 1000);
                }
            };
            input.click();
        }
        
        // 切换手电筒
        function toggleFlashlight() {
            const btn = document.getElementById('flashlightBtn');
            const icon = btn.querySelector('i');
            const text = btn.querySelector('span');
            
            flashlightOn = !flashlightOn;
            
            if (flashlightOn) {
                btn.classList.remove('bg-slate-700', 'hover:bg-slate-600');
                btn.classList.add('bg-yellow-600', 'hover:bg-yellow-700');
                icon.setAttribute('data-lucide', 'flashlight-off');
                text.textContent = '关闭手电筒';
            } else {
                btn.classList.remove('bg-yellow-600', 'hover:bg-yellow-700');
                btn.classList.add('bg-slate-700', 'hover:bg-slate-600');
                icon.setAttribute('data-lucide', 'flashlight');
                text.textContent = '开启手电筒';
            }
            
            lucide.createIcons();
        }
        
        // 跳转到手机登录
        function goToPhoneLogin() {
            window.location.href = 'login.html';
        }
        
        // 跳转到微信登录
        function goToWechatLogin() {
            // 模拟微信登录
            alert('正在跳转到微信登录...');
        }
        
        // 显示成功弹窗
        function showSuccessModal() {
            document.getElementById('successModal').classList.remove('hidden');
            
            // 3秒后自动跳转
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 3000);
        }
        
        // 模拟服务二维码扫码成功（用于演示）
        setTimeout(() => {
            // 随机在5-10秒后触发扫码成功
            const delay = Math.random() * 5000 + 5000;
            setTimeout(() => {
                showSuccessModal();
            }, delay);
        }, 1000);
        
        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>