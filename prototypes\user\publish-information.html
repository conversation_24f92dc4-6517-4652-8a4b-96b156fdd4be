<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布信息 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .form-input {
            transition: all 0.3s ease;
        }
        .form-input:focus {
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        .upload-area {
            border: 2px dashed #475569;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #22c55e;
            background-color: rgba(34, 197, 94, 0.05);
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <div class="bg-gradient-to-r from-green-500 to-emerald-500 p-4">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white"></i>
            </button>
            <h1 class="text-xl font-semibold text-white">发布信息</h1>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-md mx-auto p-4 pb-24">
        <form id="infoForm" class="space-y-6">
            <!-- 信息类型选择 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">信息类型 *</label>
                <div class="grid grid-cols-4 gap-2">
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors active" data-value="job">招聘</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="rent">出租</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="seek-rent">求租</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="sell">出售</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="idle">闲置</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="buy">求购</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="seek-job">求职</button>
                    <button type="button" class="info-type-btn px-3 py-2 bg-slate-700 text-slate-300 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors" data-value="other">其他</button>
                </div>
                <input type="hidden" name="infoType" value="job">
            </div>

            <!-- 信息标题 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">信息标题 *</label>
                <input type="text" name="title" placeholder="请输入信息标题" 
                       class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-green-500 focus:outline-none">
            </div>

            <!-- 详细内容 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">详细内容 *</label>
                <textarea name="content" rows="6" placeholder="请详细描述信息内容" 
                          class="form-input w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-green-500 focus:outline-none resize-none"></textarea>
            </div>

            <!-- 联系方式 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">联系方式</label>
                <div class="space-y-3">
                    <!-- 联系电话 -->
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                            <i data-lucide="phone" class="w-5 h-5 text-slate-400"></i>
                        </div>
                        <input type="tel" name="phone" placeholder="请输入手机号" required
                               class="flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-green-500 focus:outline-none">
                    </div>
                    
                    <!-- 短信验证码 -->
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg">
                            <i data-lucide="message-square" class="w-5 h-5 text-slate-400"></i>
                        </div>
                        <input type="text" name="smsCode" placeholder="请输入短信验证码" required
                               class="flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-green-500 focus:outline-none">
                        <button type="button" id="sendSmsBtn" onclick="sendSmsCode()" 
                                class="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm whitespace-nowrap">
                            发送验证码
                        </button>
                    </div>
                    <p class="text-xs text-slate-400 ml-13">验证码将发送至您的手机，请注意查收</p>
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-2">相关位置</label>
                <div class="flex space-x-2">
                    <input type="text" name="location" placeholder="请输入相关地址或位置" 
                           class="form-input flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:border-green-500 focus:outline-none">
                    <button type="button" class="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i data-lucide="map-pin" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>

            <!-- 图片上传 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">相关图片</label>
                <div class="upload-area rounded-lg p-6 text-center cursor-pointer" onclick="document.getElementById('imageUpload').click()">
                    <i data-lucide="image-plus" class="w-12 h-12 text-slate-400 mx-auto mb-2"></i>
                    <p class="text-sm text-slate-400 mb-1">点击上传相关图片</p>
                    <p class="text-xs text-slate-500">支持JPG、PNG格式，最多6张</p>
                </div>
                <input type="file" id="imageUpload" multiple accept="image/*" class="hidden">
                
                <!-- 图片预览区域 -->
                <div id="imagePreview" class="grid grid-cols-3 gap-2 mt-3 hidden">
                    <!-- 动态生成图片预览 -->
                </div>
            </div>

            <!-- 金额和有效期设置 -->
            <div class="bg-slate-800 rounded-xl p-4">
                <label class="block text-sm font-medium text-slate-300 mb-3">金额和有效期</label>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm text-slate-300 mb-2">金额</label>
                        <input type="text" id="amount" name="amount" value="面议"
                               class="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" 
                               placeholder="请输入金额或面议">
                    </div>
                    <div>
                        <label class="block text-sm text-slate-300 mb-2">有效期（天）</label>
                        <input type="number" id="validityDays" name="validityDays" value="15" min="1" max="30"
                               class="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" 
                               placeholder="1-30天">
                    </div>
                </div>
                <p class="text-xs text-slate-400 mt-2">有效期最多30天，到期后信息将自动下架</p>
            </div>
        </form>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700 p-4">
        <!-- 发布按钮 -->
        <div class="max-w-md mx-auto flex space-x-3">
            <button type="button" class="flex-1 py-3 bg-slate-600 text-white rounded-lg font-medium hover:bg-slate-500 transition-colors">
                保存草稿
            </button>
            <button type="submit" form="infoForm" class="flex-1 py-3 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 transition-colors">
                发布信息
            </button>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
        
        // 定位按钮点击事件
        document.querySelector('button [data-lucide="map-pin"]').parentElement.addEventListener('click', function() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    // 这里可以调用地图API获取地址
                    document.querySelector('[name="location"]').value = `当前位置 (${lat.toFixed(6)}, ${lng.toFixed(6)})`;
                    alert('定位成功！');
                }, function(error) {
                    alert('定位失败，请手动输入地址');
                });
            } else {
                alert('您的浏览器不支持定位功能');
            }
        });
        
        // 信息类型按钮选择
        document.querySelectorAll('.info-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有按钮的激活状态
                document.querySelectorAll('.info-type-btn').forEach(b => {
                    b.classList.remove('active', 'bg-green-500', 'text-white');
                    b.classList.add('bg-slate-700', 'text-slate-300');
                });
                
                // 激活当前按钮
                this.classList.add('active', 'bg-green-500', 'text-white');
                this.classList.remove('bg-slate-700', 'text-slate-300');
                
                // 更新隐藏字段值
                document.querySelector('input[name="infoType"]').value = this.dataset.value;
            });
        });
        
        // 短信验证码发送
        let smsCountdown = 0;
        
        function sendSmsCode() {
            const phoneInput = document.querySelector('input[name="phone"]');
            const phone = phoneInput.value.trim();
            
            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone)) {
                alert('请输入正确的手机号码');
                phoneInput.focus();
                return;
            }
            
            // 开始倒计时
            smsCountdown = 60;
            const sendBtn = document.getElementById('sendSmsBtn');
            sendBtn.disabled = true;
            
            const countdown = setInterval(() => {
                sendBtn.textContent = `${smsCountdown}秒后重发`;
                smsCountdown--;
                
                if (smsCountdown < 0) {
                    clearInterval(countdown);
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送验证码';
                }
            }, 1000);
            
            // 模拟发送短信
            console.log('发送短信验证码到:', phone);
            alert('验证码已发送，请注意查收');
        }
        
        // 图片上传处理
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const files = e.target.files;
            const preview = document.getElementById('imagePreview');
            
            if (files.length > 0) {
                preview.classList.remove('hidden');
                preview.innerHTML = '';
                
                Array.from(files).slice(0, 6).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative aspect-square bg-slate-700 rounded-lg overflow-hidden';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-full object-cover">
                            <button type="button" class="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600" onclick="this.parentElement.remove()">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                });
            }
        });
        
        // 表单提交处理
        document.getElementById('infoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证必填字段
            const infoType = document.querySelector('input[name="infoType"]').value;
            const title = document.querySelector('[name="title"]').value.trim();
            const content = document.querySelector('[name="content"]').value.trim();
            const phone = document.querySelector('[name="phone"]').value.trim();
            const smsCode = document.querySelector('[name="smsCode"]').value.trim();
            
            if (!infoType) {
                alert('请选择信息类型');
                return;
            }
            
            if (!title) {
                alert('请输入信息标题');
                document.querySelector('[name="title"]').focus();
                return;
            }
            
            if (!content) {
                alert('请输入详细内容');
                document.querySelector('[name="content"]').focus();
                return;
            }
            
            if (!phone) {
                alert('请输入联系电话');
                document.querySelector('[name="phone"]').focus();
                return;
            }
            
            if (!smsCode) {
                alert('请输入短信验证码');
                document.querySelector('[name="smsCode"]').focus();
                return;
            }
            
            // 模拟发布流程
            const submitButton = document.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = '发布中...';
            
            // 模拟API提交
            setTimeout(() => {
                // 收集表单数据
                const formData = {
                    infoType: infoType,
                    title: title,
                    content: content,
                    phone: phone,
                    location: document.querySelector('[name="location"]').value,
                    amount: document.querySelector('[name="amount"]').value,
                    validityDays: document.querySelector('[name="validityDays"]').value,
                    timestamp: new Date().toISOString()
                };
                
                console.log('信息已提交:', formData);
                
                // 显示成功提示
                alert('信息发布成功！');
                
                // 跳转到信息列表页面
                window.location.href = 'information.html';
            }, 1500);
        });
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>