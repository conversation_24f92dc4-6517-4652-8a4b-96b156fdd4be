<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址管理 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .address-card {
            transition: all 0.3s ease;
        }
        .address-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .default-badge {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .map-container {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="history.back()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                <h1 class="text-lg font-semibold text-slate-200">地址管理</h1>
                <button class="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors" onclick="addNewAddress()">
                    <i data-lucide="plus" class="w-5 h-5 text-white"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 地址列表 -->
    <div class="max-w-md mx-auto px-4 py-6">
        <!-- 默认地址 -->
        <div class="address-card bg-slate-800 rounded-xl p-4 mb-4 border-2 border-green-500">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-2">
                    <i data-lucide="home" class="w-5 h-5 text-green-400"></i>
                    <span class="font-semibold text-slate-200">家</span>
                    <span class="default-badge text-white text-xs px-2 py-1 rounded-full font-medium">
                        默认
                    </span>
                </div>
                <div class="flex space-x-2">
                    <button class="text-slate-400 hover:text-blue-400 transition-colors" onclick="editAddress('home')">
                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                    </button>
                    <button class="text-slate-400 hover:text-red-400 transition-colors" onclick="deleteAddress('home')">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            
            <div class="space-y-2">
                <div class="flex items-center space-x-2">
                    <i data-lucide="user" class="w-4 h-4 text-slate-400"></i>
                    <span class="text-slate-300">张三</span>
                    <span class="text-slate-400">138****8888</span>
                </div>
                <div class="flex items-start space-x-2">
                    <i data-lucide="map-pin" class="w-4 h-4 text-slate-400 mt-0.5"></i>
                    <div class="flex-1">
                        <p class="text-slate-300">北京市朝阳区建国路88号</p>
                        <p class="text-sm text-slate-400">SOHO现代城A座1201室</p>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between mt-4 pt-3 border-t border-slate-700">
                <div class="flex items-center space-x-4">
                    <button class="flex items-center space-x-1 text-blue-400 hover:text-blue-300 transition-colors" onclick="viewOnMap('home')">
                        <i data-lucide="map" class="w-4 h-4"></i>
                        <span class="text-sm">地图查看</span>
                    </button>
                    <button class="flex items-center space-x-1 text-green-400 hover:text-green-300 transition-colors" onclick="shareAddress('home')">
                        <i data-lucide="share-2" class="w-4 h-4"></i>
                        <span class="text-sm">分享位置</span>
                    </button>
                </div>
                <div class="flex items-center space-x-1 text-slate-400">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span class="text-xs">最近使用</span>
                </div>
            </div>
        </div>

        <!-- 公司地址 -->
        <div class="address-card bg-slate-800 rounded-xl p-4 mb-4">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-2">
                    <i data-lucide="building" class="w-5 h-5 text-blue-400"></i>
                    <span class="font-semibold text-slate-200">公司</span>
                </div>
                <div class="flex space-x-2">
                    <button class="text-slate-400 hover:text-green-400 transition-colors" onclick="setDefault('company')">
                        <i data-lucide="star" class="w-4 h-4"></i>
                    </button>
                    <button class="text-slate-400 hover:text-blue-400 transition-colors" onclick="editAddress('company')">
                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                    </button>
                    <button class="text-slate-400 hover:text-red-400 transition-colors" onclick="deleteAddress('company')">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            
            <div class="space-y-2">
                <div class="flex items-center space-x-2">
                    <i data-lucide="user" class="w-4 h-4 text-slate-400"></i>
                    <span class="text-slate-300">张三</span>
                    <span class="text-slate-400">138****8888</span>
                </div>
                <div class="flex items-start space-x-2">
                    <i data-lucide="map-pin" class="w-4 h-4 text-slate-400 mt-0.5"></i>
                    <div class="flex-1">
                        <p class="text-slate-300">北京市海淀区中关村大街1号</p>
                        <p class="text-sm text-slate-400">海龙大厦15层1508室</p>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between mt-4 pt-3 border-t border-slate-700">
                <div class="flex items-center space-x-4">
                    <button class="flex items-center space-x-1 text-blue-400 hover:text-blue-300 transition-colors" onclick="viewOnMap('company')">
                        <i data-lucide="map" class="w-4 h-4"></i>
                        <span class="text-sm">地图查看</span>
                    </button>
                    <button class="flex items-center space-x-1 text-green-400 hover:text-green-300 transition-colors" onclick="shareAddress('company')">
                        <i data-lucide="share-2" class="w-4 h-4"></i>
                        <span class="text-sm">分享位置</span>
                    </button>
                </div>
                <div class="flex items-center space-x-1 text-slate-400">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span class="text-xs">3天前</span>
                </div>
            </div>
        </div>

        <!-- 朋友家 -->
        <div class="address-card bg-slate-800 rounded-xl p-4 mb-4">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-2">
                    <i data-lucide="users" class="w-5 h-5 text-purple-400"></i>
                    <span class="font-semibold text-slate-200">朋友家</span>
                </div>
                <div class="flex space-x-2">
                    <button class="text-slate-400 hover:text-green-400 transition-colors" onclick="setDefault('friend')">
                        <i data-lucide="star" class="w-4 h-4"></i>
                    </button>
                    <button class="text-slate-400 hover:text-blue-400 transition-colors" onclick="editAddress('friend')">
                        <i data-lucide="edit-2" class="w-4 h-4"></i>
                    </button>
                    <button class="text-slate-400 hover:text-red-400 transition-colors" onclick="deleteAddress('friend')">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            
            <div class="space-y-2">
                <div class="flex items-center space-x-2">
                    <i data-lucide="user" class="w-4 h-4 text-slate-400"></i>
                    <span class="text-slate-300">李四</span>
                    <span class="text-slate-400">139****9999</span>
                </div>
                <div class="flex items-start space-x-2">
                    <i data-lucide="map-pin" class="w-4 h-4 text-slate-400 mt-0.5"></i>
                    <div class="flex-1">
                        <p class="text-slate-300">北京市东城区王府井大街138号</p>
                        <p class="text-sm text-slate-400">新东安市场B座2201室</p>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between mt-4 pt-3 border-t border-slate-700">
                <div class="flex items-center space-x-4">
                    <button class="flex items-center space-x-1 text-blue-400 hover:text-blue-300 transition-colors" onclick="viewOnMap('friend')">
                        <i data-lucide="map" class="w-4 h-4"></i>
                        <span class="text-sm">地图查看</span>
                    </button>
                    <button class="flex items-center space-x-1 text-green-400 hover:text-green-300 transition-colors" onclick="shareAddress('friend')">
                        <i data-lucide="share-2" class="w-4 h-4"></i>
                        <span class="text-sm">分享位置</span>
                    </button>
                </div>
                <div class="flex items-center space-x-1 text-slate-400">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span class="text-xs">1周前</span>
                </div>
            </div>
        </div>

        <!-- 添加新地址按钮 -->
        <div class="bg-slate-800 rounded-xl p-6 text-center">
            <div class="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="plus" class="w-8 h-8 text-slate-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-300 mb-2">添加新地址</h3>
            <p class="text-slate-500 mb-4">快速添加常用地址，方便下次使用</p>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors" onclick="addNewAddress()">
                立即添加
            </button>
        </div>

        <!-- 快捷操作 -->
        <div class="mt-6 grid grid-cols-2 gap-4">
            <button class="bg-slate-800 hover:bg-slate-700 rounded-xl p-4 text-center transition-colors" onclick="importFromContacts()">
                <i data-lucide="contact" class="w-6 h-6 text-blue-400 mx-auto mb-2"></i>
                <div class="text-sm font-medium text-slate-300">从通讯录导入</div>
                <div class="text-xs text-slate-500 mt-1">快速添加联系人地址</div>
            </button>
            <button class="bg-slate-800 hover:bg-slate-700 rounded-xl p-4 text-center transition-colors" onclick="getCurrentLocation()">
                <i data-lucide="crosshair" class="w-6 h-6 text-green-400 mx-auto mb-2"></i>
                <div class="text-sm font-medium text-slate-300">使用当前位置</div>
                <div class="text-xs text-slate-500 mt-1">自动获取当前位置</div>
            </button>
        </div>
    </div>

    <!-- 地址编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-end justify-center min-h-screen">
            <div class="bg-slate-800 rounded-t-2xl w-full max-w-md p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-lg font-semibold text-slate-200">编辑地址</h2>
                    <button onclick="closeEditModal()" class="text-slate-400 hover:text-slate-300">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">地址标签</label>
                        <div class="flex space-x-2">
                            <button type="button" class="tag-btn bg-blue-600 text-white px-3 py-2 rounded-lg text-sm" data-tag="家">
                                <i data-lucide="home" class="w-4 h-4 inline mr-1"></i>家
                            </button>
                            <button type="button" class="tag-btn bg-slate-600 text-slate-300 px-3 py-2 rounded-lg text-sm hover:bg-slate-500" data-tag="公司">
                                <i data-lucide="building" class="w-4 h-4 inline mr-1"></i>公司
                            </button>
                            <button type="button" class="tag-btn bg-slate-600 text-slate-300 px-3 py-2 rounded-lg text-sm hover:bg-slate-500" data-tag="学校">
                                <i data-lucide="graduation-cap" class="w-4 h-4 inline mr-1"></i>学校
                            </button>
                            <button type="button" class="tag-btn bg-slate-600 text-slate-300 px-3 py-2 rounded-lg text-sm hover:bg-slate-500" data-tag="其他">
                                <i data-lucide="map-pin" class="w-4 h-4 inline mr-1"></i>其他
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">联系人</label>
                            <input type="text" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none" placeholder="请输入姓名" value="张三">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">手机号</label>
                            <input type="tel" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none" placeholder="请输入手机号" value="13888888888">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">详细地址</label>
                        <div class="relative">
                            <input type="text" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 pr-10 text-slate-200 focus:border-blue-500 focus:outline-none" placeholder="请输入详细地址" value="北京市朝阳区建国路88号">
                            <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300" onclick="selectFromMap()">
                                <i data-lucide="map" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">门牌号/楼层</label>
                        <input type="text" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-slate-200 focus:border-blue-500 focus:outline-none" placeholder="如：A座1201室" value="SOHO现代城A座1201室">
                    </div>

                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="setDefault" class="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500" checked>
                        <label for="setDefault" class="text-sm text-slate-300">设为默认地址</label>
                    </div>

                    <div class="flex space-x-3 pt-4">
                        <button type="button" class="flex-1 bg-slate-600 hover:bg-slate-500 text-slate-300 py-3 rounded-lg transition-colors" onclick="closeEditModal()">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg transition-colors">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 添加新地址
        function addNewAddress() {
            document.getElementById('editModal').classList.remove('hidden');
        }

        // 编辑地址
        function editAddress(addressId) {
            document.getElementById('editModal').classList.remove('hidden');
            // 这里可以根据addressId加载对应的地址信息
        }

        // 关闭编辑弹窗
        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
        }

        // 删除地址
        function deleteAddress(addressId) {
            if (confirm('确定要删除这个地址吗？')) {
                alert(`地址 ${addressId} 已删除`);
            }
        }

        // 设为默认
        function setDefault(addressId) {
            alert(`已将 ${addressId} 设为默认地址`);
        }

        // 地图查看
        function viewOnMap(addressId) {
            alert(`正在地图中查看 ${addressId} 地址`);
        }

        // 分享地址
        function shareAddress(addressId) {
            alert(`正在分享 ${addressId} 地址`);
        }

        // 从通讯录导入
        function importFromContacts() {
            alert('正在打开通讯录...');
        }

        // 获取当前位置
        function getCurrentLocation() {
            alert('正在获取当前位置...');
        }

        // 从地图选择
        function selectFromMap() {
            alert('正在打开地图选择...');
        }

        // 标签选择
        document.querySelectorAll('.tag-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.tag-btn').forEach(b => {
                    b.classList.remove('bg-blue-600', 'text-white');
                    b.classList.add('bg-slate-600', 'text-slate-300');
                });
                btn.classList.remove('bg-slate-600', 'text-slate-300');
                btn.classList.add('bg-blue-600', 'text-white');
            });
        });

        // 点击弹窗外部关闭
        document.getElementById('editModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>