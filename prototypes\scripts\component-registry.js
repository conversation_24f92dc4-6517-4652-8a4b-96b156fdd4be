/**
 * 组件注册系统 - Component Registry
 * 提供统一的组件注册、初始化和生命周期管理功能
 */

// 组件注册表
class ComponentRegistry {
    constructor() {
        // 已注册的组件类型
        this.componentTypes = new Map();
        
        // 页面上的组件实例
        this.instances = new Map();
        
        // 组件初始化钩子
        this.hooks = {
            beforeInit: [],     // 组件初始化前
            afterInit: [],      // 组件初始化后
            beforeDestroy: [],  // 组件销毁前
            afterDestroy: []    // 组件销毁后
        };
        
        // 是否已初始化
        this.initialized = false;
        
        // 日志系统
        this.logger = window.logger || console;
    }
    
    /**
     * 注册组件类型
     * @param {string} name - 组件名称
     * @param {class|function} componentClass - 组件构造函数或类
     * @param {Object} options - 组件配置选项
     */
    register(name, componentClass, options = {}) {
        if (this.componentTypes.has(name)) {
            this.logger.warn(`组件类型 "${name}" 已被注册，将被覆盖`);
        }
        
        this.componentTypes.set(name, {
            class: componentClass,
            options: {
                // 默认选项
                autoInit: true,         // 自动初始化
                selector: `[data-component="${name}"]`,  // 默认选择器
                multiple: true,         // 是否允许多个实例
                ...options
            }
        });
        
        this.logger.debug(`组件类型 "${name}" 已注册`, options);
        
        // 如果已初始化，则立即初始化该组件
        if (this.initialized && options.autoInit !== false) {
            this.initComponentType(name);
        }
        
        return this;
    }
    
    /**
     * 添加生命周期钩子
     * @param {string} hook - 钩子名称
     * @param {Function} callback - 回调函数
     */
    addHook(hook, callback) {
        if (this.hooks[hook]) {
            this.hooks[hook].push(callback);
        }
        return this;
    }
    
    /**
     * 触发生命周期钩子
     * @param {string} hook - 钩子名称
     * @param {Object} data - 钩子数据
     */
    triggerHook(hook, data) {
        if (this.hooks[hook]) {
            for (const callback of this.hooks[hook]) {
                try {
                    callback(data);
                } catch (error) {
                    this.logger.error(`钩子 "${hook}" 执行出错`, error);
                }
            }
        }
    }
    
    /**
     * 初始化所有组件
     */
    init() {
        if (this.initialized) {
            this.logger.warn('组件系统已经初始化');
            return this;
        }
        
        this.logger.debug('开始初始化组件系统');
        
        // 初始化所有注册的组件类型
        for (const [name, config] of this.componentTypes) {
            if (config.options.autoInit !== false) {
                this.initComponentType(name);
            }
        }
        
        this.initialized = true;
        this.logger.info('组件系统初始化完成');
        
        // 设置组件 DOM 变化监视器
        this.setupMutationObserver();
        
        return this;
    }
    
    /**
     * 初始化特定类型的组件
     * @param {string} componentName - 组件名称
     */
    initComponentType(componentName) {
        const config = this.componentTypes.get(componentName);
        if (!config) {
            this.logger.error(`未注册的组件类型: "${componentName}"`);
            return this;
        }
        
        const { class: ComponentClass, options } = config;
        const { selector, multiple } = options;
        
        this.logger.debug(`初始化组件类型: "${componentName}"`, { selector });
        
        const elements = document.querySelectorAll(selector);
        
        if (elements.length === 0) {
            this.logger.debug(`未找到组件 "${componentName}" 的元素`, { selector });
            return this;
        }
        
        if (!multiple && elements.length > 1) {
            this.logger.warn(`组件类型 "${componentName}" 不允许多个实例，但找到了 ${elements.length} 个元素`);
        }
        
        for (let i = 0; i < (multiple ? elements.length : 1); i++) {
            const element = elements[i];
            
            // 检查元素是否已有组件实例
            const instanceId = element.dataset.componentId;
            if (instanceId && this.instances.has(instanceId)) {
                this.logger.debug(`元素已有组件实例，跳过`, { element, instanceId });
                continue;
            }
            
            // 创建唯一ID - 使用现代方法替代已弃用的 substr()
            const randomPart = Math.random().toString(36).substring(2, 11);
            const id = `${componentName}_${Date.now()}_${randomPart}`;
            element.dataset.componentId = id;
            
            // 准备组件配置
            const instanceOptions = {};
            
            // 解析元素上的数据属性到配置对象
            for (const [key, value] of Object.entries(element.dataset)) {
                if (key.startsWith('config')) {
                    const optionName = key.replace('config', '').toLowerCase();
                    // 尝试解析 JSON
                    try {
                        instanceOptions[optionName] = JSON.parse(value);
                    } catch (e) {
                        instanceOptions[optionName] = value;
                    }
                }
            }
            
            // 创建组件实例前的钩子
            this.triggerHook('beforeInit', {
                componentName,
                element,
                options: instanceOptions
            });
            
            try {
                // 创建组件实例
                const instance = new ComponentClass(element, instanceOptions);
                
                // 保存实例
                this.instances.set(id, {
                    name: componentName,
                    element,
                    instance,
                    options: instanceOptions
                });
                
                // 创建组件实例后的钩子
                this.triggerHook('afterInit', {
                    componentName,
                    element,
                    instance,
                    options: instanceOptions
                });
                
                this.logger.debug(`组件实例创建成功`, {
                    componentName,
                    instanceId: id,
                    options: instanceOptions
                });
                
            } catch (error) {
                this.logger.error(`组件实例创建失败: ${componentName}`, error, {
                    element,
                    options: instanceOptions
                });
            }
        }
        
        return this;
    }
    
    /**
     * 销毁组件实例
     * @param {string} instanceId - 组件实例ID
     */
    destroyInstance(instanceId) {
        const instanceData = this.instances.get(instanceId);
        if (!instanceData) {
            this.logger.warn(`未找到组件实例: ${instanceId}`);
            return false;
        }
        
        const { name, element, instance } = instanceData;
        
        // 销毁前钩子
        this.triggerHook('beforeDestroy', {
            componentName: name,
            element,
            instance,
            instanceId
        });
        
        try {
            // 尝试调用组件的销毁方法
            if (instance && typeof instance.destroy === 'function') {
                instance.destroy();
            }
            
            // 删除元素上的实例ID
            if (element) {
                delete element.dataset.componentId;
            }
            
            // 从注册表中删除
            this.instances.delete(instanceId);
            
            // 销毁后钩子
            this.triggerHook('afterDestroy', {
                componentName: name,
                element,
                instanceId
            });
            
            this.logger.debug(`组件实例已销毁`, {
                componentName: name,
                instanceId
            });
            
            return true;
        } catch (error) {
            this.logger.error(`组件实例销毁失败: ${instanceId}`, error);
            return false;
        }
    }
    
    /**
     * 获取组件实例
     * @param {string|Element} idOrElement - 组件实例ID或元素
     * @returns {Object} 组件实例
     */
    getInstance(idOrElement) {
        let instanceId = idOrElement;
        
        // 如果提供的是元素，查找其实例ID
        if (idOrElement instanceof Element) {
            instanceId = idOrElement.dataset.componentId;
            if (!instanceId) return null;
        }
        
        const instanceData = this.instances.get(instanceId);
        return instanceData ? instanceData.instance : null;
    }
    
    /**
     * 查找元素上的组件实例
     * @param {Element} element - DOM元素
     * @param {string} componentName - 可选的组件名称过滤
     * @returns {Object} 组件实例
     */
    findInstance(element, componentName) {
        // 首先检查元素自身
        let instanceId = element.dataset.componentId;
        if (instanceId) {
            const instanceData = this.instances.get(instanceId);
            if (instanceData && (!componentName || instanceData.name === componentName)) {
                return instanceData.instance;
            }
        }
        
        // 向上查找父元素
        let parent = element.parentElement;
        while (parent) {
            instanceId = parent.dataset.componentId;
            if (instanceId) {
                const instanceData = this.instances.get(instanceId);
                if (instanceData && (!componentName || instanceData.name === componentName)) {
                    return instanceData.instance;
                }
            }
            parent = parent.parentElement;
        }
        
        return null;
    }
    
    /**
     * 设置DOM变化监视器
     * 用于自动初始化动态添加的组件
     */
    setupMutationObserver() {
        if (!window.MutationObserver) return;
        
        const observer = new MutationObserver((mutations) => {
            let shouldScan = false;
            
            for (const mutation of mutations) {
                // 节点添加
                if (mutation.addedNodes.length) {
                    shouldScan = true;
                    break;
                }
                
                // 属性变化，检查是否为组件相关属性
                if (mutation.type === 'attributes' && 
                    (mutation.attributeName === 'data-component' || 
                     mutation.attributeName?.startsWith('data-config'))) {
                    shouldScan = true;
                    break;
                }
            }
            
            if (shouldScan) {
                // 为避免频繁扫描，使用防抖
                this.debouncedScanNewComponents();
            }
        });
        
        // 配置观察选项
        observer.observe(document.body, {
            childList: true,     // 观察直接子节点
            subtree: true,       // 观察所有后代节点
            attributes: true,    // 观察属性变化
            attributeFilter: ['data-component', 'data-config'] // 仅关注这些属性
        });
        
        // 创建防抖函数 - 检查Utils是否可用
        if (typeof Utils !== 'undefined' && Utils.debounce) {
            this.debouncedScanNewComponents = Utils.debounce(() => {
                this.scanNewComponents();
            }, 200);
        } else {
            // 降级方案：简单的防抖实现
            let timeout;
            this.debouncedScanNewComponents = () => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.scanNewComponents();
                }, 200);
            };
        }
    }
    
    /**
     * 扫描新添加的组件元素
     */
    scanNewComponents() {
        this.logger.debug('扫描新组件元素');
        
        for (const [name, config] of this.componentTypes) {
            if (config.options.autoInit !== false) {
                this.initComponentType(name);
            }
        }
    }
    
    /**
     * 重新扫描并初始化页面上的组件
     */
    refresh() {
        this.logger.debug('刷新组件系统');
        this.scanNewComponents();
        return this;
    }
    
    /**
     * 销毁所有组件实例
     */
    destroyAll() {
        this.logger.debug('销毁所有组件实例');
        
        for (const instanceId of this.instances.keys()) {
            this.destroyInstance(instanceId);
        }
        
        return this;
    }
    
    /**
     * 重置组件系统
     */
    reset() {
        this.destroyAll();
        this.componentTypes.clear();
        this.initialized = false;
        
        this.logger.debug('组件系统已重置');
        
        return this;
    }
}

// 创建全局组件注册表实例
const componentRegistry = new ComponentRegistry();

// 初始化组件方法 - 用于在页面准备好时初始化
function initComponents() {
    componentRegistry.init();
}

// 等待DOM加载完成
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initComponents);
} else {
    initComponents();
}

// 导出全局变量
window.componentRegistry = componentRegistry;
window.initComponents = initComponents;
