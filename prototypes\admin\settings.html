<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
        }
        .setting-card {
            transition: all 0.2s ease;
        }
        .setting-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #374151;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #0066ff;
        }
        input:checked + .slider:before {
            transform: translateX(20px);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech">
    <!-- 顶部导航栏 -->
    <nav class="bg-dark-card border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 tech-gradient rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">设</span>
                </div>
                <h1 class="text-xl font-bold">系统设置</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button class="px-4 py-2 bg-tech-blue rounded-lg text-white text-sm hover:bg-opacity-80 transition-colors">
                    保存设置
                </button>
                <div class="w-8 h-8 bg-tech-purple rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">管</span>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- 侧边导航 -->
        <div class="w-64 bg-dark-card border-r border-gray-700 min-h-screen">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="#basic" class="flex items-center px-4 py-3 text-white bg-tech-blue bg-opacity-20 rounded-lg">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        基础设置
                    </a>
                    <a href="#security" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        安全设置
                    </a>
                    <a href="#notification" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                        </svg>
                        通知设置
                    </a>
                    <a href="#content" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        内容管理
                    </a>
                    <a href="#system" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z"></path>
                        </svg>
                        系统配置
                    </a>
                </nav>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 p-6">
            <!-- 基础设置 -->
            <div id="basic" class="space-y-6">
                <h2 class="text-2xl font-bold text-white mb-6">基础设置</h2>
                
                <!-- 平台信息 -->
                <div class="setting-card bg-dark-card rounded-xl p-6 border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">平台信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">平台名称</label>
                            <input type="text" value="本地助手" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">平台版本</label>
                            <input type="text" value="v1.0.0" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-300 mb-2">平台描述</label>
                            <textarea rows="3" 
                                      class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple"
                                      placeholder="请输入平台描述信息">连接本地生活，分享便民信息的微信小程序平台</textarea>
                        </div>
                    </div>
                </div>

                <!-- 功能开关 -->
                <div class="setting-card bg-dark-card rounded-xl p-6 border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">功能开关</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-white font-medium">用户注册</h4>
                                <p class="text-sm text-gray-400">允许新用户注册账号</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-white font-medium">内容发布</h4>
                                <p class="text-sm text-gray-400">允许用户发布信息内容</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-white font-medium">评论功能</h4>
                                <p class="text-sm text-gray-400">允许用户评论和互动</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-white font-medium">举报功能</h4>
                                <p class="text-sm text-gray-400">允许用户举报不当内容</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 审核设置 -->
                <div class="setting-card bg-dark-card rounded-xl p-6 border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">审核设置</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-white font-medium">内容预审核</h4>
                                <p class="text-sm text-gray-400">新发布内容需要审核后才能显示</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">自动审核关键词</label>
                                <textarea rows="3" 
                                          class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple"
                                          placeholder="输入敏感词，用逗号分隔">违法,欺诈,色情,赌博,暴力</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">审核超时时间（小时）</label>
                                <input type="number" value="24" min="1" max="168"
                                       class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 限制设置 -->
                <div class="setting-card bg-dark-card rounded-xl p-6 border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">使用限制</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">每日发布限制</label>
                            <input type="number" value="10" min="1" max="100"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">图片上传限制（MB）</label>
                            <input type="number" value="5" min="1" max="20"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">内容字数限制</label>
                            <input type="number" value="500" min="50" max="2000"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                    </div>
                </div>

                <!-- 联系方式 -->
                <div class="setting-card bg-dark-card rounded-xl p-6 border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">联系方式</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">客服电话</label>
                            <input type="tel" value="************" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">客服邮箱</label>
                            <input type="email" value="<EMAIL>" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">客服微信</label>
                            <input type="text" value="localhelper_service" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">工作时间</label>
                            <input type="text" value="9:00-18:00" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                    </div>
                </div>

                <!-- 法律信息 -->
                <div class="setting-card bg-dark-card rounded-xl p-6 border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">法律信息</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">用户协议链接</label>
                            <input type="url" value="https://localhelper.com/terms" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">隐私政策链接</label>
                            <input type="url" value="https://localhelper.com/privacy" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">备案信息</label>
                            <input type="text" value="京ICP备12345678号-1" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-tech-purple">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 保存确认弹窗 -->
    <div id="saveModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-dark-card rounded-xl p-6 max-w-md w-full mx-4 border border-gray-700">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-white">保存设置</h3>
            </div>
            <p class="text-gray-300 mb-6">确认保存当前的系统设置？部分设置可能需要重启系统才能生效。</p>
            <div class="flex space-x-4">
                <button onclick="closeModal()" class="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors">
                    取消
                </button>
                <button onclick="saveSettings()" class="flex-1 px-4 py-2 bg-tech-blue text-white rounded-lg hover:bg-opacity-80 transition-colors">
                    确认保存
                </button>
            </div>
        </div>
    </div>

    <script>
        // 侧边导航切换
        document.querySelectorAll('nav a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活动状态
                document.querySelectorAll('nav a').forEach(l => {
                    l.classList.remove('bg-tech-blue', 'bg-opacity-20', 'text-white');
                    l.classList.add('text-gray-300');
                });
                
                // 添加当前活动状态
                this.classList.add('bg-tech-blue', 'bg-opacity-20', 'text-white');
                this.classList.remove('text-gray-300');
                
                // 这里可以添加切换内容区域的逻辑
                const target = this.getAttribute('href').substring(1);
                console.log('切换到:', target);
            });
        });

        // 保存设置
        document.querySelector('nav button').addEventListener('click', function() {
            document.getElementById('saveModal').classList.remove('hidden');
            document.getElementById('saveModal').classList.add('flex');
        });

        function closeModal() {
            document.getElementById('saveModal').classList.add('hidden');
            document.getElementById('saveModal').classList.remove('flex');
        }

        function saveSettings() {
            // 模拟保存过程
            alert('设置已保存 - 实际开发中将更新数据库配置');
            closeModal();
        }

        // 开关切换效果
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            toggle.addEventListener('change', function() {
                console.log('功能开关状态:', this.checked);
            });
        });

        // 表单验证
        document.querySelectorAll('input, textarea').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('border-red-500');
                } else {
                    this.classList.remove('border-red-500');
                }
            });
        });
    </script>
</body>
</html>