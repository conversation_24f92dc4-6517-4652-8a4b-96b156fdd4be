<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
        }
        .floating-animation {
            /* 移除浮动动画以提升性能 */
        }
    </style>
</head>
<body class="bg-dark-bg text-white min-h-screen flex items-center justify-center">
    <div class="w-full max-w-sm mx-auto p-6">
        <!-- Logo区域 -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 mx-auto mb-4 tech-gradient rounded-2xl flex items-center justify-center tech-glow floating-animation">
                <span class="text-white font-bold text-2xl">本</span>
            </div>
            <h1 class="text-2xl font-bold mb-2">本地助手</h1>
            <p class="text-gray-400 text-sm">连接本地，服务生活</p>
        </div>

        <!-- 登录表单 -->
        <div class="bg-dark-card rounded-2xl p-6 tech-glow">
            <!-- 微信登录按钮 -->
            <button class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center space-x-3" data-action="wechatLogin">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18z"/>
                    <path d="M15.308 9.53c-3.506 0-6.349 2.749-6.349 6.126 0 1.357.53 2.603 1.402 3.614a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098c.543.156 1.122.235 1.715.235 3.506 0 6.349-2.749 6.349-6.126S18.814 9.53 15.308 9.53zm-2.372 3.675c-.428 0-.775-.356-.775-.794 0-.438.347-.794.775-.794s.775.356.775.794c0 .438-.347.794-.775.794zm4.744 0c-.428 0-.775-.356-.775-.794 0-.438.347-.794.775-.794s.775.356.775.794c0 .438-.347.794-.775.794z"/>
                </svg>
                <span>微信一键登录</span>
            </button>
            
            <!-- 微信授权说明 -->
            <p class="text-xs text-gray-400 mt-2 text-center">授权后可获取您的微信昵称和头像</p>

            <!-- 分割线 -->
            <div class="flex items-center my-6">
                <div class="flex-1 border-t border-gray-600"></div>
                <span class="px-4 text-gray-400 text-sm">或</span>
                <div class="flex-1 border-t border-gray-600"></div>
            </div>

            <!-- 手机号登录 -->
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">手机号</label>
                    <input type="tel" placeholder="请输入手机号" class="w-full bg-gray-800 border border-gray-600 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-tech-blue focus:ring-2 focus:ring-tech-blue focus:ring-opacity-50 transition-all">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">验证码</label>
                    <div class="flex space-x-3">
                        <input type="text" placeholder="请输入验证码" class="flex-1 bg-gray-800 border border-gray-600 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-tech-blue focus:ring-2 focus:ring-tech-blue focus:ring-opacity-50 transition-all" id="verifyCode">
                        <button class="px-6 py-3 bg-tech-blue hover:bg-blue-700 text-white rounded-xl font-medium transition-colors whitespace-nowrap" id="getCodeBtn" data-action="getVerifyCode">发送验证码</button>
                    </div>
                </div>

                <!-- 记住我选项 -->
                <div class="flex items-center justify-between mb-4">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="rememberMe" class="w-4 h-4 text-tech-blue bg-gray-800 border-gray-600 rounded focus:ring-tech-blue focus:ring-2">
                        <span class="text-sm text-gray-300">记住我</span>
                    </label>
                    <a href="#" class="text-sm text-tech-cyan hover:underline" data-action="showForgotPassword">忘记密码？</a>
                </div>

                <button class="w-full tech-gradient text-white font-semibold py-4 px-6 rounded-xl transition-colors tech-glow" data-action="phoneLogin">
                    登录
                </button>
                
                <!-- 扫码服务功能 -->
                <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center space-x-3 mt-3" data-action="openQRScanner">
                    <i data-lucide="qr-code" class="w-5 h-5"></i>
                    <span>扫码服务</span>
                </button>
                
                <!-- 错误提示 -->
                <div id="errorMessage" class="hidden mt-3 p-3 bg-red-900/50 border border-red-500 rounded-xl text-red-300 text-sm text-center">
                    <span id="errorText"></span>
                </div>
            </div>

            <!-- 协议条款 -->
            <div class="mt-6 text-center">
                <p class="text-xs text-gray-400">
                    登录即表示同意
                    <a href="#" class="text-tech-cyan hover:underline">用户协议</a>
                    和
                    <a href="#" class="text-tech-cyan hover:underline">隐私政策</a>
                </p>
            </div>
        </div>

        <!-- 底部提示 -->
        <div class="text-center mt-6">
            <p class="text-gray-400 text-sm">首次登录将自动注册账号</p>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 注册所有按钮事件处理
            initLoginEvents();
            
            // 检查是否有记住的登录状态
            checkRememberedLogin();
            
            // 监听回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.querySelector('[data-action="phoneLogin"]').click();
                }
            });
        });
        
        // 初始化事件处理
        function initLoginEvents() {
            // 处理所有data-action按钮
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (!target) return;
                
                const action = target.dataset.action;
                
                switch(action) {
                    case 'wechatLogin':
                        hideError();
                        // 模拟微信登录逻辑
                        console.log('微信登录');
                        // 实际开发中这里会调用微信小程序的登录API
                        break;
                        
                    case 'getVerifyCode':
                        const phoneInput = document.querySelector('input[type="tel"]');
                        const phone = phoneInput.value.trim();
                        
                        // 验证手机号格式
                        const phoneRegex = /^1[3-9]\d{9}$/;
                        if (!phoneRegex.test(phone)) {
                            alert('请输入正确的手机号码');
                            phoneInput.focus();
                            return;
                        }
                        
                        const button = document.getElementById('getCodeBtn');
                        button.disabled = true;
                        button.classList.add('opacity-50', 'cursor-not-allowed');
                        
                        // 模拟发送验证码
                        console.log('发送验证码到:', phone);
                        alert('验证码已发送到您的手机，请注意查收\n\n验证码有效期为5分钟');
                        
                        // 60秒倒计时显示
                        startCountdown(button);
                        break;
                        
                    case 'phoneLogin':
                        const phoneInput2 = document.querySelector('input[type="tel"]');
                        const codeInput = document.getElementById('verifyCode');
                        const rememberMeInput = document.getElementById('rememberMe');
                        const phone2 = phoneInput2.value.trim();
                        const code = codeInput.value.trim();
                        
                        // 验证手机号
                        const phoneRegex2 = /^1[3-9]\d{9}$/;
                        if (!phoneRegex2.test(phone2)) {
                            alert('请输入正确的手机号码');
                            phoneInput2.focus();
                            return;
                        }
                        
                        // 验证验证码
                        if (!code) {
                            alert('请输入验证码');
                            codeInput.focus();
                            return;
                        }
                        
                        if (code.length !== 6 || !/^\d{6}$/.test(code)) {
                            alert('验证码应为6位数字');
                            codeInput.focus();
                            return;
                        }
                        
                        // 模拟登录验证
                        console.log('登录验证:', { phone: phone2, code });
                        
                        // 处理记住我功能
                        if (rememberMeInput.checked) {
                            localStorage.setItem('rememberLogin', 'true');
                            localStorage.setItem('userPhone', phone2);
                            localStorage.setItem('loginTime', new Date().getTime().toString());
                            console.log('已保存登录状态');
                        } else {
                            // 清除记住的登录状态
                            localStorage.removeItem('rememberLogin');
                            localStorage.removeItem('userPhone');
                            localStorage.removeItem('loginTime');
                        }
                        
                        // 保存用户登录信息到sessionStorage
                        sessionStorage.setItem('isLoggedIn', 'true');
                        sessionStorage.setItem('userPhone', phone2);
                        sessionStorage.setItem('loginMethod', 'phone');
                        
                        // 模拟登录成功
                        alert('登录成功！欢迎使用本地助手');
                        
                        // 跳转到主页
                        window.location.href = 'shops.html';
                        break;
                        
                    case 'openQRScanner':
                        console.log('打开扫一扫');
                        // 专用于扫描平台生成的呼叫服务二维码
                        // 用于防伪验证和快速获取服务信息
                        alert('扫一扫功能\n\n专用于扫描：\n• 平台生成的呼叫服务二维码\n• 司机/车主身份验证二维码\n• 车辆信息验证二维码\n• 服务订单确认二维码\n\n注意：此功能仅用于平台服务验证，确保服务真实性和安全性');
                        break;
                        
                    case 'showForgotPassword':
                        alert('忘记密码\n\n请联系客服重置密码：\n• 客服电话：************\n• 在线客服：工作日 9:00-18:00\n• 或通过微信登录后在个人中心修改手机号');
                        break;
                }
            });
        }
        
        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorDiv.classList.remove('hidden');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                errorDiv.classList.add('hidden');
            }, 3000);
        }
        
        // 隐藏错误信息
        function hideError() {
            document.getElementById('errorMessage').classList.add('hidden');
        }
        
        // 倒计时函数
        function startCountdown(button) {
            let count = 60;
            button.textContent = `${count}s后重发`;
            
            const timer = setInterval(() => {
                count--;
                button.textContent = `${count}s后重发`;
                
                if (count <= 0) {
                    clearInterval(timer);
                    button.disabled = false;
                    button.textContent = '发送验证码';
                    button.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            }, 1000);
        }
        
        // 检查记住的登录状态
        function checkRememberedLogin() {
            const rememberLogin = localStorage.getItem('rememberLogin');
            const userPhone = localStorage.getItem('userPhone');
            const loginTime = localStorage.getItem('loginTime');
            
            if (rememberLogin === 'true' && userPhone && loginTime) {
                // 检查登录时间是否在有效期内（例如30天）
                const currentTime = new Date().getTime();
                const timeDiff = currentTime - parseInt(loginTime);
                const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
                
                if (daysDiff <= 30) {
                    // 自动填充手机号并勾选记住我
                    const phoneInput = document.querySelector('input[type="tel"]');
                    const rememberMeInput = document.getElementById('rememberMe');
                    
                    if (phoneInput && rememberMeInput) {
                        phoneInput.value = userPhone;
                        rememberMeInput.checked = true;
                        console.log('已自动填充记住的手机号');
                    }
                } else {
                    // 登录状态已过期，清除记录
                    localStorage.removeItem('rememberLogin');
                    localStorage.removeItem('userPhone');
                    localStorage.removeItem('loginTime');
                }
            }
        }
    </script>
</body>
</html>