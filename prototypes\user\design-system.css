/* 设计系统 CSS - 统一样式管理 */

/* ===== 颜色系统 ===== */
:root {
  /* 主题色调 - 深色主题 */
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-card: #1e1e3f;
  --bg-card-hover: #252547;
  --bg-card-active: #2a2d4a;
  
  /* 文字颜色 */
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-disabled: #64748b;
  
  /* 品牌主色 */
  --brand-primary: #3b82f6;
  --brand-secondary: #2563eb;
  --brand-tertiary: #1d4ed8;
  
  /* 功能色彩 */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #06b6d4;
  
  /* 边框颜色 */
  --border-primary: #334155;
  --border-secondary: #475569;
  --border-muted: #64748b;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* 字体大小 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;
  
  /* 字重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
}

/* ===== 基础组件样式 ===== */

/* 按钮系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: 1.5;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--brand-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--brand-secondary);
}

.btn-secondary {
  background-color: transparent;
  color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--brand-primary);
  color: white;
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-primary);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--bg-card-hover);
  color: var(--text-primary);
}

.btn-danger {
  background-color: var(--error);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-xs);
  border-radius: var(--radius-sm);
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
  border-radius: var(--radius-lg);
}

/* 卡片组件 */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.card:hover {
  background-color: var(--bg-card-hover);
  box-shadow: var(--shadow-md);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-interactive:active {
  transform: translateY(0);
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: all 0.2s ease-in-out;
}

.input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.input::placeholder {
  color: var(--text-muted);
}

/* 标签组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-card-hover);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-sm);
}

.badge-primary {
  background-color: var(--brand-primary);
  color: white;
}

.badge-success {
  background-color: var(--success);
  color: white;
}

.badge-warning {
  background-color: var(--warning);
  color: white;
}

.badge-error {
  background-color: var(--error);
  color: white;
}

/* 分隔线 */
.divider {
  height: 1px;
  background-color: var(--border-primary);
  margin: var(--space-lg) 0;
}

/* 文本样式 */
.text-contrast-high {
  color: var(--text-primary);
}

.text-contrast-medium {
  color: var(--text-secondary);
}

.text-contrast-low {
  color: var(--text-muted);
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-xs);
}

.status-online {
  background-color: var(--success);
}

.status-offline {
  background-color: var(--text-muted);
}

.status-busy {
  background-color: var(--warning);
}

.status-error {
  background-color: var(--error);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-radius: 50%;
  border-top-color: var(--brand-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 工具类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* 响应式工具类 */
@media (max-width: 640px) {
  .sm\:hidden {
    display: none;
  }
  
  .sm\:block {
    display: block;
  }
  
  .sm\:flex {
    display: flex;
  }
}

@media (min-width: 641px) {
  .md\:hidden {
    display: none;
  }
  
  .md\:block {
    display: block;
  }
  
  .md\:flex {
    display: flex;
  }
}

/* 无障碍增强 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --border-primary: #ffffff;
    --bg-card: #000000;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  /* 已经是深色主题，保持现有样式 */
}

/* 浅色模式备选 */
@media (prefers-color-scheme: light) {
  .light-theme {
    --bg-primary: #fafbfc;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    --bg-card-hover: #f9fafb;
    --bg-card-active: #f3f4f6;
    
    --text-primary: #1e293b;
    --text-secondary: #334155;
    --text-muted: #475569;
    --text-disabled: #64748b;
    
    --border-primary: #e2e8f0;
    --border-secondary: #cbd5e1;
    --border-muted: #94a3b8;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .btn {
    border: 1px solid #000;
    background: transparent;
    color: #000;
  }
}