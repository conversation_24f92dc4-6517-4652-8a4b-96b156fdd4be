<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no">
    <title>我的呼叫服务 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>
    <script src="../scripts/common.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=PingFang+SC:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-bg: #0f0f23;
            --secondary-bg: #1e1e3f;
            --card-bg: #252547;
            --accent-blue: #0066ff;
            --accent-purple: #6366f1;
            --accent-cyan: #06b6d4;
            --text-primary: #e2e8f0;
            --text-secondary: #94a3b8;
            --border-color: rgba(99, 102, 241, 0.2);
        }
        
        body {
            font-family: 'Inter', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #16213e 50%, var(--secondary-bg) 100%);
            min-height: 100vh;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-purple) 50%, var(--accent-cyan) 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
            border-color: rgba(99, 102, 241, 0.4);
        }
        
        .glow-button {
            background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
            transition: all 0.3s ease;
        }
        
        .glow-button:hover {
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
            transform: translateY(-1px);
        }
        
        .tab-active {
            background: var(--accent-blue);
            color: white;
        }
        
        .tab-inactive {
            background: rgba(99, 102, 241, 0.1);
            color: var(--text-secondary);
        }
        
        .modal-backdrop {
            backdrop-filter: blur(10px);
            background: rgba(15, 15, 35, 0.8);
        }
        
        .qr-preview {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-[#0f0f23] via-[#1e1e3f] to-[#252547] text-white">
    <!-- 顶部导航 -->
    <header class="tech-gradient p-4 sticky top-0 z-40">
        <div class="flex items-center justify-between max-w-md mx-auto">
            <div class="flex items-center space-x-3">
                <button onclick="history.back()" class="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                <h1 class="text-lg font-semibold">我的呼叫服务</h1>
            </div>
            <button id="addQrBtn" class="glow-button px-4 py-2 rounded-lg text-sm font-medium flex items-center space-x-1" onclick="addNewQR()">
                <i data-lucide="plus" class="w-4 h-4"></i>
                <span>新增二维码</span>
            </button>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-md mx-auto px-4 pb-20">
        <!-- 标签页导航 -->
        <div class="flex bg-gray-800/50 rounded-xl p-1 mt-4 mb-6">
            <button class="tab-btn flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-all tab-active" data-tab="qr-management">
                二维码管理
            </button>
            <button class="tab-btn flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-all tab-inactive" data-tab="call-records">
                呼叫记录
            </button>
            <button class="tab-btn flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-all tab-inactive" data-tab="reviews">
                评价管理
            </button>
        </div>

        <!-- 二维码管理页面 -->
        <div id="qr-management" class="tab-content">
            <!-- 二维码列表 -->
            <div class="space-y-4">
                <!-- 二维码卡片 1 -->
                <div class="bg-slate-800 rounded-xl p-4 border border-slate-700">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <h3 class="text-white font-medium mb-1">餐桌服务二维码</h3>
                            <p class="text-slate-400 text-sm">服务时间：09:00-22:00</p>
                            <p class="text-slate-500 text-xs">创建时间：2024-01-15 14:30</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">已启用</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">餐饮服务</span>
                            <span class="text-slate-400 text-sm">今日呼叫：12次</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <button class="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors" onclick="previewQR('餐桌服务二维码')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                            <span>预览</span>
                        </button>
                        <button class="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="downloadQR('餐桌服务二维码')">
                            <i data-lucide="download" class="w-4 h-4"></i>
                            <span>下载</span>
                        </button>
                        <button class="flex items-center space-x-1 px-3 py-2 bg-slate-600 text-white rounded-lg text-sm hover:bg-slate-500 transition-colors" onclick="editQR('餐桌服务二维码')">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                            <span>编辑</span>
                        </button>
                    </div>
                </div>
                
                <!-- 二维码卡片 2 -->
                <div class="bg-slate-800 rounded-xl p-4 border border-slate-700">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <h3 class="text-white font-medium mb-1">客房服务二维码</h3>
                            <p class="text-slate-400 text-sm">服务时间：24小时</p>
                            <p class="text-slate-500 text-xs">创建时间：2024-01-10 09:15</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">已启用</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">酒店服务</span>
                            <span class="text-slate-400 text-sm">今日呼叫：8次</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3">
                        <button class="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors" onclick="previewQR('客房服务二维码')">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                            <span>预览</span>
                        </button>
                        <button class="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="downloadQR('客房服务二维码')">
                            <i data-lucide="download" class="w-4 h-4"></i>
                            <span>下载</span>
                        </button>
                        <button class="flex items-center space-x-1 px-3 py-2 bg-slate-600 text-white rounded-lg text-sm hover:bg-slate-500 transition-colors" onclick="editQR('客房服务二维码')">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                            <span>编辑</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 新增二维码按钮 -->
            <div class="mt-6">
                <button class="w-full flex items-center justify-center space-x-2 py-4 border-2 border-dashed border-slate-600 rounded-xl text-slate-400 hover:border-blue-500 hover:text-blue-400 transition-colors" onclick="addNewQR()">
                    <i data-lucide="plus" class="w-5 h-5"></i>
                    <span>新增呼叫服务二维码</span>
                </button>
            </div>
        </div>

        <!-- 呼叫记录页面 -->
        <div id="call-records" class="tab-content hidden">
            <div class="space-y-4">
                <!-- 实时推送消息 -->
                <div class="bg-red-600/10 border border-red-600/30 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-red-400 flex items-center">
                            <i data-lucide="bell" class="w-4 h-4 mr-2"></i>
                            新呼叫消息
                        </h3>
                        <span class="text-xs text-gray-400">2分钟前</span>
                    </div>
                    <p class="text-sm text-white mb-2">餐桌1号 - 点餐</p>
                    <p class="text-xs text-gray-400 mb-3">备注：要一份宫保鸡丁，不要花生</p>
                    <div class="flex space-x-2">
                        <button class="flex-1 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm font-medium transition-colors">
                            确认处理
                        </button>
                        <button class="flex-1 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm font-medium transition-colors">
                            等待处理
                        </button>
                    </div>
                </div>

                <!-- 历史记录 -->
                <div class="bg-gray-800/50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-white">餐桌1号 - 加水</h3>
                        <span class="text-xs text-green-400">已处理</span>
                    </div>
                    <p class="text-sm text-gray-400 mb-1">呼叫时间：14:25</p>
                    <p class="text-xs text-gray-400">处理时间：14:27</p>
                </div>

                <div class="bg-gray-800/50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-white">前台服务 - 咨询</h3>
                        <span class="text-xs text-blue-400">处理中</span>
                    </div>
                    <p class="text-sm text-gray-400 mb-1">呼叫时间：13:45</p>
                    <p class="text-xs text-gray-400">备注：询问附近景点推荐</p>
                </div>
            </div>
        </div>

        <!-- 评价管理页面 -->
        <div id="reviews" class="tab-content hidden">
            <div class="space-y-4">
                <!-- 评价统计 -->
                <div class="bg-gray-800/50 rounded-xl p-4">
                    <h3 class="font-semibold text-white mb-3">评价统计</h3>
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-yellow-400">4.8</div>
                            <div class="text-xs text-gray-400">平均评分</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-blue-400">156</div>
                            <div class="text-xs text-gray-400">总评价数</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400">98%</div>
                            <div class="text-xs text-gray-400">好评率</div>
                        </div>
                    </div>
                </div>

                <!-- 评价列表 -->
                <div class="bg-gray-800/50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-2">
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                            </div>
                            <span class="text-sm text-white">餐桌1号</span>
                        </div>
                        <span class="text-xs text-gray-400">今天 15:30</span>
                    </div>
                    <p class="text-sm text-gray-300 mb-2">服务很及时，菜品也很不错，会再来的！</p>
                    <button class="text-xs text-blue-400 hover:text-blue-300 transition-colors">
                        回复评价
                    </button>
                </div>

                <div class="bg-gray-800/50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-2">
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                <i data-lucide="star" class="w-4 h-4"></i>
                            </div>
                            <span class="text-sm text-white">前台服务</span>
                        </div>
                        <span class="text-xs text-gray-400">昨天 20:15</span>
                    </div>
                    <p class="text-sm text-gray-300 mb-2">前台小姐姐很热情，给了很多有用的建议。</p>
                    <div class="bg-blue-600/10 border-l-2 border-blue-600 pl-3 mt-2">
                        <p class="text-xs text-blue-300">回复：谢谢您的好评，我们会继续努力提供更好的服务！</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 二维码预览弹窗 -->
    <div id="qrPreviewModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center">
        <div class="bg-slate-800 rounded-2xl p-6 max-w-sm w-full mx-4">
            <div class="text-center">
                <h3 class="text-lg font-semibold text-white mb-4">二维码预览</h3>
                
                <!-- 二维码图片 -->
                <div class="bg-white rounded-xl p-4 mb-4 mx-auto w-fit">
                    <h4 id="qrTitle" class="text-center text-gray-800 mb-2 font-medium">服务二维码</h4>
                    <div class="w-40 h-40 mx-auto flex items-center justify-center">
                        <canvas id="qrCanvas" class="w-full h-full"></canvas>
                        <!-- 备用静态图片，当canvas不可用时显示 -->
                        <img id="qrFallback" src="https://api.qrserver.com/v1/create-qr-code/?size=160x160&data=https://example.com/call-service/table-1" 
                             alt="呼叫服务二维码" class="w-full h-full hidden">
                        <!-- 二维码API来源: QR Server API - https://api.qrserver.com -->
                    </div>
                </div>
                
                <p class="text-slate-400 text-sm mb-4">扫码呼叫服务</p>
                
                <div class="flex space-x-3">
                    <button onclick="downloadQRFromPreview()" class="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        下载打印
                    </button>
                    <button onclick="closeQRPreview()" class="flex-1 bg-slate-600 text-white py-2 rounded-lg hover:bg-slate-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑二维码弹窗 -->
    <div id="qrFormModal" class="fixed inset-0 z-50 modal-backdrop flex items-center justify-center hidden">
        <div class="bg-gray-900 rounded-2xl p-6 max-w-md mx-4 relative max-h-[90vh] overflow-y-auto">
            <button onclick="closeQrForm()" class="absolute right-4 top-4 text-gray-400 hover:text-white">
                <i data-lucide="x" class="w-6 h-6"></i>
            </button>
            <h3 class="text-lg font-semibold text-white mb-4">新增二维码</h3>
            
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">服务场景名称 *</label>
                    <input type="text" name="serviceName" class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none" placeholder="如：餐桌1号、前台服务" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">服务内容 *</label>
                    <div class="flex flex-wrap gap-2 mb-2" id="serviceList">
                        <span class="px-2 py-1 bg-blue-600/20 text-blue-300 rounded text-xs">点餐</span>
                        <span class="px-2 py-1 bg-blue-600/20 text-blue-300 rounded text-xs">加水</span>
                        <span class="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">评价</span>
                    </div>
                    <div class="flex">
                        <input type="text" class="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded-l-lg text-white focus:border-blue-500 focus:outline-none" placeholder="添加服务项目">
                        <button type="button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-r-lg text-sm font-medium transition-colors">
                            添加
                        </button>
                    </div>
                    <p class="text-xs text-gray-400 mt-1">"评价"是固定项，不能删除</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">服务时间</label>
                    <input type="text" name="serviceTime" class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none" placeholder="如：08:00-22:00">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">欢迎语</label>
                    <textarea name="welcomeMessage" class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none" rows="2" placeholder="欢迎使用我们的服务，请选择您需要的服务项目"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">评价接收人设置</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="reviewReceiver" value="self" class="mr-2" checked>
                            <span class="text-sm text-gray-300">我自己接收</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="reviewReceiver" value="other" class="mr-2">
                            <span class="text-sm text-gray-300">指定其他人接收</span>
                        </label>
                        <div class="space-y-3">
                            <input type="tel" name="receiverPhone" class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none" placeholder="接收人手机号" disabled>
                            <div class="verification-section" style="display: none;">
                                <label class="block text-sm font-medium text-gray-300 mb-2">验证码 *</label>
                                <div class="flex space-x-2">
                                    <input type="text" id="verificationCode" placeholder="请输入验证码" 
                                           class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500" required>
                                    <button type="button" id="sendCodeBtn" onclick="sendVerificationCode()" 
                                            class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors whitespace-nowrap">
                                        发送验证码
                                    </button>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">为确保安全，修改接收人手机号需要验证</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex space-x-3 pt-4">
                    <button type="button" onclick="closeQrForm()" class="flex-1 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors">
                        取消
                    </button>
                    <button type="submit" class="flex-1 py-3 glow-button rounded-lg font-medium">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // 错误处理包装器
        function withErrorHandling(func, errorMessage = '操作失败，请稍后重试') {
            return function(...args) {
                try {
                    return func.apply(this, args);
                } catch (error) {
                    console.error('Error:', error);
                    showToast(errorMessage, 'error');
                }
            };
        }
        
        // 初始化图标
        lucide.createIcons();
        
        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            // ESC键关闭弹窗
            if (e.key === 'Escape') {
                const qrPreview = document.getElementById('qrPreviewModal');
                const qrFormModal = document.getElementById('qrFormModal');
                
                if (qrPreview && !qrPreview.classList.contains('hidden')) {
                    closeQRPreview();
                } else if (qrFormModal && !qrFormModal.classList.contains('hidden')) {
                    closeQrForm();
                }
            }
            
            // Tab键焦点管理
            if (e.key === 'Tab') {
                const activeModal = document.querySelector('.modal-backdrop:not(.hidden)');
                if (activeModal) {
                    const focusableElements = activeModal.querySelectorAll(
                        'button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
                    );
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];
                    
                    if (e.shiftKey && document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    } else if (!e.shiftKey && document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        });
        
        // 为所有按钮添加焦点样式
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('focus', function() {
                this.classList.add('ring-2', 'ring-blue-500', 'ring-opacity-50');
            });
            
            button.addEventListener('blur', function() {
                this.classList.remove('ring-2', 'ring-blue-500', 'ring-opacity-50');
            });
        });
        
        // 为输入框添加焦点样式
        document.querySelectorAll('input, textarea').forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.add('ring-2', 'ring-blue-500', 'ring-opacity-50');
            });
            
            input.addEventListener('blur', function() {
                this.classList.remove('ring-2', 'ring-blue-500', 'ring-opacity-50');
            });
        });
        
        // 添加ARIA标签和角色
        document.querySelectorAll('.tab-btn').forEach((tab, index) => {
            tab.setAttribute('role', 'tab');
            tab.setAttribute('aria-selected', index === 0 ? 'true' : 'false');
            tab.setAttribute('tabindex', index === 0 ? '0' : '-1');
        });
        
        document.querySelectorAll('.tab-content').forEach((content, index) => {
            content.setAttribute('role', 'tabpanel');
            content.setAttribute('aria-hidden', index === 0 ? 'false' : 'true');
        });
        
        // 为弹窗添加ARIA属性
        const qrPreview = document.getElementById('qrPreviewModal');
        if (qrPreview) {
            qrPreview.setAttribute('role', 'dialog');
            qrPreview.setAttribute('aria-modal', 'true');
            qrPreview.setAttribute('aria-labelledby', 'qrTitle');
        }
        
        const qrFormModal = document.getElementById('qrFormModal');
        if (qrFormModal) {
            qrFormModal.setAttribute('role', 'dialog');
            qrFormModal.setAttribute('aria-modal', 'true');
            qrFormModal.setAttribute('aria-labelledby', 'formTitle');
        }
        
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.dataset.tab;
                
                // 移除所有活跃状态
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('tab-active');
                    b.classList.add('tab-inactive');
                    b.setAttribute('aria-selected', 'false');
                    b.setAttribute('tabindex', '-1');
                });
                
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                    content.setAttribute('aria-hidden', 'true');
                });
                
                // 激活当前标签
                this.classList.add('tab-active');
                this.classList.remove('tab-inactive');
                this.setAttribute('aria-selected', 'true');
                this.setAttribute('tabindex', '0');
                
                const targetContent = document.getElementById(targetTab);
                targetContent.classList.remove('hidden');
                targetContent.setAttribute('aria-hidden', 'false');
            });
            
            // 键盘导航支持
            btn.addEventListener('keydown', function(e) {
                const tabs = Array.from(document.querySelectorAll('.tab-btn'));
                const currentIndex = tabs.indexOf(this);
                
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    e.preventDefault();
                    let nextIndex;
                    
                    if (e.key === 'ArrowLeft') {
                        nextIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                    } else {
                        nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                    }
                    
                    tabs[nextIndex].focus();
                    tabs[nextIndex].click();
                }
            });
        });
        
        // 新增二维码
        document.getElementById('addQrBtn').addEventListener('click', () => {
            document.getElementById('qrFormModal').classList.remove('hidden');
        });
        
        // 关闭表单弹窗
        function closeQrForm() {
            document.getElementById('qrFormModal').classList.add('hidden');
        }
        
        // 预览二维码
        const previewQR = withErrorHandling(function(name) {
            const titleElement = document.getElementById('qrTitle');
            const canvasElement = document.getElementById('qrCanvas');
            const fallbackElement = document.getElementById('qrFallback');
            const modal = document.getElementById('qrPreviewModal');
            
            if (!modal || !titleElement) {
                console.error('预览模态框元素未找到');
                showToast('预览功能暂时不可用', 'error');
                return;
            }
            
            titleElement.textContent = name;
            
            // 尝试生成二维码
            if (typeof QRious !== 'undefined' && canvasElement) {
                try {
                    const qr = new QRious({
                        element: canvasElement,
                        value: `https://example.com/call/${encodeURIComponent(name)}`,
                        size: 160,
                        background: 'white',
                        foreground: '#0066ff'
                    });
                    canvasElement.classList.remove('hidden');
                    if (fallbackElement) fallbackElement.classList.add('hidden');
                } catch (qrError) {
                    console.warn('二维码生成失败，使用备用图片:', qrError);
                    if (canvasElement) canvasElement.classList.add('hidden');
                    if (fallbackElement) {
                        fallbackElement.src = `https://api.qrserver.com/v1/create-qr-code/?size=160x160&data=${encodeURIComponent('https://example.com/call/' + name)}`;
                        fallbackElement.classList.remove('hidden');
                    }
                }
            } else {
                // QRious不可用，使用备用图片
                if (canvasElement) canvasElement.classList.add('hidden');
                if (fallbackElement) {
                    fallbackElement.src = `https://api.qrserver.com/v1/create-qr-code/?size=160x160&data=${encodeURIComponent('https://example.com/call/' + name)}`;
                    fallbackElement.classList.remove('hidden');
                }
            }
            
            modal.classList.remove('hidden');
            
            // 焦点管理
            setTimeout(() => {
                const closeBtn = modal.querySelector('button');
                if (closeBtn) closeBtn.focus();
            }, 100);
        }, '预览二维码失败');
        
        // 关闭预览弹窗
        function closeQRPreview() {
            document.getElementById('qrPreviewModal').classList.add('hidden');
        }
        
        // 下载二维码
        function downloadQR(name) {
            try {
                const titleElement = document.getElementById('qrTitle');
                const canvasElement = document.getElementById('qrCanvas');
                
                if (titleElement) {
                    titleElement.textContent = name;
                }
                
                // 生成二维码用于下载
                if (typeof QRious !== 'undefined' && canvasElement) {
                    try {
                        const qr = new QRious({
                            element: canvasElement,
                            value: `https://example.com/call/${encodeURIComponent(name)}`,
                            size: 300, // 下载用更高分辨率
                            background: 'white',
                            foreground: '#0066ff'
                        });
                        
                        // 直接下载
                        const link = document.createElement('a');
                        link.download = `${name}-二维码.png`;
                        link.href = canvasElement.toDataURL('image/png');
                        link.click();
                        
                        // 显示成功提示
                        const button = event.target.closest('button');
                        const originalText = button.querySelector('span').textContent;
                        button.querySelector('span').textContent = '已下载';
                        button.classList.add('bg-green-700');
                        setTimeout(() => {
                            button.querySelector('span').textContent = originalText;
                            button.classList.remove('bg-green-700');
                        }, 2000);
                        
                    } catch (qrError) {
                        console.error('二维码生成失败:', qrError);
                        alert('下载失败，请稍后重试');
                    }
                } else {
                    // 备用下载方式
                    const downloadUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent('https://example.com/call/' + name)}`;
                    const link = document.createElement('a');
                    link.download = `${name}-二维码.png`;
                    link.href = downloadUrl;
                    link.click();
                }
            } catch (error) {
                console.error('下载二维码时发生错误:', error);
                alert('下载失败，请稍后重试');
            }
        }
        
        // 编辑二维码
        function editQR(name) {
            try {
                // 这里可以预填充表单数据
                const modal = document.getElementById('qrFormModal');
                const titleInput = modal.querySelector('input[type="text"]');
                if (titleInput) {
                    titleInput.value = name;
                }
                modal.classList.remove('hidden');
            } catch (error) {
                console.error('编辑二维码时发生错误:', error);
                alert('打开编辑界面失败，请稍后重试');
            }
        }
        
        // 从预览弹窗下载二维码
        function downloadQRFromPreview() {
            try {
                const titleElement = document.getElementById('qrTitle');
                const canvasElement = document.getElementById('qrCanvas');
                const fallbackElement = document.getElementById('qrFallback');
                
                const name = titleElement ? titleElement.textContent : '二维码';
                
                if (canvasElement && !canvasElement.classList.contains('hidden')) {
                    // 从canvas下载
                    const link = document.createElement('a');
                    link.download = `${name}-二维码.png`;
                    link.href = canvasElement.toDataURL('image/png');
                    link.click();
                } else if (fallbackElement && !fallbackElement.classList.contains('hidden')) {
                    // 从备用图片下载
                    const link = document.createElement('a');
                    link.download = `${name}-二维码.png`;
                    link.href = fallbackElement.src;
                    link.click();
                } else {
                    alert('无法下载二维码，请稍后重试');
                }
            } catch (error) {
                console.error('下载二维码时发生错误:', error);
                alert('下载失败，请稍后重试');
            }
        }
        
        // 新增二维码功能
        function addNewQR() {
            try {
                const modal = document.getElementById('qrFormModal');
                const form = modal.querySelector('form');
                
                // 重置表单
                if (form) {
                    form.reset();
                }
                
                // 更新标题
                const title = modal.querySelector('h3');
                if (title) {
                    title.textContent = '新增二维码';
                }
                
                modal.classList.remove('hidden');
            } catch (error) {
                console.error('打开新增界面时发生错误:', error);
                alert('打开新增界面失败，请稍后重试');
            }
        }
        
        // 发送验证码功能
        function sendVerificationCode() {
            try {
                const phoneInput = document.querySelector('input[name="receiverPhone"]');
                const sendBtn = document.getElementById('sendCodeBtn');
                
                if (!phoneInput || !phoneInput.value.trim()) {
                    showToast('请先输入手机号', 'error');
                    return;
                }
                
                // 验证手机号格式
                const phoneRegex = /^1[3-9]\d{9}$/;
                if (!phoneRegex.test(phoneInput.value.trim())) {
                    showToast('请输入正确的手机号格式', 'error');
                    return;
                }
                
                // 模拟发送验证码
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';
                sendBtn.classList.add('opacity-50');
                
                setTimeout(() => {
                    showToast('验证码已发送到您的手机，请注意查收', 'success');
                    
                    // 开始倒计时
                    let countdown = 60;
                    const timer = setInterval(() => {
                        sendBtn.textContent = `${countdown}s后重发`;
                        countdown--;
                        
                        if (countdown < 0) {
                            clearInterval(timer);
                            sendBtn.disabled = false;
                            sendBtn.textContent = '发送验证码';
                            sendBtn.classList.remove('opacity-50');
                        }
                    }, 1000);
                }, 1000);
                
            } catch (error) {
                console.error('发送验证码时发生错误:', error);
                showToast('发送验证码失败，请稍后重试', 'error');
            }
        }
        
        // showToast函数已移至公共脚本 common.js
            
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            }, 100);
            
            // 自动消失
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
        
        // 表单验证
        function validateForm() {
            const serviceName = document.querySelector('input[name="serviceName"]').value.trim();
            
            if (!serviceName) {
                showToast('请输入服务场景名称', 'error');
                return false;
            }
            
            if (serviceName.length < 2) {
                showToast('服务场景名称至少需要2个字符', 'error');
                return false;
            }
            
            // 检查评价接收人设置
            const receiverType = document.querySelector('input[name="reviewReceiver"]:checked')?.value;
            if (receiverType === 'other') {
                const phone = document.querySelector('input[name="receiverPhone"]').value.trim();
                const verificationCode = document.querySelector('#verificationCode').value.trim();
                const phoneRegex = /^1[3-9]\d{9}$/;
                
                if (!phone) {
                    showToast('请输入接收人手机号', 'error');
                    return false;
                }
                
                if (!phoneRegex.test(phone)) {
                    showToast('请输入正确的手机号码', 'error');
                    return false;
                }
                
                if (!verificationCode) {
                    showToast('请输入验证码', 'error');
                    return false;
                }
                
                if (verificationCode.length !== 6) {
                    showToast('验证码应为6位数字', 'error');
                    return false;
                }
            }
            
            return true;
        }
        
        // 评价接收人设置
        document.querySelectorAll('input[name="reviewReceiver"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                const phoneInput = document.querySelector('input[type="tel"]');
                const verificationSection = document.querySelector('.verification-section');
                
                if (phoneInput) {
                    phoneInput.disabled = e.target.value === 'self';
                    if (e.target.value === 'self') {
                        phoneInput.value = '';
                        document.querySelector('#verificationCode').value = '';
                    } else {
                        phoneInput.focus();
                    }
                }
                
                if (verificationSection) {
                    verificationSection.style.display = e.target.value === 'other' ? 'block' : 'none';
                }
            });
        });
        
        // 表单提交处理
        document.querySelector('#qrFormModal form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            try {
                // 表单验证
                if (!validateForm()) {
                    return;
                }
                
                const formData = new FormData(e.target);
                const serviceName = formData.get('serviceName');
                const serviceTime = formData.get('serviceTime');
                const welcomeMessage = formData.get('welcomeMessage');
                const receiverType = formData.get('reviewReceiver');
                const receiverPhone = formData.get('receiverPhone');
                
                // 模拟保存成功
                const saveBtn = e.target.querySelector('button[type="submit"]');
                const originalText = saveBtn.textContent;
                
                saveBtn.disabled = true;
                saveBtn.textContent = '保存中...';
                saveBtn.classList.add('opacity-50');
                
                setTimeout(() => {
                    showToast('二维码创建成功！', 'success');
                    closeQrForm();
                    
                    // 重置按钮状态
                    saveBtn.disabled = false;
                    saveBtn.textContent = originalText;
                    saveBtn.classList.remove('opacity-50');
                    
                    // 可以在这里添加新创建的二维码到列表中
                    // addQRToList(serviceName, serviceTime, welcomeMessage);
                }, 1500);
                
            } catch (error) {
                console.error('保存二维码时发生错误:', error);
                showToast('保存失败，请稍后重试', 'error');
            }
        });
    </script>
</body>
</html>