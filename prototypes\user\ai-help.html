<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能助手 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .chat-bubble {
            animation: slideUp 0.3s ease-out;
        }
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .typing-indicator {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="bot" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-white">AI智能助手</h1>
                        <p class="text-xs text-slate-400" id="sectionTitle">通用帮助</p>
                    </div>
                </div>
                
                <button onclick="clearChat()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="trash-2" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 聊天区域 -->
    <div class="max-w-md mx-auto px-4 py-4 pb-24">
        <div id="chatContainer" class="space-y-4">
            <!-- 欢迎消息 -->
            <div class="chat-bubble">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i data-lucide="bot" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="bg-slate-800 rounded-lg rounded-tl-none p-3 max-w-xs">
                        <p class="text-sm text-slate-200" id="welcomeMessage">
                            您好！我是您的AI智能助手，可以为您提供本地助手平台的使用帮助。请告诉我您需要什么帮助？
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷问题 -->
    <div class="fixed bottom-20 left-0 right-0 bg-slate-900 border-t border-slate-700 p-4">
        <div class="max-w-md mx-auto">
            <div class="mb-3">
                <p class="text-xs text-slate-400 mb-2">常见问题：</p>
                <div class="flex flex-wrap gap-2" id="quickQuestions">
                    <!-- 快捷问题将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center space-x-3">
                <div class="flex-1 relative">
                    <input type="text" id="messageInput" placeholder="输入您的问题..." 
                           class="w-full bg-slate-800 border border-slate-600 rounded-lg px-4 py-3 pr-12 text-sm text-slate-200 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                           onkeypress="handleKeyPress(event)">
                    <button onclick="sendMessage()" class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                        <i data-lucide="send" class="w-4 h-4 text-white"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取当前板块信息
        const urlParams = new URLSearchParams(window.location.search);
        const currentSection = urlParams.get('section') || 'general';
        
        // 板块配置
        const sectionConfig = {
            shops: {
                title: '商铺板块帮助',
                welcome: '您好！我是商铺板块的AI助手，可以帮您解答关于商铺搜索、店铺信息、优惠券使用等问题。',
                quickQuestions: [
                    '如何搜索附近商铺？',
                    '怎么使用优惠券？',
                    '如何联系商家？',
                    '商铺认证标识说明'
                ]
            },
            information: {
                title: '信息板块帮助',
                welcome: '您好！我是信息板块的AI助手，可以帮您解答关于信息发布、搜索筛选、安全交易等问题。',
                quickQuestions: [
                    '如何发布信息？',
                    '怎么筛选信息类型？',
                    '如何安全交易？',
                    '举报虚假信息'
                ]
            },
            transportation: {
                title: '出行板块帮助',
                welcome: '您好！我是出行板块的AI助手，可以帮您解答关于拼车、代驾、出行安全等问题。',
                quickQuestions: [
                    '如何发布拼车信息？',
                    '代驾服务怎么用？',
                    '出行安全注意事项',
                    '如何取消订单？'
                ]
            },
            errands: {
                title: '跑腿板块帮助',
                welcome: '您好！我是跑腿板块的AI助手，可以帮您解答关于任务发布、接单流程、费用结算等问题。',
                quickQuestions: [
                    '如何发布跑腿任务？',
                    '怎么接单赚钱？',
                    '费用如何结算？',
                    '任务纠纷处理'
                ]
            },
            general: {
                title: '通用帮助',
                welcome: '您好！我是您的AI智能助手，可以为您提供本地助手平台的使用帮助。请告诉我您需要什么帮助？',
                quickQuestions: [
                    '平台功能介绍',
                    '账号安全设置',
                    '隐私保护说明',
                    '联系客服'
                ]
            }
        };

        // 初始化页面
        function initializePage() {
            const config = sectionConfig[currentSection];
            document.getElementById('sectionTitle').textContent = config.title;
            document.getElementById('welcomeMessage').textContent = config.welcome;
            
            // 生成快捷问题
            const quickQuestionsContainer = document.getElementById('quickQuestions');
            quickQuestionsContainer.innerHTML = '';
            
            config.quickQuestions.forEach(question => {
                const button = document.createElement('button');
                button.className = 'text-xs bg-slate-700 text-slate-300 px-3 py-2 rounded-full hover:bg-slate-600 transition-colors';
                button.textContent = question;
                button.onclick = () => askQuestion(question);
                quickQuestionsContainer.appendChild(button);
            });
            
            // 初始化Lucide图标
            lucide.createIcons();
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addUserMessage(message);
                input.value = '';
                
                // 显示AI正在思考
                showTypingIndicator();
                
                // 模拟AI回复
                setTimeout(() => {
                    hideTypingIndicator();
                    addAIResponse(message);
                }, 1500);
            }
        }

        // 快捷提问
        function askQuestion(question) {
            addUserMessage(question);
            showTypingIndicator();
            
            setTimeout(() => {
                hideTypingIndicator();
                addAIResponse(question);
            }, 1500);
        }

        // 添加用户消息
        function addUserMessage(message) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-bubble flex justify-end';
            messageDiv.innerHTML = `
                <div class="bg-blue-600 rounded-lg rounded-tr-none p-3 max-w-xs">
                    <p class="text-sm text-white">${message}</p>
                </div>
            `;
            chatContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // 显示输入指示器
        function showTypingIndicator() {
            const chatContainer = document.getElementById('chatContainer');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typingIndicator';
            typingDiv.className = 'chat-bubble';
            typingDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i data-lucide="bot" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="bg-slate-800 rounded-lg rounded-tl-none p-3">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-slate-400 rounded-full typing-indicator"></div>
                            <div class="w-2 h-2 bg-slate-400 rounded-full typing-indicator" style="animation-delay: 0.2s"></div>
                            <div class="w-2 h-2 bg-slate-400 rounded-full typing-indicator" style="animation-delay: 0.4s"></div>
                        </div>
                    </div>
                </div>
            `;
            chatContainer.appendChild(typingDiv);
            scrollToBottom();
        }

        // 隐藏输入指示器
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 添加AI回复
        function addAIResponse(userMessage) {
            const response = generateAIResponse(userMessage);
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-bubble';
            messageDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i data-lucide="bot" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="bg-slate-800 rounded-lg rounded-tl-none p-3 max-w-xs">
                        <p class="text-sm text-slate-200">${response}</p>
                    </div>
                </div>
            `;
            chatContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // 生成AI回复
        function generateAIResponse(userMessage) {
            const config = sectionConfig[currentSection];
            const message = userMessage.toLowerCase();
            
            // 根据不同板块和问题类型生成回复
            if (currentSection === 'shops') {
                if (message.includes('搜索') || message.includes('附近')) {
                    return '您可以通过以下方式搜索商铺：\n1. 在顶部搜索框输入关键词\n2. 点击城市选择按钮切换地区\n3. 使用分类筛选功能\n4. 查看距离排序结果';
                } else if (message.includes('优惠券')) {
                    return '优惠券使用方法：\n1. 点击商铺卡片上的优惠券标签\n2. 查看优惠详情和使用条件\n3. 到店消费时出示优惠券\n4. 注意查看有效期限制';
                } else if (message.includes('联系') || message.includes('商家')) {
                    return '联系商家的方式：\n1. 点击商铺卡片上的电话图标\n2. 查看商铺详情页的联系信息\n3. 通过平台消息功能咨询\n4. 注意核实商家身份，谨防诈骗';
                }
            } else if (currentSection === 'information') {
                if (message.includes('发布')) {
                    return '发布信息的步骤：\n1. 点击底部"发布"按钮\n2. 选择信息类型（招聘、租房等）\n3. 填写详细信息和联系方式\n4. 上传相关图片\n5. 确认发布并等待审核';
                } else if (message.includes('筛选')) {
                    return '信息筛选功能：\n1. 使用顶部分类标签快速筛选\n2. 点击排序按钮选择排序方式\n3. 使用搜索框输入关键词\n4. 设置距离和价格范围';
                } else if (message.includes('安全') || message.includes('交易')) {
                    return '安全交易建议：\n1. 当面交易，一手交钱一手交货\n2. 核实对方身份信息\n3. 选择公共场所见面\n4. 保留交易凭证\n5. 遇到可疑情况及时举报';
                }
            } else if (currentSection === 'transportation') {
                if (message.includes('拼车')) {
                    return '拼车功能使用：\n1. 发布拼车信息时填写准确的出发地和目的地\n2. 设置合理的出发时间\n3. 注明可拼乘人数\n4. 与拼车伙伴提前沟通确认';
                } else if (message.includes('代驾')) {
                    return '代驾服务说明：\n1. 选择有资质的代驾司机\n2. 确认代驾费用和服务范围\n3. 检查司机身份证和驾驶证\n4. 保管好车钥匙和贵重物品';
                }
            } else if (currentSection === 'errands') {
                if (message.includes('发布') || message.includes('任务')) {
                    return '发布跑腿任务：\n1. 详细描述任务内容和要求\n2. 设置合理的报酬金额\n3. 标明任务地点和时间\n4. 上传必要的图片说明\n5. 选择可靠的接单人';
                } else if (message.includes('接单')) {
                    return '接单赚钱指南：\n1. 选择适合自己的任务类型\n2. 仔细阅读任务要求\n3. 评估任务难度和时间成本\n4. 及时与发布者沟通\n5. 按时完成任务获得好评';
                }
            }
            
            // 通用回复
            const generalResponses = [
                '感谢您的提问！我正在为您查找相关信息，请稍等片刻。',
                '这是一个很好的问题。根据您的需求，我建议您可以尝试以下方法...',
                '我理解您的困惑。让我为您详细解释一下这个功能的使用方法。',
                '您提到的问题很常见，我来为您提供一些实用的建议。'
            ];
            
            return generalResponses[Math.floor(Math.random() * generalResponses.length)];
        }

        // 滚动到底部
        function scrollToBottom() {
            setTimeout(() => {
                window.scrollTo(0, document.body.scrollHeight);
            }, 100);
        }

        // 清空聊天记录
        function clearChat() {
            if (confirm('确定要清空聊天记录吗？')) {
                const chatContainer = document.getElementById('chatContainer');
                chatContainer.innerHTML = `
                    <div class="chat-bubble">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i data-lucide="bot" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="bg-slate-800 rounded-lg rounded-tl-none p-3 max-w-xs">
                                <p class="text-sm text-slate-200" id="welcomeMessage">
                                    ${sectionConfig[currentSection].welcome}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
                // 图标已在页面初始化时加载
            }
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 处理键盘事件
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>