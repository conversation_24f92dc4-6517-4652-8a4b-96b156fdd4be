/**
 * 响应式组件库 - 解决移动端适配和响应式布局问题
 * 基于质量检查报告的响应式优化方案
 */

/**
 * 响应式管理器
 */
class ResponsiveManager {
    constructor() {
        this.breakpoints = UI_CONFIG.BREAKPOINTS;
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.listeners = [];
        this.setupEventListeners();
    }

    getCurrentBreakpoint() {
        const width = window.innerWidth;
        if (width >= this.breakpoints.xl) return 'xl';
        if (width >= this.breakpoints.lg) return 'lg';
        if (width >= this.breakpoints.md) return 'md';
        if (width >= this.breakpoints.sm) return 'sm';
        return 'xs';
    }

    setupEventListeners() {
        const debouncedResize = Utils.debounce(() => {
            const newBreakpoint = this.getCurrentBreakpoint();
            if (newBreakpoint !== this.currentBreakpoint) {
                const oldBreakpoint = this.currentBreakpoint;
                this.currentBreakpoint = newBreakpoint;
                this.notifyListeners(newBreakpoint, oldBreakpoint);
            }
        }, 100);

        window.addEventListener('resize', debouncedResize);
        window.addEventListener('orientationchange', () => {
            setTimeout(debouncedResize, 100);
        });
    }

    onBreakpointChange(callback) {
        this.listeners.push(callback);
        return () => {
            const index = this.listeners.indexOf(callback);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    notifyListeners(newBreakpoint, oldBreakpoint) {
        this.listeners.forEach(callback => {
            try {
                callback(newBreakpoint, oldBreakpoint);
            } catch (error) {
                console.error('响应式监听器执行错误:', error);
            }
        });
    }

    isMobile() {
        return this.currentBreakpoint === 'xs' || this.currentBreakpoint === 'sm';
    }

    isTablet() {
        return this.currentBreakpoint === 'md';
    }

    isDesktop() {
        return this.currentBreakpoint === 'lg' || this.currentBreakpoint === 'xl';
    }
}

/**
 * 移动端适配组件
 */
class MobileAdapter {
    constructor() {
        this.setupViewport();
        this.setupTouchHandlers();
        this.setupMobileOptimizations();
    }

    setupViewport() {
        // 设置viewport meta标签
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

        // 防止iOS Safari缩放
        document.addEventListener('gesturestart', (e) => {
            e.preventDefault();
        });
    }

    setupTouchHandlers() {
        // 优化触摸响应
        document.addEventListener('touchstart', () => {}, { passive: true });
        
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // 优化滚动性能
        document.addEventListener('touchmove', (e) => {
            // 允许在可滚动元素内滚动
            const target = e.target;
            const scrollable = target.closest('[data-scrollable]') || 
                             target.closest('.overflow-auto') ||
                             target.closest('.overflow-y-auto') ||
                             target.closest('.overflow-x-auto');
            
            if (!scrollable && !target.closest('input, textarea, select')) {
                e.preventDefault();
            }
        }, { passive: false });
    }

    setupMobileOptimizations() {
        // 移动端特定样式
        const style = document.createElement('style');
        style.textContent = `
            /* 移动端优化样式 */
            @media (max-width: 768px) {
                /* 确保最小点击区域 */
                button, .btn, [role="button"], input[type="button"], input[type="submit"] {
                    min-height: ${UI_CONFIG.MIN_TOUCH_TARGET}px;
                    min-width: ${UI_CONFIG.MIN_TOUCH_TARGET}px;
                }
                
                /* 优化表单输入 */
                input, textarea, select {
                    font-size: 16px; /* 防止iOS缩放 */
                }
                
                /* 优化模态框 */
                .modal-dialog {
                    margin: 1rem;
                    max-height: calc(100vh - 2rem);
                }
                
                /* 优化Toast位置 */
                .toast-container {
                    bottom: env(safe-area-inset-bottom, 1rem);
                    left: env(safe-area-inset-left, 1rem);
                    right: env(safe-area-inset-right, 1rem);
                }
            }
            
            /* 安全区域适配 */
            @supports (padding: env(safe-area-inset-top)) {
                .safe-area-top {
                    padding-top: env(safe-area-inset-top);
                }
                .safe-area-bottom {
                    padding-bottom: env(safe-area-inset-bottom);
                }
                .safe-area-left {
                    padding-left: env(safe-area-inset-left);
                }
                .safe-area-right {
                    padding-right: env(safe-area-inset-right);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 检测是否为移动设备
    static isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 检测是否为iOS设备
    static isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    // 检测是否为Android设备
    static isAndroid() {
        return /Android/.test(navigator.userAgent);
    }
}

/**
 * 自适应网格组件
 */
class AdaptiveGrid {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            minItemWidth: 280,
            gap: 16,
            autoResize: true,
            ...options
        };
        
        if (this.container) {
            this.init();
        }
    }

    init() {
        this.setupGrid();
        
        if (this.options.autoResize) {
            const resizeObserver = new ResizeObserver(() => {
                this.updateGrid();
            });
            resizeObserver.observe(this.container);
        }
    }

    setupGrid() {
        this.container.style.display = 'grid';
        this.container.style.gap = `${this.options.gap}px`;
        this.updateGrid();
    }

    updateGrid() {
        const containerWidth = this.container.offsetWidth;
        const columns = Math.floor(containerWidth / (this.options.minItemWidth + this.options.gap));
        const actualColumns = Math.max(1, columns);
        
        this.container.style.gridTemplateColumns = `repeat(${actualColumns}, 1fr)`;
    }

    // 静态方法：为元素应用自适应网格
    static apply(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        return Array.from(elements).map(el => new AdaptiveGrid(el, options));
    }
}

/**
 * 响应式图片组件
 */
class ResponsiveImage {
    constructor(img, options = {}) {
        this.img = typeof img === 'string' ? document.querySelector(img) : img;
        this.options = {
            lazy: true,
            placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
            ...options
        };
        
        if (this.img) {
            this.init();
        }
    }

    init() {
        if (this.options.lazy) {
            this.setupLazyLoading();
        }
        this.setupResponsive();
    }

    setupLazyLoading() {
        const originalSrc = this.img.src || this.img.dataset.src;
        
        // 设置占位符
        if (this.options.placeholder) {
            this.img.src = this.options.placeholder;
        }
        
        // 使用Intersection Observer
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(originalSrc);
                    observer.unobserve(this.img);
                }
            });
        }, {
            rootMargin: '50px'
        });
        
        observer.observe(this.img);
    }

    loadImage(src) {
        const img = new Image();
        img.onload = () => {
            this.img.src = src;
            this.img.classList.add('loaded');
        };
        img.onerror = () => {
            this.img.classList.add('error');
        };
        img.src = src;
    }

    setupResponsive() {
        this.img.style.maxWidth = '100%';
        this.img.style.height = 'auto';
    }

    // 静态方法：为所有图片应用响应式处理
    static applyToAll(selector = 'img', options = {}) {
        const images = document.querySelectorAll(selector);
        return Array.from(images).map(img => new ResponsiveImage(img, options));
    }
}

/**
 * 响应式表格组件
 */
class ResponsiveTable {
    constructor(table, options = {}) {
        this.table = typeof table === 'string' ? document.querySelector(table) : table;
        this.options = {
            breakpoint: 768,
            stackOnMobile: true,
            ...options
        };
        
        if (this.table) {
            this.init();
        }
    }

    init() {
        this.setupResponsive();
        this.setupEventListeners();
    }

    setupResponsive() {
        // 添加响应式包装器
        if (!this.table.closest('.table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            wrapper.style.cssText = `
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: 0.5rem;
                border: 1px solid #e2e8f0;
            `;
            
            this.table.parentNode.insertBefore(wrapper, this.table);
            wrapper.appendChild(this.table);
        }

        // 移动端堆叠样式
        if (this.options.stackOnMobile) {
            this.addStackingStyles();
        }
    }

    addStackingStyles() {
        const style = document.createElement('style');
        const tableId = this.table.id || `table-${Date.now()}`;
        if (!this.table.id) this.table.id = tableId;
        
        style.textContent = `
            @media (max-width: ${this.options.breakpoint}px) {
                #${tableId} {
                    border: 0;
                }
                
                #${tableId} thead {
                    display: none;
                }
                
                #${tableId} tr {
                    border-bottom: 3px solid #ddd;
                    display: block;
                    margin-bottom: 0.625em;
                }
                
                #${tableId} td {
                    border: none;
                    display: block;
                    font-size: 0.8em;
                    text-align: right;
                    padding-left: 50%;
                    position: relative;
                }
                
                #${tableId} td:before {
                    content: attr(data-label) ":";
                    position: absolute;
                    left: 6px;
                    width: 45%;
                    text-align: left;
                    font-weight: bold;
                }
            }
        `;
        
        document.head.appendChild(style);
        
        // 为每个td添加data-label属性
        const headers = Array.from(this.table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = this.table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, index) => {
                if (headers[index]) {
                    cell.setAttribute('data-label', headers[index]);
                }
            });
        });
    }

    setupEventListeners() {
        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            this.updateLayout();
        }, 100));
    }

    updateLayout() {
        // 可以在这里添加更多响应式逻辑
    }

    // 静态方法：为所有表格应用响应式处理
    static applyToAll(selector = 'table', options = {}) {
        const tables = document.querySelectorAll(selector);
        return Array.from(tables).map(table => new ResponsiveTable(table, options));
    }
}

// 创建全局实例
const responsiveManager = new ResponsiveManager();
const mobileAdapter = new MobileAdapter();

// 导出到全局UI对象
if (window.UI) {
    window.UI.responsive = responsiveManager;
    window.UI.mobile = mobileAdapter;
    window.UI.AdaptiveGrid = AdaptiveGrid;
    window.UI.ResponsiveImage = ResponsiveImage;
    window.UI.ResponsiveTable = ResponsiveTable;
}

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 自动应用响应式图片
    ResponsiveImage.applyToAll('img[data-responsive]');
    
    // 自动应用响应式表格
    ResponsiveTable.applyToAll('table[data-responsive]');
    
    // 自动应用自适应网格
    AdaptiveGrid.apply('[data-adaptive-grid]');
    
    console.log('响应式组件库已初始化');
});

// 导出模块（如果支持ES6模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ResponsiveManager,
        MobileAdapter,
        AdaptiveGrid,
        ResponsiveImage,
        ResponsiveTable
    };
}