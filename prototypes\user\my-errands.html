<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的跑腿 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .errand-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        .errand-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.2);
            border-color: rgba(99, 102, 241, 0.3);
        }
        .status-badge {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 顶部导航 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
        <div class="max-w-md mx-auto flex items-center space-x-3">
            <button onclick="history.back()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6 text-white"></i>
            </button>
            <h1 class="text-xl font-semibold text-white">我的跑腿</h1>
            <div class="flex-1"></div>
            <button onclick="window.location.href='publish-errands.html'" class="p-2 hover:bg-white/20 rounded-lg transition-colors" title="发布跑腿">
                <i data-lucide="plus" class="w-6 h-6 text-white"></i>
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="max-w-md mx-auto p-4">
        <div class="grid grid-cols-4 gap-3 mb-6">
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-blue-400">4</div>
                <div class="text-xs text-slate-400">全部</div>
            </div>
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-green-400">2</div>
                <div class="text-xs text-slate-400">进行中</div>
            </div>
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-yellow-400">1</div>
                <div class="text-xs text-slate-400">已完成</div>
            </div>
            <div class="bg-slate-800 rounded-xl p-3 text-center">
                <div class="text-lg font-bold text-red-400">1</div>
                <div class="text-xs text-slate-400">已取消</div>
            </div>
        </div>

        <!-- 筛选标签 -->
        <div class="flex space-x-2 mb-4 overflow-x-auto">
            <button onclick="filterByType('all')" class="filter-btn bg-blue-600 text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">全部</button>
            <button onclick="filterByType('delivery')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">代取快递</button>
            <button onclick="filterByType('shopping')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">代购</button>
            <button onclick="filterByType('transport')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">代驾</button>
            <button onclick="filterByType('other')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">其他</button>
        </div>

        <!-- 跑腿列表 -->
        <div class="space-y-4" id="errandsList">
            <!-- 代取快递 -->
            <div class="errand-card bg-slate-800 rounded-xl p-4" data-type="delivery">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=60&h=60&fit=crop" 
                         alt="快递" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1566576912321-d58ddd7a6088 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">代取快递</h3>
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">进行中</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">菜鸟驿站取件，酬劳：10元</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                                    <span>距离500m</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                    <span>2小时前</span>
                                </span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button onclick="contactRunner('1')" class="p-1.5 bg-blue-600/20 hover:bg-blue-600/30 rounded-lg transition-colors" title="联系跑腿员">
                                    <i data-lucide="phone" class="w-4 h-4 text-blue-400"></i>
                                </button>
                                <button onclick="viewProgress('1')" class="p-1.5 bg-green-600/20 hover:bg-green-600/30 rounded-lg transition-colors" title="查看进度">
                                    <i data-lucide="eye" class="w-4 h-4 text-green-400"></i>
                                </button>
                                <button onclick="cancelErrand('1')" class="p-1.5 bg-red-600/20 hover:bg-red-600/30 rounded-lg transition-colors" title="取消订单">
                                    <i data-lucide="x" class="w-4 h-4 text-red-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 代购 -->
            <div class="errand-card bg-slate-800 rounded-xl p-4" data-type="shopping">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=60&h=60&fit=crop" 
                         alt="代购" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1556909114-f6e7ad7d3136 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">超市代购</h3>
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">进行中</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">购买生活用品，预算：200元，酬劳：20元</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                                    <span>距离1.2km</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                    <span>1小时前</span>
                                </span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button onclick="contactRunner('2')" class="p-1.5 bg-blue-600/20 hover:bg-blue-600/30 rounded-lg transition-colors" title="联系跑腿员">
                                    <i data-lucide="phone" class="w-4 h-4 text-blue-400"></i>
                                </button>
                                <button onclick="viewProgress('2')" class="p-1.5 bg-green-600/20 hover:bg-green-600/30 rounded-lg transition-colors" title="查看进度">
                                    <i data-lucide="eye" class="w-4 h-4 text-green-400"></i>
                                </button>
                                <button onclick="cancelErrand('2')" class="p-1.5 bg-red-600/20 hover:bg-red-600/30 rounded-lg transition-colors" title="取消订单">
                                    <i data-lucide="x" class="w-4 h-4 text-red-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已完成的代驾 -->
            <div class="errand-card bg-slate-800 rounded-xl p-4" data-type="transport">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=60&h=60&fit=crop" 
                         alt="代驾" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1449824913935-59a10b8d2000 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">代驾服务</h3>
                            <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-medium">已完成</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">从酒店到家，距离15km，费用：80元</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="star" class="w-3 h-3 text-yellow-400"></i>
                                    <span>已评价</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                    <span>昨天</span>
                                </span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button onclick="viewDetails('3')" class="p-1.5 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors" title="查看详情">
                                    <i data-lucide="eye" class="w-4 h-4 text-slate-400"></i>
                                </button>
                                <button onclick="reorderErrand('3')" class="p-1.5 bg-blue-600/20 hover:bg-blue-600/30 rounded-lg transition-colors" title="再次下单">
                                    <i data-lucide="repeat" class="w-4 h-4 text-blue-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已取消的其他服务 -->
            <div class="errand-card bg-slate-800 rounded-xl p-4 opacity-60" data-type="other">
                <div class="flex items-start space-x-3">
                    <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=60&h=60&fit=crop" 
                         alt="其他服务" class="w-15 h-15 rounded-lg object-cover">
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1558618666-fcd25c85cd64 -->
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-white">排队代办</h3>
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-medium">已取消</span>
                        </div>
                        <p class="text-sm text-slate-400 mb-2">银行排队办理业务，酬劳：50元</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="circle-x" class="w-3 h-3"></i>
                                    <span>用户取消</span>
                                </span>
                                <span class="flex items-center space-x-1">
                                    <i data-lucide="clock" class="w-3 h-3"></i>
                                    <span>3天前</span>
                                </span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="viewDetails('4')" class="text-slate-400 hover:text-slate-300 transition-colors" title="查看详情">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </button>
                                <button onclick="reorderErrand('4')" class="text-blue-400 hover:text-blue-300 transition-colors" title="重新下单">
                                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="text-center py-12 hidden">
            <i data-lucide="truck" class="w-16 h-16 text-slate-600 mx-auto mb-4"></i>
            <p class="text-slate-400 mb-4">暂无相关跑腿订单</p>
            <button onclick="window.location.href='publish-errands.html'" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                发布跑腿
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布各类信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿需求</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 筛选功能
        function filterByType(type) {
            const cards = document.querySelectorAll('.errand-card');
            const buttons = document.querySelectorAll('.filter-btn');
            const emptyState = document.getElementById('emptyState');
            
            // 更新按钮状态
            buttons.forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            event.target.classList.remove('bg-slate-700', 'text-slate-300');
            event.target.classList.add('bg-blue-600', 'text-white');
            
            let visibleCount = 0;
            
            cards.forEach(card => {
                if (type === 'all' || card.dataset.type === type) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 显示/隐藏空状态
            if (visibleCount === 0) {
                emptyState.classList.remove('hidden');
            } else {
                emptyState.classList.add('hidden');
            }
        }

        // 联系跑腿员
        function contactRunner(id) {
            console.log('联系跑腿员:', id);
            alert('正在联系跑腿员...');
            // 实际应用中打开聊天界面或拨打电话
        }

        // 查看进度
        function viewProgress(id) {
            console.log('查看进度:', id);
            alert('跳转到进度详情页面');
            // 实际应用中跳转到进度页面
        }

        // 取消跑腿
        function cancelErrand(id) {
            if (confirm('确定要取消这个跑腿订单吗？')) {
                console.log('取消跑腿:', id);
                alert('订单已取消');
                // 实际应用中调用取消API
                location.reload();
            }
        }

        // 查看详情
        function viewDetails(id) {
            console.log('查看详情:', id);
            alert('跳转到订单详情页面');
            // 实际应用中跳转到详情页面
        }

        // 重新下单
        function reorderErrand(id) {
            console.log('重新下单:', id);
            alert('跳转到发布页面');
            // 实际应用中跳转到发布页面并预填信息
        }

        // 发布弹窗功能
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }

        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }

        function navigateToPublish(type) {
            closePublishModal();
            switch(type) {
                case 'shops':
                    window.location.href = 'publish-shop.html';
                    break;
                case 'information':
                    window.location.href = 'publish-information.html';
                    break;
                case 'transportation':
                    window.location.href = 'publish-transportation.html';
                    break;
                case 'errands':
                    window.location.href = 'publish-errands.html';
                    break;
            }
        }

        // 全局函数
        window.filterByType = filterByType;
        window.contactRunner = contactRunner;
        window.viewProgress = viewProgress;
        window.cancelErrand = cancelErrand;
        window.viewDetails = viewDetails;
        window.reorderErrand = reorderErrand;
        window.showPublishModal = showPublishModal;
        window.closePublishModal = closePublishModal;
        window.navigateToPublish = navigateToPublish;
    </script>
</body>
</html>