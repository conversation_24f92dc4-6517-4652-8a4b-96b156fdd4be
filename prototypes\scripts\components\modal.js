/**
 * 模态框组件
 * 基于组件注册系统的模态框实现
 */

class Modal {
    /**
     * 构造函数
     * @param {HTMLElement} element - 模态框元素
     * @param {Object} options - 配置选项
     */
    constructor(element, options) {
        this.element = element;
        this.options = {
            // 默认选项
            closeOnEscape: true,            // 按ESC关闭
            closeOnOverlayClick: true,      // 点击遮罩关闭
            showCloseButton: true,          // 显示关闭按钮
            animation: true,                // 启用动画
            animationDuration: 300,         // 动画持续时间(ms)
            backdrop: true,                 // 显示遮罩
            keyboard: true,                 // 键盘控制
            draggable: false,               // 是否可拖动
            width: null,                    // 宽度，为null则自适应
            position: 'center',             // 位置：center, top, custom
            onOpen: null,                   // 打开回调
            onClose: null,                  // 关闭回调
            ...options
        };
        
        // 内部状态
        this.isOpen = false;                // 是否打开
        this.isDragging = false;            // 是否正在拖拽
        this.originalPosition = {x: 0, y: 0}; // 原始位置
        
        // 元素引用
        this.overlay = null;                // 遮罩层
        this.dialog = this.element.querySelector('.modal-dialog');
        this.header = this.element.querySelector('.modal-header');
        this.closeButton = this.element.querySelector('.modal-close');
        this.content = this.element.querySelector('.modal-body');
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化模态框
     */
    init() {
        // 创建或配置遮罩
        this.setupOverlay();
        
        // 如果需要添加关闭按钮
        if (this.options.showCloseButton && !this.closeButton) {
            this.addCloseButton();
        }
        
        // 应用动画设置
        if (this.options.animation) {
            this.setupAnimation();
        }
        
        // 支持拖拽
        if (this.options.draggable && this.header) {
            this.setupDraggable();
        }
        
        // 应用自定义宽度
        if (this.options.width && this.dialog) {
            this.dialog.style.width = this.options.width + 'px';
            this.dialog.style.maxWidth = '100%';
        }
        
        // 设置位置
        this.setPosition(this.options.position);
        
        // 绑定事件
        this.bindEvents();
        
        // 添加ARIA属性以支持无障碍访问
        this.setupAccessibility();
        
        // 默认隐藏模态框
        this.element.style.display = 'none';
    }
    
    /**
     * 创建遮罩层
     */
    setupOverlay() {
        if (this.options.backdrop) {
            this.overlay = document.createElement('div');
            this.overlay.className = 'modal-overlay';
            
            // 将遮罩添加到模态框前面
            if (this.element.parentNode) {
                this.element.parentNode.insertBefore(this.overlay, this.element);
            } else {
                document.body.appendChild(this.overlay);
            }
            
            // 默认隐藏遮罩
            this.overlay.style.display = 'none';
        }
    }
    
    /**
     * 添加关闭按钮
     */
    addCloseButton() {
        if (this.header) {
            this.closeButton = document.createElement('button');
            this.closeButton.className = 'modal-close';
            this.closeButton.setAttribute('aria-label', '关闭');
            this.closeButton.innerHTML = '&times;';
            this.header.appendChild(this.closeButton);
        }
    }
    
    /**
     * 设置动画效果
     */
    setupAnimation() {
        const duration = this.options.animationDuration;
        
        this.element.style.transition = `opacity ${duration}ms ease`;
        if (this.dialog) {
            this.dialog.style.transition = `transform ${duration}ms ease, opacity ${duration}ms ease`;
        }
        if (this.overlay) {
            this.overlay.style.transition = `opacity ${duration}ms ease`;
        }
    }
    
    /**
     * 设置无障碍特性
     */
    setupAccessibility() {
        // 添加角色和状态属性
        this.element.setAttribute('role', 'dialog');
        this.element.setAttribute('aria-modal', 'true');
        this.element.setAttribute('aria-hidden', 'true');
        
        // 如果有标题，设置aria-labelledby
        const title = this.element.querySelector('.modal-header h1, .modal-header h2, .modal-header h3');
        if (title) {
            const titleId = title.id || 'modal-title-' + this.generateId();
            title.id = titleId;
            this.element.setAttribute('aria-labelledby', titleId);
        }
        
        // 如果有内容，设置aria-describedby
        if (this.content) {
            const contentId = this.content.id || 'modal-content-' + this.generateId();
            this.content.id = contentId;
            this.element.setAttribute('aria-describedby', contentId);
        }
    }
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }
    
    /**
     * 设置模态框位置
     * @param {string} position - 位置: center, top, custom
     */
    setPosition(position) {
        if (!this.dialog) return;
        
        switch (position) {
            case 'top':
                this.dialog.style.marginTop = '20px';
                this.dialog.style.marginBottom = 'auto';
                break;
            case 'center':
                this.dialog.style.margin = 'auto';
                break;
            case 'custom':
                // 自定义位置由外部设置
                break;
            default:
                this.dialog.style.margin = 'auto';
        }
    }
    
    /**
     * 设置拖拽功能
     */
    setupDraggable() {
        if (!this.header) return;
        
        this.header.style.cursor = 'move';
        this.header.setAttribute('data-draggable', 'true');
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭按钮事件
        if (this.closeButton) {
            this.closeButton.addEventListener('click', this.close.bind(this));
        }
        
        // 点击遮罩关闭
        if (this.overlay && this.options.closeOnOverlayClick) {
            this.overlay.addEventListener('click', this.close.bind(this));
        }
        
        // 按ESC关闭
        if (this.options.closeOnEscape) {
            document.addEventListener('keydown', this.handleKeyDown.bind(this));
        }
        
        // 拖拽相关事件
        if (this.options.draggable && this.header) {
            this.header.addEventListener('mousedown', this.startDrag.bind(this));
            document.addEventListener('mousemove', this.doDrag.bind(this));
            document.addEventListener('mouseup', this.stopDrag.bind(this));
            
            // 触摸事件支持
            this.header.addEventListener('touchstart', this.startDrag.bind(this));
            document.addEventListener('touchmove', this.doDrag.bind(this));
            document.addEventListener('touchend', this.stopDrag.bind(this));
        }
        
        // 防止点击对话框内容时关闭
        if (this.dialog) {
            this.dialog.addEventListener('click', (e) => e.stopPropagation());
        }
    }
    
    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyDown(event) {
        if (this.isOpen && event.key === 'Escape' && this.options.keyboard) {
            this.close();
        }
    }
    
    /**
     * 开始拖拽
     * @param {MouseEvent|TouchEvent} event - 鼠标或触摸事件
     */
    startDrag(event) {
        if (!this.options.draggable || !this.dialog) return;
        
        event.preventDefault();
        
        // 获取事件坐标
        const clientX = event.clientX || (event.touches && event.touches[0].clientX);
        const clientY = event.clientY || (event.touches && event.touches[0].clientY);
        
        if (clientX === undefined || clientY === undefined) return;
        
        this.isDragging = true;
        
        // 计算鼠标点击位置和对话框位置的差值
        const rect = this.dialog.getBoundingClientRect();
        this.dragOffsetX = clientX - rect.left;
        this.dragOffsetY = clientY - rect.top;
        
        // 设置对话框为绝对定位以便拖动
        this.dialog.style.position = 'absolute';
        this.dialog.style.margin = '0';
        this.dialog.style.left = rect.left + 'px';
        this.dialog.style.top = rect.top + 'px';
    }
    
    /**
     * 执行拖拽
     * @param {MouseEvent|TouchEvent} event - 鼠标或触摸事件
     */
    doDrag(event) {
        if (!this.isDragging || !this.dialog) return;
        
        event.preventDefault();
        
        // 获取事件坐标
        const clientX = event.clientX || (event.touches && event.touches[0].clientX);
        const clientY = event.clientY || (event.touches && event.touches[0].clientY);
        
        if (clientX === undefined || clientY === undefined) return;
        
        // 计算新位置
        const newLeft = clientX - this.dragOffsetX;
        const newTop = clientY - this.dragOffsetY;
        
        // 应用新位置
        this.dialog.style.left = newLeft + 'px';
        this.dialog.style.top = newTop + 'px';
    }
    
    /**
     * 结束拖拽
     */
    stopDrag() {
        this.isDragging = false;
    }
    
    /**
     * 打开模态框
     */
    open() {
        if (this.isOpen) return;
        
        // 显示模态框和遮罩
        this.element.style.display = 'flex';
        if (this.overlay) {
            this.overlay.style.display = 'block';
        }
        
        // 防止背景滚动
        document.body.classList.add('modal-open');
        
        // 设置为已打开状态
        this.isOpen = true;
        this.element.setAttribute('aria-hidden', 'false');
        
        // 添加动画类
        if (this.options.animation) {
            // 使用setTimeout确保CSS过渡生效
            setTimeout(() => {
                this.element.style.opacity = '1';
                if (this.dialog) {
                    this.dialog.style.transform = 'translateY(0)';
                    this.dialog.style.opacity = '1';
                }
                if (this.overlay) {
                    this.overlay.style.opacity = '1';
                }
            }, 10);
        }
        
        // 焦点管理
        this.manageFocus();
        
        // 调用回调函数
        if (typeof this.options.onOpen === 'function') {
            this.options.onOpen(this);
        }
        
        // 触发事件
        this.element.dispatchEvent(new CustomEvent('modal:open', { detail: { modal: this } }));
    }
    
    /**
     * 关闭模态框
     */
    close() {
        if (!this.isOpen) return;
        
        if (this.options.animation) {
            // 启动关闭动画
            this.element.style.opacity = '0';
            if (this.dialog) {
                this.dialog.style.transform = 'translateY(-20px)';
                this.dialog.style.opacity = '0';
            }
            if (this.overlay) {
                this.overlay.style.opacity = '0';
            }
            
            // 等待动画完成后隐藏
            setTimeout(() => {
                this.hideModal();
            }, this.options.animationDuration);
        } else {
            this.hideModal();
        }
        
        // 调用回调函数
        if (typeof this.options.onClose === 'function') {
            this.options.onClose(this);
        }
        
        // 触发事件
        this.element.dispatchEvent(new CustomEvent('modal:close', { detail: { modal: this } }));
    }
    
    /**
     * 隐藏模态框
     */
    hideModal() {
        this.element.style.display = 'none';
        if (this.overlay) {
            this.overlay.style.display = 'none';
        }
        
        // 恢复背景滚动
        document.body.classList.remove('modal-open');
        
        // 设置为关闭状态
        this.isOpen = false;
        this.element.setAttribute('aria-hidden', 'true');
    }
    
    /**
     * 管理焦点
     */
    manageFocus() {
        // 寻找第一个可聚焦元素
        const focusableElements = this.element.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length > 0) {
            // 保存当前焦点，以便关闭时恢复
            this.previouslyFocused = document.activeElement;
            
            // 聚焦第一个元素
            focusableElements[0].focus();
            
            // 设置焦点陷阱
            this.setupFocusTrap(focusableElements);
        }
    }
    
    /**
     * 设置焦点陷阱，防止Tab键导航跳出模态框
     * @param {NodeList} focusableElements - 可聚焦元素列表
     */
    setupFocusTrap(focusableElements) {
        if (focusableElements.length === 0) return;
        
        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];
        
        this.element.addEventListener('keydown', (e) => {
            if (e.key !== 'Tab') return;
            
            if (e.shiftKey) {
                // Shift + Tab: 如果焦点在第一个元素上，则循环到最后一个
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                // Tab: 如果焦点在最后一个元素上，则循环到第一个
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        });
    }
    
    /**
     * 设置模态框内容
     * @param {string|HTMLElement} content - 模态框内容
     */
    setContent(content) {
        if (!this.content) return;
        
        if (typeof content === 'string') {
            this.content.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            this.content.innerHTML = '';
            this.content.appendChild(content);
        }
    }
    
    /**
     * 设置模态框标题
     * @param {string} title - 标题文本
     */
    setTitle(title) {
        const titleElement = this.element.querySelector('.modal-header h1, .modal-header h2, .modal-header h3');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }
    
    /**
     * 销毁模态框，清理资源
     */
    destroy() {
        // 如果是打开状态，先关闭
        if (this.isOpen) {
            this.hideModal();
        }
        
        // 移除事件监听
        if (this.closeButton) {
            this.closeButton.removeEventListener('click', this.close);
        }
        
        if (this.overlay) {
            this.overlay.removeEventListener('click', this.close);
        }
        
        document.removeEventListener('keydown', this.handleKeyDown);
        
        if (this.options.draggable && this.header) {
            this.header.removeEventListener('mousedown', this.startDrag);
            document.removeEventListener('mousemove', this.doDrag);
            document.removeEventListener('mouseup', this.stopDrag);
            
            this.header.removeEventListener('touchstart', this.startDrag);
            document.removeEventListener('touchmove', this.doDrag);
            document.removeEventListener('touchend', this.stopDrag);
        }
        
        if (this.dialog) {
            this.dialog.removeEventListener('click', e => e.stopPropagation());
        }
        
        // 移除遮罩层
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
    }
}

// 注册模态框组件
if (window.componentRegistry) {
    window.componentRegistry.register('modal', Modal, {
        // 组件配置选项
        selector: '[data-component="modal"]',
        multiple: true, 
        autoInit: false // 默认不自动初始化，需要手动调用打开
    });
} else {
    console.warn('ComponentRegistry not found. Modal component could not be registered.');
}

// 导出模态框类
window.Modal = Modal;
