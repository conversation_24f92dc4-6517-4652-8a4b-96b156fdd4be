<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地信息 - 同城服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/common.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .safe-area-pb { padding-bottom: env(safe-area-inset-bottom); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.3); }
        .info-tag { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .line-clamp-2 { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }
        .description-expanded .line-clamp-2 { -webkit-line-clamp: unset; }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 to-slate-800 min-h-screen text-white">
    <!-- 顶部搜索区域 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center space-x-3">
                <!-- 城市选择按钮 -->
                <button class="flex items-center space-x-1 bg-slate-700 px-3 py-2 rounded-lg text-sm font-medium text-slate-300 hover:bg-slate-600 transition-colors">
                    <i data-lucide="map-pin" class="w-4 h-4"></i>
                    <span>深圳</span>
                    <i data-lucide="chevron-down" class="w-3 h-3"></i>
                </button>
                
                <!-- 搜索输入框 -->
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索本地信息..." 
                           class="w-full bg-slate-700 border-0 rounded-lg px-4 py-2 pl-10 pr-4 text-sm text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:bg-slate-600 transition-all">
                    <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                </div>
                
                <!-- 扫一扫按钮 -->
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" onclick="openQRScanner()">
                    <i data-lucide="qr-code" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 四大板块导航 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-between py-3">
                <button onclick="window.location.href='shops.html'" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                <button class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg bg-purple-600 text-white font-medium">
                    <i data-lucide="info" class="w-5 h-5"></i>
                    <span class="text-xs">信息</span>
                </button>
                <button onclick="window.location.href='transportation.html'" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="car" class="w-5 h-5"></i>
                    <span class="text-xs">出行</span>
                </button>
                <button onclick="window.location.href='errands.html'" class="flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400 hover:bg-slate-700 hover:text-slate-300 transition-colors">
                    <i data-lucide="package" class="w-5 h-5"></i>
                    <span class="text-xs">跑腿</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 信息分类筛选 -->
    <div class="bg-slate-800 border-b border-slate-700">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="space-y-2">
                <!-- 第一行：5个分类 -->
                <div class="grid grid-cols-5 gap-2">
                    <button onclick="filterCategory('all')" class="category-filter active px-3 py-2 bg-purple-600 text-white rounded-full text-sm font-medium">
                        全部
                    </button>
                    <button onclick="filterCategory('recruitment')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        招聘
                    </button>
                    <button onclick="filterCategory('rental')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        出租
                    </button>
                    <button onclick="filterCategory('rent-seeking')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        求租
                    </button>
                    <button onclick="filterCategory('selling')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        出售
                    </button>
                </div>
                <!-- 第二行：空位 + 4个分类 -->
                <div class="grid grid-cols-5 gap-2">
                    <div></div> <!-- 空位，与"全部"对应 -->
                    <button onclick="filterCategory('second-hand')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        闲置
                    </button>
                    <button onclick="filterCategory('buying')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        求购
                    </button>
                    <button onclick="filterCategory('job-seeking')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        求职
                    </button>
                    <button onclick="filterCategory('others')" class="category-filter px-3 py-2 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors">
                        其他
                    </button>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-md mx-auto px-4 pt-4 pb-24 space-y-4">
        <!-- 安全出行提醒 -->
        <div id="info-banner" class="bg-purple-500/10 border border-purple-500/30 rounded-lg p-3 relative">
            <div class="flex items-start space-x-3">
                <i data-lucide="shield-check" class="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0"></i>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-purple-400 mb-2">安全信息提醒</h4>
                    <div class="text-xs text-purple-300/90 space-y-1">
                        <p>• 乘车前请核实司机身份和车辆信息</p>
                        <p>• 建议选择白天出行，避免深夜独自乘车</p>
                        <p>• 上车后请系好安全带，注意行车安全</p>
                        <p>• 如遇紧急情况请及时报警求助</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <span id="countdown-timer" class="text-xs text-purple-400 font-mono">5s</span>
                    <button id="close-banner-btn" class="text-purple-400 hover:text-purple-200">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 信息列表 -->
        <div class="space-y-4">
        <!-- 广告卡片 - 小尺寸 -->
        <div class="bg-gradient-to-r from-purple-900 to-pink-900 rounded-xl shadow-md border border-purple-700 p-3 relative">
            <div class="absolute top-0 right-0 text-yellow-400 text-xs px-2 py-1 font-bold flex items-center space-x-1">
                <i data-lucide="megaphone" class="w-3 h-3"></i>
                <span>广告</span>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1497366216548-37526070297c?w=140&h=105&fit=crop" 
                         alt="广告图片" class="w-full h-full object-cover">
                </div>
                
                <div class="flex-1">
                    <h3 class="font-bold text-white text-base mb-1">高薪诚聘Web前端开发工程师</h3>
                    <div class="flex flex-wrap gap-1 mb-1">
                        <span class="text-yellow-400 text-xs font-medium flex items-center space-x-1">
                            <i data-lucide="briefcase" class="w-3 h-3"></i>
                            <span>科技公司</span>
                        </span>
                    </div>
                    <p class="text-xs text-pink-200 line-clamp-2">快速发展的互联网公司，寻找优秀前端开发工程师，具有竞争力薪资，五险一金，弹性工作制。</p>
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-yellow-400 text-xs font-bold">¥25K-35K</span>
                        <button class="text-xs px-2 py-1 bg-yellow-500 text-slate-900 rounded-lg hover:bg-yellow-600 transition-colors">
                            立即申请
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 租房信息卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="rental" onclick="viewInfoDetail('rental', '精装一室一厅出租')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">精装一室一厅出租</h3>
                <span class="bg-red-600 text-white px-3 py-1 rounded-lg text-lg font-bold">¥2,800/月</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-purple-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="home" class="w-3 h-3"></i>
                    <span>出租</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="star" class="w-3 h-3"></i>
                    <span>推荐</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>2.1km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效14天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    精装修，家具家电齐全，拎包入住。小区环境优美，交通便利，近地铁站。房间朝南采光好，24小时热水，独立卫生间。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=140&h=105&fit=crop" 
                     alt="房间" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=140&h=105&fit=crop" 
                     alt="客厅" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=140&h=105&fit=crop" 
                     alt="厨房" class="w-24 h-18 rounded-lg object-cover">
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>福田区车公庙地铁站附近小区</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 二手物品卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="second-hand" onclick="viewInfoDetail('second-hand', 'iPhone 14 Pro 256G 深空黑')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">iPhone 14 Pro 256G 深空黑</h3>
                <span class="bg-blue-600 text-white px-3 py-1 rounded-lg text-lg font-bold">¥6,800</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="tag" class="w-3 h-3"></i>
                    <span>闲置</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="smartphone" class="w-3 h-3"></i>
                    <span>二手</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>3.8km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效7天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    9成新，无磕碰，功能正常，配件齐全。原装充电器、数据线、耳机都在。因换新机出售，诚心转让。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=140&h=105&fit=crop" 
                     alt="手机正面" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=140&h=105&fit=crop" 
                     alt="手机背面" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1585060544812-6b45742d762f?w=140&h=105&fit=crop" 
                     alt="配件" class="w-24 h-18 rounded-lg object-cover">
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>罗湖区东门步行街商圈</span>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- 小型广告卡片 -->
        <div class="bg-gradient-to-r from-green-900 to-teal-900 rounded-xl shadow-md border border-green-700 p-3 relative">
            <div class="absolute top-0 right-0 text-yellow-400 text-xs px-2 py-1 font-bold flex items-center space-x-1">
                <i data-lucide="megaphone" class="w-3 h-3"></i>
                <span>广告</span>
            </div>
            
            <div class="flex items-start space-x-3">
                <div class="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=140&h=105&fit=crop" 
                         alt="广告图片" class="w-full h-full object-cover">
                </div>
                
                <div class="flex-1">
                    <h3 class="font-bold text-white text-base mb-1">高端公寓特惠出租</h3>
                    <div class="flex flex-wrap gap-1 mb-1">
                        <span class="text-green-400 text-xs font-medium flex items-center space-x-[6px]">
                            <i data-lucide="home" class="w-3 h-3"></i>
                            <span>精装公寓</span>
                        </span>
                    </div>
                    <p class="text-xs text-green-200 line-clamp-2">市中心豪华公寓，全新装修，家电齐全，拎包入住，首月半价。</p>
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-yellow-400 text-xs font-bold">¥3,500/月</span>
                        <button class="text-xs px-2 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 求职信息卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="job-seeking" onclick="viewInfoDetail('job-seeking', '寻找UI设计师职位')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">寻找UI设计师职位</h3>
                <span class="bg-purple-600 text-white px-3 py-1 rounded-lg text-lg font-bold">期望¥8K-12K</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-red-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="search" class="w-3 h-3"></i>
                    <span>求职</span>
                </span>
                <span class="text-gray-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield" class="w-3 h-3"></i>
                    <span>未认证</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>1.8km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效30天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    熟练掌握Figma、Sketch等设计工具，有移动端和Web端设计经验，作品集丰富。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>南山区科技园</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 招聘信息卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="recruitment" onclick="viewInfoDetail('recruitment', '招聘前端开发工程师')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">招聘前端开发工程师</h3>
                <span class="bg-green-600 text-white px-3 py-1 rounded-lg text-lg font-bold">¥12K-18K</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-purple-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="briefcase" class="w-3 h-3"></i>
                    <span>招聘</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>0.5km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效15天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    要求熟练掌握Vue.js、React等前端框架，有3年以上开发经验，五险一金，弹性工作制。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>南山区深圳湾科技生态园</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 家政服务卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="housekeeping" onclick="viewInfoDetail('housekeeping', '专业家政清洁服务')">
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">专业家政清洁服务</h3>
                <span class="bg-orange-600 text-white px-3 py-1 rounded-lg text-lg font-bold">¥80/小时</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-teal-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="spray-can" class="w-3 h-3"></i>
                    <span>家政</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>1.2km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效20天</span>
                </span>
            </div>
            
            <div class="mb-3">
                <p class="text-sm text-slate-300">
                    专业家政团队，提供深度清洁、日常保洁、开荒保洁等服务。工具齐全，价格合理，服务周到。
                </p>
            </div>
            
            <div class="flex space-x-2 mb-3">
                <img src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=140&h=105&fit=crop" 
                     alt="清洁工具" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=140&h=105&fit=crop" 
                     alt="清洁现场" class="w-24 h-18 rounded-lg object-cover">
            </div>
            
            <div class="flex items-center justify-between text-xs">
                <span class="text-slate-400 flex items-center space-x-[6px]">
                    <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                    <span>福田区华强北商圈</span>
                </span>
            </div>
        </div>

        <!-- 求租信息卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="rent-seeking" onclick="viewInfoDetail('rent-seeking', '求租一居室')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">求租一居室</h3>
                <span class="bg-blue-600 text-white px-3 py-1 rounded-lg text-lg font-bold">预算¥3K-4K</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-blue-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="home" class="w-3 h-3"></i>
                    <span>求租</span>
                </span>
                <span class="text-gray-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield" class="w-3 h-3"></i>
                    <span>未认证</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>2.5km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效10天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    本人单身，工作稳定，求租朝阳区一居室，预算3000-4000元，爱干净，无不良嗜好。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>朝阳区国贸商圈</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 出售信息卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="selling" onclick="viewInfoDetail('selling', '全新MacBook Pro')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">全新MacBook Pro</h3>
                <span class="bg-yellow-600 text-white px-3 py-1 rounded-lg text-lg font-bold">¥12,800</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="tag" class="w-3 h-3"></i>
                    <span>出售</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield-check" class="w-3 h-3"></i>
                    <span>已认证</span>
                </span>
                <span class="text-cyan-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="laptop" class="w-3 h-3"></i>
                    <span>电子产品</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>4.2km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效15天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    M2芯片，16GB内存，512GB存储，全新未拆封，因工作变动急售，价格可小刀。
                </p>
            </div>
            
            <!-- 图片展示区域 -->
            <div class="flex space-x-2 mb-4">
                <img src="https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=140&h=105&fit=crop" 
                     alt="MacBook" class="w-24 h-18 rounded-lg object-cover">
                <img src="https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=140&h=105&fit=crop" 
                     alt="包装盒" class="w-24 h-18 rounded-lg object-cover">
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>西城区金融街</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 求购信息卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4 card-hover cursor-pointer" data-category="buying" onclick="viewInfoDetail('buying', '求购二手相机')">
            <!-- 标题区域 -->
            <div class="mb-3 flex items-center">
                <h3 class="font-bold text-white text-lg mr-2">求购二手相机</h3>
                <span class="bg-cyan-600 text-white px-3 py-1 rounded-lg text-lg font-bold">预算¥5K</span>
            </div>
            
            <!-- 标签区域 -->
            <div class="flex flex-wrap gap-0.5 mb-3">
                <span class="text-cyan-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shopping-cart" class="w-3 h-3"></i>
                    <span>求购</span>
                </span>
                <span class="text-gray-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="shield" class="w-3 h-3"></i>
                    <span>未认证</span>
                </span>
                <span class="text-yellow-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="camera" class="w-3 h-3"></i>
                    <span>相机</span>
                </span>
                <span class="text-orange-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="map-pin" class="w-3 h-3"></i>
                    <span>3.1km</span>
                </span>
                <span class="text-green-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="phone" class="w-3 h-3"></i>
                    <span>电话</span>
                </span>
                <span class="text-amber-400 text-xs px-1.5 py-1 font-medium flex items-center space-x-0.5">
                    <i data-lucide="clock" class="w-3 h-3"></i>
                    <span>有效25天</span>
                </span>
            </div>
            
            <!-- 描述区域 -->
            <div class="mb-3">
                <p class="text-sm text-slate-300 leading-relaxed">
                    求购佳能或尼康单反相机，成色8成新以上，价格合理即可，摄影爱好者诚心求购。
                </p>
            </div>
            
            <!-- 附加信息区域 -->
            <div class="mb-4">
                <div class="flex items-center justify-between text-xs">
                    <span class="text-slate-400 flex items-center space-x-[6px]">
                        <i data-lucide="map-pin" class="w-3 h-3 text-slate-400"></i>
                        <span>东城区王府井</span>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 信息真实性提示 -->
    <div id="info-reminder" class="max-w-md mx-auto px-4 mb-4 transition-opacity duration-500 opacity-0">
        <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-3 relative">
            <button id="close-info-reminder" class="absolute top-2 right-2 text-slate-500 hover:text-slate-300">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i data-lucide="info" class="w-4 h-4 text-white"></i>
                </div>
                <div class="flex-1">
                    <h4 class="text-white font-medium mb-1">信息安全提醒</h4>
                    <p class="text-slate-300 text-sm">请注意核实信息真实性，谨防诈骗，交易时请选择安全的交易方式。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button onclick="window.location.href='shops.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-purple-400">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs font-medium">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-xl max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布本地信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿任务</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-700 text-white rounded-xl hover:bg-slate-600 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 标签切换功能
        function switchTab(tab) {
            const tabs = {
                'shops': 'shops.html',
                'information': 'information.html',
                'transportation': 'transportation.html',
                'errands': 'errands.html'
            };
            
            if (tabs[tab] && tab !== 'information') {
                window.location.href = tabs[tab];
            }
        }

        // 扫一扫功能
        function openQRScanner() {
            showToast('扫一扫功能开发中...');
        }

        // 分类筛选功能
        function filterCategory(category) {
            // 重置所有按钮状态
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.classList.remove('bg-purple-600', 'text-white', 'font-medium', 'active');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            // 设置当前按钮为选中状态（信息板块使用紫色）
            event.target.classList.remove('bg-slate-700', 'text-slate-300');
            event.target.classList.add('bg-purple-600', 'text-white', 'font-medium', 'active');
            
            // 获取所有信息卡片
            const cards = document.querySelectorAll('[data-category]');
            let visibleCount = 0;
            
            // 显示/隐藏卡片
            cards.forEach(card => {
                const cardCategory = card.dataset.category;
                let shouldShow = false;
                
                if (category === 'all') {
                    shouldShow = true;
                } else {
                    // 根据分类映射关系判断
                    switch(category) {
                        case 'recruitment':
                            shouldShow = cardCategory === 'recruitment';
                            break;
                        case 'rental':
                            shouldShow = cardCategory === 'rental';
                            break;
                        case 'rent-seeking':
                            shouldShow = cardCategory === 'rent-seeking';
                            break;
                        case 'selling':
                            shouldShow = cardCategory === 'selling';
                            break;
                        case 'second-hand':
                            shouldShow = cardCategory === 'second-hand';
                            break;
                        case 'buying':
                            shouldShow = cardCategory === 'buying';
                            break;
                        case 'job-seeking':
                            shouldShow = cardCategory === 'job-seeking';
                            break;
                        case 'others':
                            shouldShow = !['recruitment', 'rental', 'rent-seeking', 'selling', 'second-hand', 'buying', 'job-seeking'].includes(cardCategory);
                            break;
                        default:
                            shouldShow = cardCategory === category;
                    }
                }
                
                if (shouldShow) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 如果没有匹配的卡片，在控制台输出提示
            if (visibleCount === 0 && category !== 'all') {
                console.log(`没有找到分类为 "${category}" 的信息`);
            }
        }

        // 收藏功能已移至公共脚本 common.js

        // 展开/收起功能已移除

        // 拨打电话和导航功能已移至公共脚本 common.js
        
        // 电话按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            document.querySelectorAll('[data-lucide="phone"]').forEach(button => {
                button.parentElement.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    console.log('点击电话按钮');
                    // 模拟电话号码，实际应用中应从数据中获取
                    const phoneNumber = '13800138000';
                    callPhone(phoneNumber); // 使用公共函数
                });
            });
            
            // 距离点击导航事件
            document.querySelectorAll('span').forEach(span => {
                if (span.textContent.includes('距离') && (span.textContent.includes('m') || span.textContent.includes('km'))) {
                    span.style.cursor = 'pointer';
                    span.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // 获取最近的信息卡片
                        const infoCard = span.closest('[data-category]');
                        if (infoCard) {
                            const titleElement = infoCard.querySelector('h3');
                            const destination = titleElement ? titleElement.textContent.trim() : '目标位置';
                            openNavigation(destination);
                        }
                    });
                }
            });
        });
        
        // 导航功能已移至公共脚本 common.js

        // 查看信息详情
        function viewInfoDetail(category, title) {
            showToast(`查看详情：${title}`);
        }

        // 显示发布弹窗
        function showPublishModal() {
            document.getElementById('publishModal').classList.remove('hidden');
        }

        // 关闭发布弹窗
        function closePublishModal() {
            document.getElementById('publishModal').classList.add('hidden');
        }

        // 导航到发布页面
        function navigateToPublish(type) {
            const publishPages = {
                'shops': 'publish-shop.html',
                'information': 'publish-information.html',
                'transportation': 'publish-transportation.html',
                'errands': 'publish-errands.html'
            };
            
            if (publishPages[type]) {
                window.location.href = publishPages[type];
            }
        }

        // 显示AI帮助
        function showAIHelp() {
            showToast('AI助手功能开发中...');
        }

        // 自动关闭底部提醒（不影响导航栏显示）
        document.addEventListener('DOMContentLoaded', function() {
            const reminder = document.getElementById('info-reminder');
            const closeButton = document.getElementById('close-info-reminder');

            if (reminder) {
                // 显示提醒
                reminder.classList.remove('opacity-0');
                
                // 3秒后自动关闭提醒
                setTimeout(() => {
                    reminder.style.display = 'none';
                }, 3000);

                // 手动关闭提醒
                closeButton.addEventListener('click', () => {
                    reminder.style.display = 'none';
                });
            }
        });

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 transform translate-x-full`;
            
            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    toast.classList.add('bg-green-500');
                    break;
                case 'error':
                    toast.classList.add('bg-red-500');
                    break;
                case 'warning':
                    toast.classList.add('bg-yellow-500');
                    break;
                default:
                    toast.classList.add('bg-blue-500');
            }
            
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }


    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const banner = document.getElementById('info-banner');
            if (!banner) return;

            const closeBtn = document.getElementById('close-banner-btn');
            const countdownTimer = document.getElementById('countdown-timer');
            
            let countdown = 5;
            
            const interval = setInterval(() => {
                countdown--;
                if (countdownTimer) {
                    countdownTimer.textContent = `${countdown}s`;
                }
                if (countdown <= 0) {
                    clearInterval(interval);
                    banner.style.transition = 'opacity 0.5s ease';
                    banner.style.opacity = '0';
                    setTimeout(() => banner.style.display = 'none', 500);
                }
            }, 1000);

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    clearInterval(interval);
                    banner.style.transition = 'opacity 0.5s ease';
                    banner.style.opacity = '0';
                    setTimeout(() => banner.style.display = 'none', 500);
                });
            }
            
            lucide.createIcons();
        });
    </script>
</body>
</html>