# 组件注册系统使用指南

## 1. 概述

组件注册系统（Component Registry）是一个统一的组件管理框架，用于规范化组件的注册、初始化和生命周期管理。该系统解决了以下问题：

- 减少重复的组件初始化代码
- 提供统一的组件生命周期管理
- 支持动态加载和销毁组件
- 减少全局变量污染
- 提高代码可维护性

## 2. 基本使用

### 2.1 引入组件注册系统

在HTML文件的`<head>`标签中引入组件注册系统：

```html
<!-- 先引入基础依赖 -->
<script src="scripts/config.js"></script>
<script src="scripts/logger.js"></script>
<script src="scripts/common.js"></script>
<!-- 再引入组件注册系统 -->
<script src="scripts/component-registry.js"></script>
```

### 2.2 定义组件

创建一个简单的组件类：

```javascript
// 轮播图组件示例
class Carousel {
    constructor(element, options) {
        this.element = element;
        this.options = {
            autoPlay: true,
            interval: 3000,
            ...options
        };
        
        this.slides = element.querySelectorAll('.carousel-slide');
        this.currentIndex = 0;
        this.interval = null;
        
        this.init();
    }
    
    init() {
        // 创建导航点
        this.createNavDots();
        // 绑定事件
        this.bindEvents();
        // 如果设置了自动播放，启动定时器
        if (this.options.autoPlay) {
            this.startAutoPlay();
        }
    }
    
    createNavDots() {
        // 创建导航点的代码
    }
    
    bindEvents() {
        // 绑定事件的代码
    }
    
    goToSlide(index) {
        // 切换到指定幻灯片的代码
    }
    
    startAutoPlay() {
        // 启动自动播放
        this.interval = setInterval(() => {
            this.next();
        }, this.options.interval);
    }
    
    stopAutoPlay() {
        // 停止自动播放
        clearInterval(this.interval);
    }
    
    next() {
        // 下一张幻灯片
    }
    
    prev() {
        // 上一张幻灯片
    }
    
    // 销毁方法 - 组件注册系统会调用
    destroy() {
        this.stopAutoPlay();
        // 移除事件监听器
        // 清理其他资源
    }
}
```

### 2.3 注册组件

在脚本文件中注册组件：

```javascript
// 注册轮播图组件
componentRegistry.register('carousel', Carousel, {
    // 可选的自定义选项
    selector: '[data-component="carousel"]', // 默认使用组件名称作为选择器
    multiple: true, // 允许多个实例
    autoInit: true // 自动初始化
});
```

### 2.4 在HTML中使用组件

```html
<!-- 使用data-component属性标记组件 -->
<div class="carousel-container" data-component="carousel">
    <!-- 使用data-config-*属性配置组件 -->
    <div class="carousel" data-config-auto-play="true" data-config-interval="5000">
        <div class="carousel-slide">幻灯片1</div>
        <div class="carousel-slide">幻灯片2</div>
        <div class="carousel-slide">幻灯片3</div>
    </div>
    <button class="carousel-prev">上一张</button>
    <button class="carousel-next">下一张</button>
</div>
```

## 3. 高级特性

### 3.1 组件生命周期钩子

组件注册系统提供了四个生命周期钩子：

- `beforeInit`: 组件初始化前
- `afterInit`: 组件初始化后
- `beforeDestroy`: 组件销毁前
- `afterDestroy`: 组件销毁后

使用示例：

```javascript
// 添加初始化后钩子
componentRegistry.addHook('afterInit', ({ componentName, instance }) => {
    console.log(`组件 ${componentName} 初始化完成`);
    
    // 对特定组件进行操作
    if (componentName === 'carousel') {
        // 对轮播图组件进行额外设置
    }
});
```

### 3.2 手动操作组件实例

获取组件实例：

```javascript
// 通过元素获取实例
const carouselElement = document.querySelector('.carousel-container');
const carousel = componentRegistry.getInstance(carouselElement);

// 或者通过ID获取实例（如果知道实例ID）
const carousel = componentRegistry.getInstance('carousel_12345');

// 调用组件方法
if (carousel) {
    carousel.stopAutoPlay();
    carousel.goToSlide(2);
}
```

查找组件实例：

```javascript
// 查找包含某个元素的组件
const slideElement = document.querySelector('.carousel-slide');
const carousel = componentRegistry.findInstance(slideElement, 'carousel');
```

### 3.3 动态创建和销毁组件

动态创建组件后刷新注册表：

```javascript
// 创建新的轮播图容器
const container = document.createElement('div');
container.className = 'carousel-container';
container.dataset.component = 'carousel';
container.innerHTML = `
    <div class="carousel">
        <div class="carousel-slide">新幻灯片1</div>
        <div class="carousel-slide">新幻灯片2</div>
    </div>
`;

// 添加到DOM
document.body.appendChild(container);

// 刷新组件注册表，初始化新组件
componentRegistry.refresh();
```

手动销毁组件：

```javascript
// 获取实例ID
const instanceId = carouselElement.dataset.componentId;

// 销毁实例
if (instanceId) {
    componentRegistry.destroyInstance(instanceId);
}

// 或者销毁所有组件实例
componentRegistry.destroyAll();
```

### 3.4 禁用自动初始化

对于需要手动初始化的组件：

```javascript
// 注册时禁用自动初始化
componentRegistry.register('complexComponent', ComplexComponent, {
    autoInit: false
});

// 稍后手动初始化特定组件
function initializeComplexComponents() {
    componentRegistry.initComponentType('complexComponent');
}

// 在某个事件后调用
document.getElementById('loadMore').addEventListener('click', initializeComplexComponents);
```

## 4. 最佳实践

### 4.1 组件命名规范

- 使用驼峰命名法定义组件类名（如`Carousel`，`TabPanel`）
- 使用小驼峰或连字符命名组件标识符（如`carousel`，`tab-panel`）
- 一个组件类通常对应一个独立的JS文件

### 4.2 组件设计原则

- 单一职责：一个组件只负责一种功能
- 封装内部状态：不要暴露内部实现细节
- 提供清晰的公共API：方便外部调用
- 实现销毁方法：清理事件监听器和定时器
- 使用选项合并：合并默认选项和用户选项

### 4.3 组件依赖管理

当组件之间存在依赖关系时：

```javascript
// 父组件需要子组件
class TabPanel {
    constructor(element, options) {
        // ...初始化代码
        
        // 获取子组件
        this.tabs = Array.from(element.querySelectorAll('[data-component="tab"]'))
            .map(el => componentRegistry.getInstance(el));
            
        // 如果子组件还未初始化，手动初始化
        if (this.tabs.length === 0) {
            componentRegistry.initComponentType('tab');
            this.tabs = Array.from(element.querySelectorAll('[data-component="tab"]'))
                .map(el => componentRegistry.getInstance(el));
        }
    }
}
```

### 4.4 性能优化

- 使用事件委托减少事件绑定数量
- 延迟初始化不可见的组件
- 销毁不再使用的组件实例
- 使用`data-config-*`属性传递配置，避免重复绑定事件

## 5. 故障排除

### 5.1 常见问题

1. **组件未初始化**
   - 检查是否正确添加了`data-component`属性
   - 确保组件注册发生在DOM加载完成前
   - 验证选择器是否正确

2. **组件初始化错误**
   - 检查浏览器控制台是否有错误信息
   - 确保组件类的构造函数没有错误
   - 验证传递给组件的配置是否有效

3. **组件无法找到**
   - 确认组件ID是否正确
   - 检查元素是否存在于DOM中
   - 验证组件是否已被销毁

### 5.2 调试技巧

启用详细日志：

```javascript
// 初始化日志系统
if (window.logger) {
    logger.init({
        level: logger.LEVELS.DEBUG,
        verbose: true
    });
}
```

检查已注册的组件：

```javascript
// 打印所有已注册的组件类型
console.log(Array.from(componentRegistry.componentTypes.keys()));

// 打印所有活跃的组件实例
console.log(Array.from(componentRegistry.instances.entries()));
```

## 6. 示例

### 6.1 表单验证组件

```javascript
// 表单验证组件
class FormValidator {
    constructor(element, options) {
        this.form = element;
        this.options = {
            validateOnBlur: true,
            validateOnSubmit: true,
            ...options
        };
        
        this.init();
    }
    
    init() {
        // 绑定表单事件
        if (this.options.validateOnSubmit) {
            this.form.addEventListener('submit', this.handleSubmit.bind(this));
        }
        
        // 绑定字段验证事件
        if (this.options.validateOnBlur) {
            this.form.addEventListener('blur', this.handleBlur.bind(this), true);
        }
    }
    
    handleSubmit(event) {
        if (!this.validateAll()) {
            event.preventDefault();
        }
    }
    
    handleBlur(event) {
        if (event.target.matches('input, select, textarea')) {
            this.validateField(event.target);
        }
    }
    
    validateAll() {
        // 验证所有字段的代码
        return true;
    }
    
    validateField(field) {
        // 验证单个字段的代码
        return true;
    }
    
    destroy() {
        // 移除事件监听器
        this.form.removeEventListener('submit', this.handleSubmit);
        this.form.removeEventListener('blur', this.handleBlur, true);
    }
}

// 注册组件
componentRegistry.register('formValidator', FormValidator);
```

使用示例：

```html
<form data-component="formValidator" data-config-validate-on-blur="true">
    <div class="form-group">
        <label for="name">姓名</label>
        <input id="name" name="name" required>
    </div>
    <div class="form-group">
        <label for="email">邮箱</label>
        <input id="email" name="email" type="email" required>
    </div>
    <button type="submit">提交</button>
</form>
```

### 6.2 模态框组件

```javascript
// 模态框组件
class Modal {
    constructor(element, options) {
        this.element = element;
        this.options = {
            closeOnEscape: true,
            closeOnOverlayClick: true,
            ...options
        };
        
        this.overlay = null;
        this.closeButton = element.querySelector('.modal-close');
        
        this.init();
    }
    
    init() {
        // 创建遮罩层
        this.createOverlay();
        // 绑定事件
        this.bindEvents();
    }
    
    createOverlay() {
        this.overlay = document.createElement('div');
        this.overlay.className = 'modal-overlay';
        document.body.appendChild(this.overlay);
    }
    
    bindEvents() {
        if (this.closeButton) {
            this.closeButton.addEventListener('click', this.close.bind(this));
        }
        
        if (this.options.closeOnOverlayClick) {
            this.overlay.addEventListener('click', this.close.bind(this));
        }
        
        if (this.options.closeOnEscape) {
            document.addEventListener('keydown', this.handleKeyDown.bind(this));
        }
    }
    
    handleKeyDown(event) {
        if (event.key === 'Escape') {
            this.close();
        }
    }
    
    open() {
        document.body.classList.add('modal-open');
        this.element.classList.add('modal-active');
        this.overlay.classList.add('modal-overlay-active');
    }
    
    close() {
        document.body.classList.remove('modal-open');
        this.element.classList.remove('modal-active');
        this.overlay.classList.remove('modal-overlay-active');
    }
    
    destroy() {
        // 移除事件监听
        if (this.closeButton) {
            this.closeButton.removeEventListener('click', this.close);
        }
        
        if (this.options.closeOnOverlayClick) {
            this.overlay.removeEventListener('click', this.close);
        }
        
        if (this.options.closeOnEscape) {
            document.removeEventListener('keydown', this.handleKeyDown);
        }
        
        // 移除遮罩层
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
    }
}

// 注册模态框组件
componentRegistry.register('modal', Modal);
```

使用示例：

```html
<div class="modal" data-component="modal" data-config-close-on-escape="true">
    <div class="modal-dialog">
        <div class="modal-header">
            <h3>模态框标题</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>模态框内容</p>
        </div>
        <div class="modal-footer">
            <button class="btn">确认</button>
        </div>
    </div>
</div>
```

## 7. 总结

组件注册系统为前端开发提供了统一的组件管理方案，具有以下优势：

1. **简化开发流程**：标准化组件的定义和使用方式
2. **提高代码质量**：减少重复代码，规范组件生命周期
3. **增强可维护性**：集中管理组件，方便调试和维护
4. **提升性能**：按需初始化组件，避免资源浪费
5. **良好的扩展性**：支持组件间通信和依赖管理

通过遵循本指南的最佳实践，可以充分利用组件注册系统的功能，构建高质量、可维护的前端应用。
