/**
 * 表单增强组件库 - 解决表单验证、用户体验、无障碍访问等问题
 * 基于质量检查报告的表单优化方案
 */

/**
 * 增强型表单验证器
 */
class EnhancedFormValidator {
    constructor() {
        this.rules = {
            required: {
                validate: (value) => value !== null && value !== undefined && String(value).trim() !== '',
                message: '此字段为必填项'
            },
            phone: {
                validate: (value) => /^1[3-9]\d{9}$/.test(value),
                message: '请输入正确的手机号码'
            },
            email: {
                validate: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                message: '请输入正确的邮箱地址'
            },
            idCard: {
                validate: (value) => /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value),
                message: '请输入正确的身份证号码'
            },
            url: {
                validate: (value) => /^https?:\/\/.+/.test(value),
                message: '请输入正确的网址'
            },
            number: {
                validate: (value) => !isNaN(value) && !isNaN(parseFloat(value)),
                message: '请输入有效的数字'
            },
            integer: {
                validate: (value) => Number.isInteger(Number(value)),
                message: '请输入整数'
            },
            min: {
                validate: (value, param) => String(value).length >= param,
                message: (param) => `最少需要${param}个字符`
            },
            max: {
                validate: (value, param) => String(value).length <= param,
                message: (param) => `最多允许${param}个字符`
            },
            minValue: {
                validate: (value, param) => Number(value) >= param,
                message: (param) => `数值不能小于${param}`
            },
            maxValue: {
                validate: (value, param) => Number(value) <= param,
                message: (param) => `数值不能大于${param}`
            },
            pattern: {
                validate: (value, param) => new RegExp(param).test(value),
                message: '格式不正确'
            },
            confirm: {
                validate: (value, param, form) => {
                    const targetField = form.querySelector(`[name="${param}"]`);
                    return targetField ? value === targetField.value : false;
                },
                message: (param) => `与${param}不匹配`
            }
        };
        
        this.setupGlobalValidation();
    }

    setupGlobalValidation() {
        // 实时验证
        document.addEventListener('input', (e) => {
            if (e.target.dataset.validate) {
                this.validateField(e.target);
            }
        });

        // 失焦验证
        document.addEventListener('blur', (e) => {
            if (e.target.dataset.validate) {
                this.validateField(e.target);
            }
        }, true);

        // 表单提交验证
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.dataset.validate !== 'false') {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        });
    }

    validateField(field) {
        const rules = field.dataset.validate.split('|');
        const value = field.value;
        const form = field.closest('form');
        
        this.clearFieldError(field);
        
        for (const ruleString of rules) {
            const [ruleName, param] = ruleString.split(':');
            const rule = this.rules[ruleName];
            
            if (rule && !rule.validate(value, param, form)) {
                const message = typeof rule.message === 'function' 
                    ? rule.message(param) 
                    : rule.message;
                this.showFieldError(field, message);
                return false;
            }
        }
        
        this.showFieldSuccess(field);
        return true;
    }

    validateForm(form) {
        const fields = form.querySelectorAll('[data-validate]');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    showFieldError(field, message) {
        field.classList.add('error');
        field.classList.remove('success');
        field.setAttribute('aria-invalid', 'true');
        
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-500 text-sm mt-1';
            errorElement.setAttribute('role', 'alert');
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }

    showFieldSuccess(field) {
        field.classList.remove('error');
        field.classList.add('success');
        field.setAttribute('aria-invalid', 'false');
        
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    clearFieldError(field) {
        field.classList.remove('error', 'success');
        field.removeAttribute('aria-invalid');
        
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    addRule(name, rule) {
        this.rules[name] = rule;
    }
}

/**
 * 智能输入组件
 */
class SmartInput {
    constructor(input, options = {}) {
        this.input = typeof input === 'string' ? document.querySelector(input) : input;
        this.options = {
            autoComplete: true,
            formatOnBlur: true,
            clearButton: true,
            ...options
        };
        
        if (this.input) {
            this.init();
        }
    }

    init() {
        this.setupEnhancements();
        this.setupEventListeners();
    }

    setupEnhancements() {
        // 添加清除按钮
        if (this.options.clearButton && this.input.type !== 'password') {
            this.addClearButton();
        }

        // 添加字符计数
        if (this.input.maxLength) {
            this.addCharacterCount();
        }

        // 添加密码强度指示器
        if (this.input.type === 'password') {
            this.addPasswordStrengthIndicator();
        }

        // 添加格式化功能
        if (this.options.formatOnBlur) {
            this.setupFormatting();
        }
    }

    addClearButton() {
        const wrapper = document.createElement('div');
        wrapper.className = 'relative';
        
        this.input.parentNode.insertBefore(wrapper, this.input);
        wrapper.appendChild(this.input);
        
        const clearBtn = document.createElement('button');
        clearBtn.type = 'button';
        clearBtn.className = 'absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors';
        clearBtn.innerHTML = '<i data-lucide="x" class="w-4 h-4"></i>';
        clearBtn.setAttribute('aria-label', '清除输入');
        clearBtn.style.display = 'none';
        
        clearBtn.addEventListener('click', () => {
            this.input.value = '';
            this.input.focus();
            this.input.dispatchEvent(new Event('input', { bubbles: true }));
            clearBtn.style.display = 'none';
        });
        
        wrapper.appendChild(clearBtn);
        
        // 显示/隐藏清除按钮
        this.input.addEventListener('input', () => {
            clearBtn.style.display = this.input.value ? 'block' : 'none';
        });
    }

    addCharacterCount() {
        const counter = document.createElement('div');
        counter.className = 'character-count text-sm text-gray-500 mt-1 text-right';
        this.input.parentNode.appendChild(counter);
        
        const updateCounter = () => {
            const current = this.input.value.length;
            const max = this.input.maxLength;
            counter.textContent = `${current}/${max}`;
            
            if (current > max * 0.9) {
                counter.classList.add('text-orange-500');
            } else {
                counter.classList.remove('text-orange-500');
            }
            
            if (current >= max) {
                counter.classList.add('text-red-500');
            } else {
                counter.classList.remove('text-red-500');
            }
        };
        
        this.input.addEventListener('input', updateCounter);
        updateCounter();
    }

    addPasswordStrengthIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength mt-2';
        indicator.innerHTML = `
            <div class="flex space-x-1 mb-1">
                <div class="strength-bar flex-1 h-1 bg-gray-200 rounded"></div>
                <div class="strength-bar flex-1 h-1 bg-gray-200 rounded"></div>
                <div class="strength-bar flex-1 h-1 bg-gray-200 rounded"></div>
                <div class="strength-bar flex-1 h-1 bg-gray-200 rounded"></div>
            </div>
            <div class="strength-text text-sm text-gray-500">请输入密码</div>
        `;
        
        this.input.parentNode.appendChild(indicator);
        
        this.input.addEventListener('input', () => {
            this.updatePasswordStrength(indicator);
        });
    }

    updatePasswordStrength(indicator) {
        const password = this.input.value;
        const bars = indicator.querySelectorAll('.strength-bar');
        const text = indicator.querySelector('.strength-text');
        
        let strength = 0;
        let strengthText = '弱';
        let strengthColor = 'bg-red-500';
        
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        // 重置所有条
        bars.forEach(bar => {
            bar.className = 'strength-bar flex-1 h-1 bg-gray-200 rounded';
        });
        
        if (strength >= 3) {
            strengthText = '中等';
            strengthColor = 'bg-yellow-500';
        }
        if (strength >= 4) {
            strengthText = '强';
            strengthColor = 'bg-green-500';
        }
        if (strength >= 5) {
            strengthText = '很强';
            strengthColor = 'bg-green-600';
        }
        
        // 填充强度条
        for (let i = 0; i < Math.min(strength, 4); i++) {
            bars[i].className = `strength-bar flex-1 h-1 ${strengthColor} rounded`;
        }
        
        text.textContent = password ? `密码强度：${strengthText}` : '请输入密码';
    }

    setupFormatting() {
        this.input.addEventListener('blur', () => {
            this.formatValue();
        });
    }

    formatValue() {
        const type = this.input.dataset.format || this.input.type;
        let value = this.input.value.trim();
        
        switch (type) {
            case 'phone':
                value = value.replace(/\D/g, '');
                if (value.length === 11) {
                    value = value.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
                }
                break;
            case 'idcard':
                value = value.toUpperCase();
                if (value.length === 18) {
                    value = value.replace(/(\d{6})(\d{8})(\d{3}[0-9X])/, '$1 $2 $3');
                }
                break;
            case 'bankcard':
                value = value.replace(/\D/g, '');
                value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
                break;
        }
        
        if (value !== this.input.value) {
            this.input.value = value;
        }
    }

    setupEventListeners() {
        // 自动聚焦到下一个字段
        if (this.input.dataset.autoNext) {
            this.input.addEventListener('input', () => {
                if (this.input.value.length === this.input.maxLength) {
                    const nextField = document.querySelector(`[name="${this.input.dataset.autoNext}"]`);
                    if (nextField) {
                        nextField.focus();
                    }
                }
            });
        }
    }

    // 静态方法：为所有输入框应用智能增强
    static applyToAll(selector = 'input[data-smart]', options = {}) {
        const inputs = document.querySelectorAll(selector);
        return Array.from(inputs).map(input => new SmartInput(input, options));
    }
}

/**
 * 文件上传组件
 */
class FileUploader {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            maxFiles: 5,
            maxSize: 10 * 1024 * 1024, // 10MB
            acceptedTypes: ['image/*'],
            showPreview: true,
            dragDrop: true,
            ...options
        };
        
        this.files = [];
        
        if (this.container) {
            this.init();
        }
    }

    init() {
        this.createUploadArea();
        this.setupEventListeners();
    }

    createUploadArea() {
        this.container.innerHTML = `
            <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-6 text-center transition-colors hover:border-blue-400">
                <div class="upload-icon mb-4">
                    <i data-lucide="upload-cloud" class="w-12 h-12 mx-auto text-gray-400"></i>
                </div>
                <div class="upload-text">
                    <p class="text-lg font-medium text-gray-700 mb-2">点击上传文件或拖拽到此处</p>
                    <p class="text-sm text-gray-500">支持 ${this.options.acceptedTypes.join(', ')}，最大 ${this.formatFileSize(this.options.maxSize)}</p>
                </div>
                <input type="file" class="upload-input hidden" multiple="${this.options.maxFiles > 1}" accept="${this.options.acceptedTypes.join(',')}">
            </div>
            <div class="file-list mt-4"></div>
        `;
        
        // 初始化Lucide图标
        if (window.lucide) {
            window.lucide.createIcons();
        }
    }

    setupEventListeners() {
        const uploadArea = this.container.querySelector('.upload-area');
        const uploadInput = this.container.querySelector('.upload-input');
        
        // 点击上传
        uploadArea.addEventListener('click', () => {
            uploadInput.click();
        });
        
        // 文件选择
        uploadInput.addEventListener('change', (e) => {
            this.handleFiles(Array.from(e.target.files));
        });
        
        // 拖拽上传
        if (this.options.dragDrop) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-400', 'bg-blue-50');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-400', 'bg-blue-50');
                this.handleFiles(Array.from(e.dataTransfer.files));
            });
        }
    }

    handleFiles(newFiles) {
        const validFiles = newFiles.filter(file => this.validateFile(file));
        
        if (this.files.length + validFiles.length > this.options.maxFiles) {
            window.UI.toast.error(`最多只能上传${this.options.maxFiles}个文件`);
            return;
        }
        
        validFiles.forEach(file => {
            this.addFile(file);
        });
    }

    validateFile(file) {
        // 检查文件类型
        const isValidType = this.options.acceptedTypes.some(type => {
            if (type.endsWith('/*')) {
                return file.type.startsWith(type.slice(0, -1));
            }
            return file.type === type;
        });
        
        if (!isValidType) {
            window.UI.toast.error(`不支持的文件类型：${file.type}`);
            return false;
        }
        
        // 检查文件大小
        if (file.size > this.options.maxSize) {
            window.UI.toast.error(`文件大小超过限制：${this.formatFileSize(this.options.maxSize)}`);
            return false;
        }
        
        return true;
    }

    addFile(file) {
        const fileId = Utils.generateId();
        const fileObj = {
            id: fileId,
            file: file,
            progress: 0,
            status: 'pending' // pending, uploading, success, error
        };
        
        this.files.push(fileObj);
        this.renderFileItem(fileObj);
        
        // 如果有预览功能，生成预览
        if (this.options.showPreview && file.type.startsWith('image/')) {
            this.generatePreview(fileObj);
        }
    }

    renderFileItem(fileObj) {
        const fileList = this.container.querySelector('.file-list');
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item flex items-center p-3 bg-gray-50 rounded-lg mb-2';
        fileItem.dataset.fileId = fileObj.id;
        
        fileItem.innerHTML = `
            <div class="file-preview w-12 h-12 bg-gray-200 rounded mr-3 flex items-center justify-center">
                <i data-lucide="file" class="w-6 h-6 text-gray-500"></i>
            </div>
            <div class="file-info flex-1">
                <div class="file-name font-medium text-gray-700">${fileObj.file.name}</div>
                <div class="file-size text-sm text-gray-500">${this.formatFileSize(fileObj.file.size)}</div>
                <div class="file-progress mt-1">
                    <div class="progress-bar w-full bg-gray-200 rounded-full h-2">
                        <div class="progress-fill bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <button type="button" class="file-remove ml-3 text-red-500 hover:text-red-700 transition-colors" aria-label="删除文件">
                <i data-lucide="trash-2" class="w-5 h-5"></i>
            </button>
        `;
        
        // 删除文件
        fileItem.querySelector('.file-remove').addEventListener('click', () => {
            this.removeFile(fileObj.id);
        });
        
        fileList.appendChild(fileItem);
        
        // 初始化Lucide图标
        if (window.lucide) {
            window.lucide.createIcons();
        }
    }

    generatePreview(fileObj) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const fileItem = this.container.querySelector(`[data-file-id="${fileObj.id}"]`);
            const preview = fileItem.querySelector('.file-preview');
            preview.innerHTML = `<img src="${e.target.result}" class="w-full h-full object-cover rounded" alt="预览">`;
        };
        reader.readAsDataURL(fileObj.file);
    }

    removeFile(fileId) {
        this.files = this.files.filter(f => f.id !== fileId);
        const fileItem = this.container.querySelector(`[data-file-id="${fileId}"]`);
        if (fileItem) {
            fileItem.remove();
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFiles() {
        return this.files;
    }

    clear() {
        this.files = [];
        this.container.querySelector('.file-list').innerHTML = '';
    }
}

/**
 * 短信验证码组件
 */
class SMSVerification {
    constructor(phoneInput, codeInput, sendButton, options = {}) {
        this.phoneInput = typeof phoneInput === 'string' ? document.querySelector(phoneInput) : phoneInput;
        this.codeInput = typeof codeInput === 'string' ? document.querySelector(codeInput) : codeInput;
        this.sendButton = typeof sendButton === 'string' ? document.querySelector(sendButton) : sendButton;
        
        this.options = {
            countdown: 60,
            phoneValidation: true,
            autoSubmit: false,
            ...options
        };
        
        this.countdownTimer = null;
        
        if (this.phoneInput && this.codeInput && this.sendButton) {
            this.init();
        }
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 发送验证码
        this.sendButton.addEventListener('click', () => {
            this.sendCode();
        });
        
        // 手机号输入验证
        if (this.options.phoneValidation) {
            this.phoneInput.addEventListener('input', () => {
                this.validatePhone();
            });
        }
        
        // 验证码输入
        this.codeInput.addEventListener('input', () => {
            if (this.options.autoSubmit && this.codeInput.value.length === 6) {
                this.verifyCode();
            }
        });
    }

    validatePhone() {
        const phone = this.phoneInput.value;
        const isValid = /^1[3-9]\d{9}$/.test(phone);
        
        this.sendButton.disabled = !isValid;
        this.sendButton.classList.toggle('opacity-50', !isValid);
        
        return isValid;
    }

    async sendCode() {
        if (!this.validatePhone()) {
            window.UI.toast.error('请输入正确的手机号码');
            return;
        }
        
        try {
            // 这里应该调用实际的API
            await this.mockSendSMS();
            
            window.UI.toast.success('验证码已发送');
            this.startCountdown();
            
        } catch (error) {
            window.UI.toast.error('发送失败，请重试');
        }
    }

    mockSendSMS() {
        return new Promise((resolve) => {
            setTimeout(resolve, 1000);
        });
    }

    startCountdown() {
        let countdown = this.options.countdown;
        this.sendButton.disabled = true;
        
        const updateButton = () => {
            this.sendButton.textContent = `${countdown}秒后重发`;
            countdown--;
            
            if (countdown < 0) {
                clearInterval(this.countdownTimer);
                this.sendButton.disabled = false;
                this.sendButton.textContent = '发送验证码';
                this.countdownTimer = null;
            }
        };
        
        updateButton();
        this.countdownTimer = setInterval(updateButton, 1000);
    }

    async verifyCode() {
        const code = this.codeInput.value;
        
        if (code.length !== 6) {
            window.UI.toast.error('请输入6位验证码');
            return false;
        }
        
        try {
            // 这里应该调用实际的验证API
            const isValid = await this.mockVerifyCode(code);
            
            if (isValid) {
                window.UI.toast.success('验证成功');
                return true;
            } else {
                window.UI.toast.error('验证码错误');
                return false;
            }
        } catch (error) {
            window.UI.toast.error('验证失败，请重试');
            return false;
        }
    }

    mockVerifyCode(code) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟验证逻辑
                resolve(code === '123456');
            }, 500);
        });
    }

    reset() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
        
        this.sendButton.disabled = false;
        this.sendButton.textContent = '发送验证码';
        this.codeInput.value = '';
    }
}

// 创建全局实例
const enhancedFormValidator = new EnhancedFormValidator();

// 导出到全局UI对象
if (window.UI) {
    window.UI.formValidator = enhancedFormValidator;
    window.UI.SmartInput = SmartInput;
    window.UI.FileUploader = FileUploader;
    window.UI.SMSVerification = SMSVerification;
}

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 自动应用智能输入
    SmartInput.applyToAll();
    
    // 初始化文件上传器
    document.querySelectorAll('[data-file-upload]').forEach(container => {
        const options = {
            maxFiles: parseInt(container.dataset.maxFiles) || 5,
            maxSize: parseInt(container.dataset.maxSize) || 10 * 1024 * 1024,
            acceptedTypes: container.dataset.acceptedTypes ? container.dataset.acceptedTypes.split(',') : ['image/*']
        };
        new FileUploader(container, options);
    });
    
    // 初始化短信验证
    document.querySelectorAll('[data-sms-verification]').forEach(container => {
        const phoneInput = container.querySelector('[data-phone]');
        const codeInput = container.querySelector('[data-code]');
        const sendButton = container.querySelector('[data-send]');
        
        if (phoneInput && codeInput && sendButton) {
            new SMSVerification(phoneInput, codeInput, sendButton);
        }
    });
    
    console.log('表单增强组件库已初始化');
});

// 导出模块（如果支持ES6模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        EnhancedFormValidator,
        SmartInput,
        FileUploader,
        SMSVerification
    };
}