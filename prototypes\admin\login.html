<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': '#0066ff',
                        'tech-purple': '#6366f1',
                        'tech-cyan': '#06b6d4',
                        'dark-bg': '#0f0f23',
                        'dark-card': '#1e1e3f'
                    },
                    fontFamily: {
                        'tech': ['Inter', 'PingFang SC', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, #0066ff 0%, #6366f1 50%, #06b6d4 100%);
        }
        .tech-glow {
            box-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
        }
        .input-glow:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-tech min-h-screen flex items-center justify-center">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-tech-purple rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-tech-cyan rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="relative z-10 w-full max-w-md mx-4">
        <!-- Logo区域 -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-tech-blue to-tech-purple rounded-2xl mb-4 tech-glow">
                <span class="text-white font-bold text-2xl">管</span>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">管理员登录</h1>
            <p class="text-gray-400 text-sm">本地助手后台管理系统</p>
        </div>

        <!-- 登录表单 -->
        <div class="bg-dark-card rounded-2xl p-8 tech-glow">
            <form class="space-y-6">
                <!-- 用户名输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">管理员账号</label>
                    <div class="relative">
                        <input type="text" 
                               placeholder="请输入管理员账号"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-tech-purple input-glow transition-all duration-200">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 密码输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">登录密码</label>
                    <div class="relative">
                        <input type="password" 
                               placeholder="请输入登录密码"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-tech-purple input-glow transition-all duration-200">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 验证码 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">安全验证</label>
                    <div class="flex space-x-3">
                        <input type="text" 
                               placeholder="验证码"
                               class="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-tech-purple input-glow transition-all duration-200">
                        <div class="w-24 h-12 bg-gray-700 rounded-lg flex items-center justify-center border border-gray-600 cursor-pointer hover:bg-gray-600 transition-colors">
                            <span class="text-tech-cyan font-mono text-lg">A8K9</span>
                        </div>
                    </div>
                </div>

                <!-- 记住登录 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" class="w-4 h-4 text-tech-purple bg-gray-800 border-gray-600 rounded focus:ring-tech-purple focus:ring-2">
                        <span class="ml-2 text-sm text-gray-300">记住登录状态</span>
                    </label>
                    <a href="#" class="text-sm text-tech-cyan hover:text-tech-blue transition-colors">忘记密码？</a>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" 
                        class="w-full py-3 px-4 tech-gradient text-white font-semibold rounded-lg hover:opacity-90 transition-all duration-200 tech-glow">
                    登录管理后台
                </button>
            </form>

            <!-- 安全提示 -->
            <div class="mt-6 p-4 bg-gray-800 rounded-lg border border-gray-600">
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <h4 class="text-sm font-medium text-yellow-400 mb-1">安全提示</h4>
                        <p class="text-xs text-gray-400">请确保在安全环境下登录，不要在公共设备上保存登录状态</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center mt-6 text-gray-400 text-xs">
            <p>本地助手管理系统 v1.0</p>
            <p class="mt-1">如有技术问题请联系系统管理员</p>
        </div>
    </div>

    <script>
        // 验证码点击刷新
        document.querySelector('.w-24.h-12').addEventListener('click', function() {
            const codes = ['A8K9', 'B7X3', 'C9M5', 'D4N8', 'E6P2'];
            const randomCode = codes[Math.floor(Math.random() * codes.length)];
            this.querySelector('span').textContent = randomCode;
        });

        // 表单提交处理
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('登录功能演示 - 实际开发中将连接后端验证');
        });
    </script>
</body>
</html>