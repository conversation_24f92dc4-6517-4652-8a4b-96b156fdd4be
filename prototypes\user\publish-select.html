<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择发布板块 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .modal-overlay {
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
        }
        .modal-content {
            opacity: 1;
            transition: opacity 0.2s ease;
        }
        .section-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }
        .section-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3);
        }
        .section-card:active {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    <!-- 模拟背景页面 -->
    <div class="min-h-screen bg-slate-900 p-4">
        <div class="max-w-md mx-auto">
            <div class="text-center py-20">
                <h2 class="text-xl font-semibold text-slate-300 mb-4">点击发布按钮查看弹窗效果</h2>
                <button id="showModal" class="bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors" data-action="showPublishModal">
                    发布信息
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 z-50 hidden">
        <!-- 遮罩层 -->
        <div class="modal-overlay absolute inset-0" data-action="closePublishModal"></div>
        
        <!-- 弹窗内容 -->
        <div class="modal-content absolute bottom-0 left-0 right-0 bg-slate-800 rounded-t-3xl p-6">
            <div class="max-w-md mx-auto">
                <!-- 拖拽指示器 -->
                <div class="w-12 h-1 bg-slate-600 rounded-full mx-auto mb-6"></div>
                
                <!-- 标题和返回按钮 -->
                <div class="flex items-center justify-between mb-6">
                    <button data-action="back" class="p-2 hover:bg-slate-700 rounded-lg transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5 text-slate-400"></i>
                    </button>
                    <div class="text-center flex-1">
                        <h2 class="text-2xl font-bold text-white mb-2">发布中心</h2>
                        <p class="text-slate-400 text-sm">统一管理您的发布内容</p>
                    </div>
                    <button data-action="showDrafts" class="p-2 hover:bg-slate-700 rounded-lg transition-colors" title="草稿箱">
                        <i data-lucide="file-text" class="w-5 h-5 text-slate-400"></i>
                    </button>
                </div>
                
                <!-- 快速操作区 -->
                <div class="flex space-x-3 mb-6">
                    <button data-action="showHistory" class="flex-1 bg-slate-700 hover:bg-slate-600 rounded-lg p-3 transition-colors">
                        <div class="flex items-center justify-center space-x-2">
                            <i data-lucide="clock" class="w-4 h-4 text-slate-300"></i>
                            <span class="text-sm text-slate-300">发布历史</span>
                        </div>
                    </button>
                    <button data-action="togglePublishMode" id="publishModeBtn" class="flex-1 bg-blue-600 hover:bg-blue-700 rounded-lg p-3 transition-colors">
                        <div class="flex items-center justify-center space-x-2">
                            <i data-lucide="zap" class="w-4 h-4 text-white"></i>
                            <span class="text-sm text-white">快速发布</span>
                        </div>
                    </button>
                </div>
                
                <!-- 四大板块选择 -->
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <!-- 商铺板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="selectShopSection">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="store" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">商铺</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">发布商家信息<br>展示产品服务</p>
                        </div>
                    </div>
                    
                    <!-- 信息板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="publishTo" data-params='{"type":"information"}'>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="info" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">信息</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">招聘求职<br>租房买卖</p>
                        </div>
                    </div>
                    
                    <!-- 出行板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="publishTo" data-params='{"type":"transportation"}'>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="car" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">出行</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">拼车代驾<br>货运服务</p>
                        </div>
                    </div>
                    
                    <!-- 跑腿板块 -->
                    <div class="section-card p-6 rounded-2xl border border-slate-600 cursor-pointer" data-action="publishTo" data-params='{"type":"errands"}'>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="font-semibold text-white mb-2">跑腿</h3>
                            <p class="text-xs text-slate-400 leading-relaxed">代买代送<br>代办事务</p>
                        </div>
                    </div>
                </div>
                
                <!-- 取消按钮 -->
                <button data-action="closePublishModal" class="w-full py-4 text-slate-400 font-medium hover:text-white transition-colors">
                    取消
                </button>
                
                <!-- 底部安全距离 -->
                <div class="h-4"></div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            // 页面加载时自动显示弹窗（用于演示）
            setTimeout(() => {
                document.getElementById('publishModal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }, 500);
            
            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    document.getElementById('publishModal').classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            });
            
            // 初始化事件处理
            initPublishEvents();
        });
        
        // 初始化发布选择页面事件
        function initPublishEvents() {
            // 处理所有data-action按钮
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (!target) return;
                
                const action = target.dataset.action;
                const params = target.dataset.params ? JSON.parse(target.dataset.params) : {};
                
                switch(action) {
                    case 'showPublishModal':
                        document.getElementById('publishModal').classList.remove('hidden');
                        document.body.style.overflow = 'hidden';
                        break;
                        
                    case 'closePublishModal':
                        document.getElementById('publishModal').classList.add('hidden');
                        document.body.style.overflow = 'auto';
                        break;
                        
                    case 'back':
                        window.history.back();
                        break;
                        
                    case 'showDrafts':
                        alert('草稿箱功能即将上线');
                        break;
                        
                    case 'showHistory':
                        alert('发布历史功能即将上线');
                        break;
                        
                    case 'togglePublishMode':
                        const btn = document.getElementById('publishModeBtn');
                        if (btn.classList.contains('bg-blue-600')) {
                            btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                            btn.classList.add('bg-slate-700', 'hover:bg-slate-600');
                            btn.querySelector('span').textContent = '标准发布';
                        } else {
                            btn.classList.remove('bg-slate-700', 'hover:bg-slate-600');
                            btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                            btn.querySelector('span').textContent = '快速发布';
                        }
                        break;
                        
                    case 'selectShopSection':
                        // 检查用户是否已有商铺
                        const hasShop = localStorage.getItem('userHasShop') === 'true';
                        if (hasShop) {
                            window.location.href = 'my-shop-products.html';
                        } else {
                            window.location.href = 'publish-shop.html';
                        }
                        break;
                        
                    case 'publishTo':
                        if (params && params.type) {
                            window.location.href = `publish-${params.type}.html`;
                        }
                        break;
                }
            });
        }
    </script>
</body>
</html>