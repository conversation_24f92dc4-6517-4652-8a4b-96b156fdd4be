<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券管理 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .coupon-card {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            position: relative;
            overflow: hidden;
        }
        .coupon-card::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -10px;
            width: 20px;
            height: 20px;
            background: #0f172a;
            border-radius: 50%;
            transform: translateY(-50%);
        }
        .coupon-card::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -10px;
            width: 20px;
            height: 20px;
            background: #0f172a;
            border-radius: 50%;
            transform: translateY(-50%);
        }
        .coupon-expired {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
        }
        .coupon-used {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .coupon-dashed {
            border-left: 2px dashed rgba(255, 255, 255, 0.3);
        }
        .tab-active {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="history.back()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                <h1 class="text-lg font-semibold text-slate-200">我的优惠券</h1>
                <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" onclick="scanCoupon()">
                    <i data-lucide="scan-line" class="w-5 h-5 text-slate-300"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 优惠券统计 -->
    <div class="max-w-md mx-auto px-4 py-6">
        <div class="bg-slate-800 rounded-xl p-6 mb-6">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-400">8</div>
                    <div class="text-sm text-slate-400 mt-1">可使用</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-400">12</div>
                    <div class="text-sm text-slate-400 mt-1">已使用</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-slate-400">3</div>
                    <div class="text-sm text-slate-400 mt-1">已过期</div>
                </div>
            </div>
        </div>

        <!-- 标签页切换 -->
        <div class="flex bg-slate-800 rounded-lg p-1 mb-6">
            <button class="tab-btn flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all tab-active" data-tab="available">
                可使用 (8)
            </button>
            <button class="tab-btn flex-1 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="used">
                已使用 (12)
            </button>
            <button class="tab-btn flex-1 py-2 px-4 rounded-md text-sm font-medium text-slate-400 hover:text-slate-300 transition-all" data-tab="expired">
                已过期 (3)
            </button>
        </div>

        <!-- 可使用优惠券 -->
        <div id="available" class="tab-content space-y-4">
            <!-- 满减券 -->
            <div class="coupon-card rounded-xl p-6 pb-12 text-white relative">
                <div class="flex items-start justify-between">
                    <div class="flex-1 pr-4">
                        <div class="flex items-baseline space-x-1">
                            <span class="text-xs">¥</span>
                            <span class="text-3xl font-bold">20</span>
                        </div>
                        <div class="text-sm opacity-90 mt-1">满100元可用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold text-sm leading-tight">商铺通用券</div>
                            <div class="text-xs opacity-90 mt-1 leading-tight">有效期至 2024-02-15</div>
                            <div class="text-xs opacity-75 mt-1 leading-tight">适用于所有商铺消费</div>
                        </div>
                    </div>
                </div>
                <button class="absolute bottom-3 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-full text-xs transition-all" onclick="useCoupon('coupon1')">
                    立即使用
                </button>
            </div>

            <!-- 折扣券 -->
            <div class="coupon-card rounded-xl p-6 pb-12 text-white relative" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)">
                <div class="flex items-start justify-between">
                    <div class="flex-1 pr-4">
                        <div class="flex items-baseline space-x-1">
                            <span class="text-3xl font-bold">8.5</span>
                            <span class="text-lg">折</span>
                        </div>
                        <div class="text-sm opacity-90 mt-1">无门槛使用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold text-sm leading-tight">跑腿服务券</div>
                            <div class="text-xs opacity-90 mt-1 leading-tight">有效期至 2024-02-20</div>
                            <div class="text-xs opacity-75 mt-1 leading-tight">适用于所有跑腿服务</div>
                        </div>
                    </div>
                </div>
                <button class="absolute bottom-3 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-full text-xs transition-all" onclick="useCoupon('coupon2')">
                    立即使用
                </button>
            </div>

            <!-- 免费券 -->
            <div class="coupon-card rounded-xl p-6 pb-12 text-white relative" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%)">
                <div class="flex items-start justify-between">
                    <div class="flex-1 pr-4">
                        <div class="text-2xl font-bold">免费</div>
                        <div class="text-sm opacity-90 mt-1">首次使用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold text-sm leading-tight">扫码呼叫服务</div>
                            <div class="text-xs opacity-90 mt-1 leading-tight">有效期至 2024-03-01</div>
                            <div class="text-xs opacity-75 mt-1 leading-tight">新用户专享福利</div>
                        </div>
                    </div>
                </div>
                <button class="absolute bottom-3 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-full text-xs transition-all" onclick="useCoupon('coupon3')">
                    立即使用
                </button>
            </div>

            <!-- 出行券 -->
            <div class="coupon-card rounded-xl p-6 pb-12 text-white relative" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)">
                <div class="flex items-start justify-between">
                    <div class="flex-1 pr-4">
                        <div class="flex items-baseline space-x-1">
                            <span class="text-xs">¥</span>
                            <span class="text-3xl font-bold">15</span>
                        </div>
                        <div class="text-sm opacity-90 mt-1">满50元可用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold text-sm leading-tight">出行服务券</div>
                            <div class="text-xs opacity-90 mt-1 leading-tight">有效期至 2024-02-25</div>
                            <div class="text-xs opacity-75 mt-1 leading-tight">拼车、代驾通用</div>
                        </div>
                    </div>
                </div>
                <button class="absolute bottom-3 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-full text-xs transition-all" onclick="useCoupon('coupon4')">
                    立即使用
                </button>
            </div>

            <!-- 即将过期提醒 -->
            <div class="bg-orange-500 bg-opacity-10 border border-orange-500 border-opacity-30 rounded-xl p-4">
                <div class="flex items-center space-x-3">
                    <i data-lucide="clock" class="w-5 h-5 text-orange-400"></i>
                    <div class="flex-1">
                        <div class="text-orange-400 font-medium">即将过期提醒</div>
                        <div class="text-sm text-orange-300 mt-1">您有2张优惠券将在3天内过期，请及时使用</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已使用优惠券 -->
        <div id="used" class="tab-content space-y-4 hidden">
            <div class="coupon-used rounded-xl p-6 pb-12 text-white relative">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-baseline space-x-1">
                            <span class="text-xs">¥</span>
                            <span class="text-3xl font-bold">10</span>
                        </div>
                        <div class="text-sm opacity-90 mt-1">满50元可用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold leading-tight">商铺通用券</div>
                            <div class="text-sm opacity-90 mt-1 leading-tight">使用时间：2024-01-10</div>
                            <div class="text-xs opacity-75 mt-2 leading-tight">订单号：202401100001</div>
                        </div>
                    </div>
                </div>
                <div class="absolute bottom-3 right-4 bg-white bg-opacity-20 px-4 py-1 rounded-full text-sm">
                    已使用
                </div>
            </div>

            <div class="coupon-used rounded-xl p-6 pb-12 text-white relative">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="text-2xl font-bold">9折</div>
                        <div class="text-sm opacity-90 mt-1">无门槛使用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold leading-tight">跑腿服务券</div>
                            <div class="text-sm opacity-90 mt-1 leading-tight">使用时间：2024-01-08</div>
                            <div class="text-xs opacity-75 mt-2 leading-tight">订单号：202401080015</div>
                        </div>
                    </div>
                </div>
                <div class="absolute bottom-3 right-4 bg-white bg-opacity-20 px-4 py-1 rounded-full text-sm">
                    已使用
                </div>
            </div>
        </div>

        <!-- 已过期优惠券 -->
        <div id="expired" class="tab-content space-y-4 hidden">
            <div class="coupon-expired rounded-xl p-6 pb-12 text-white relative">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-baseline space-x-1">
                            <span class="text-xs">¥</span>
                            <span class="text-3xl font-bold">30</span>
                        </div>
                        <div class="text-sm opacity-90 mt-1">满200元可用</div>
                    </div>
                    <div class="coupon-dashed pl-6 ml-6 flex-shrink-0 min-w-0">
                        <div class="text-right">
                            <div class="font-semibold leading-tight">商铺通用券</div>
                            <div class="text-sm opacity-90 mt-1 leading-tight">过期时间：2024-01-05</div>
                            <div class="text-xs opacity-75 mt-2 leading-tight">未使用</div>
                        </div>
                    </div>
                </div>
                <div class="absolute bottom-3 right-4 bg-white bg-opacity-20 px-4 py-1 rounded-full text-sm">
                    已过期
                </div>
            </div>
        </div>


    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.dataset.tab;
                
                // 更新按钮状态
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('tab-active');
                    b.classList.add('text-slate-400');
                });
                btn.classList.add('tab-active');
                btn.classList.remove('text-slate-400');
                
                // 显示对应内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tab).classList.remove('hidden');
            });
        });

        // 扫描优惠券
        function scanCoupon() {
            alert('正在打开扫码功能...');
        }

        // 使用优惠券
        function useCoupon(couponId) {
            alert('正在跳转到使用页面...');
        }


    </script>
</body>
</html>