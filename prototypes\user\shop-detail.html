<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商铺详情 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/config.js"></script>
    <script src="../scripts/logger.js"></script>
    <script src="../scripts/common.js"></script>
    <script src="../scripts/templates.js"></script>
    <script src="../scripts/event-handlers.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .shop-hero {
            height: 200px;
            object-fit: cover;
        }
        
        .shop-logo {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            object-fit: cover;
            border: 3px solid #1e293b;
        }
        
        .rating-stars {
            color: #fbbf24;
        }
        
        .product-card {
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-4px);
        }
        
        .product-image {
            height: 120px;
            object-fit: cover;
        }
        
        .shop-card-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .image-overlay {
            background-color: rgba(0, 0, 0, 0.9);
        }
        
        .overlay-image {
            max-height: 80vh;
            max-width: 90vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .shop-tag {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .product-card {
            transition: background-color 0.2s ease;
        }
        .product-card:hover {
            background-color: rgba(30, 41, 59, 0.8);
        }
        .image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .image-overlay.active {
            display: flex;
        }
        .overlay-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 图片浏览覆盖层 -->
    <div id="imageOverlay" class="fixed inset-0 z-50 image-overlay hidden flex items-center justify-center">
        <button class="absolute top-4 right-4 text-white text-2xl z-10" data-action="closeImageOverlay">
            <i data-lucide="x" class="w-8 h-8"></i>
        </button>
        <img id="overlayImage" src="" alt="大图预览" class="overlay-image rounded-lg shadow-xl">
    </div>

    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button class="flex items-center justify-center w-8 h-8 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors" data-action="goBack">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                
                <h1 class="text-lg font-semibold text-white">商家详情</h1>
                
                <div class="w-8 h-8"></div>
            </div>
        </div>
    </div>

    <!-- 商家信息卡片 -->
    <div class="bg-slate-800">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="space-y-3">
                <div class="flex items-center mb-2">
                    <h2 class="text-xl font-bold text-white truncate" title="星巴克咖啡(科技园店)">星巴克咖啡(科技园店)</h2>
                    <button class="flex items-center justify-center space-x-2 px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex-shrink-0 ml-3" data-action="contactMerchant">
                        <i data-lucide="phone" class="w-4 h-4"></i>
                        <span class="text-sm font-semibold">电话</span>
                    </button>
                </div>

                <!-- Status and Actions Row -->
                <div class="flex items-center gap-x-2 text-slate-300 mb-3 text-sm">
                    <span class="bg-blue-600 text-white px-2 py-1 rounded-full font-medium flex-shrink-0">实体店</span>
                    <div class="flex items-center flex-shrink-0">
                        <i data-lucide="map-pin" class="w-3.5 h-3.5 mr-1"></i>
                        <span>1.2km</span>
                    </div>
                    <button class="hover:text-white transition-colors flex items-center space-x-1.5 flex-shrink-0" data-action="toggleFavorite" id="favoriteBtn2">
                        <i data-lucide="heart" class="w-3.5 h-3.5" id="favoriteIcon2"></i>
                        <span>收藏</span>
                    </button>
                    <button class="hover:text-white transition-colors flex items-center space-x-1.5 flex-shrink-0" data-action="claimShop">
                        <i data-lucide="user-check" class="w-3.5 h-3.5"></i>
                        <span>认领</span>
                    </button>
                    <button class="hover:text-white transition-colors flex items-center space-x-1.5 flex-shrink-0" data-action="reportShop">
                        <i data-lucide="flag" class="w-3.5 h-3.5"></i>
                        <span>举报</span>
                    </button>
                    <button id="manageShopBtn" class="hover:text-white transition-colors flex items-center space-x-1.5 flex-shrink-0" data-action="manageMyShop" style="display: none;">
                        <i data-lucide="edit-3" class="w-3.5 h-3.5"></i>
                        <span>编辑</span>
                    </button>
                </div>

                <!-- 营业时间和提示信息 -->
                <div class="text-xs text-slate-500 space-y-1">
                    <div class="flex items-center">
                        <i data-lucide="clock" class="w-3 h-3 mr-1.5"></i>
                        <span>营业时间：09:00-22:00</span>
                    </div>
                    <div>💡 线下到店消费，平台仅提供信息展示</div>
                    <div class="flex items-center">
                        <i data-lucide="map-pin" class="w-3 h-3 mr-1.5"></i>
                        <span>深圳市南山区科技园南区深南大道9988号</span>
                    </div>
                </div>
            </div>
            

        </div>
    </div>

    <!-- 服务分类 -->
    <div class="bg-slate-800 mt-2">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-600 text-white border border-blue-500 rounded-full text-sm font-medium" data-action="filterServices" data-params='{"type":"all"}'>全部服务</button>
                    <button class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-action="filterServices" data-params='{"type":"coffee"}'>咖啡</button>
                    <button class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-action="filterServices" data-params='{"type":"food"}'>轻食</button>
                    <button class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors" data-action="filterServices" data-params='{"type":"dessert"}'>甜品</button>
                </div>
                
                <span class="text-sm text-slate-400">服务展示</span>
            </div>
        </div>
    </div>

    <!-- 服务展示网格 -->
    <div class="bg-slate-900 mt-2">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="grid grid-cols-2 gap-2">
                <!-- 服务卡片1 -->
                <div class="product-card bg-slate-800 border border-slate-700 rounded-lg overflow-hidden" data-action="viewServiceDetail" data-params='{"id":"coffee-beans"}'>
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=120&fit=crop" 
                             alt="咖啡豆" class="w-full h-24 object-cover cursor-pointer">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1560472354-b33ff0c44a43 -->
                        <span class="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">特色</span>
                    </div>
                    <div class="p-2">
                        <h3 class="font-medium text-white text-sm mb-1 truncate">精品手工咖啡豆</h3>
                        <p class="text-xs text-slate-400 mb-1 line-clamp-1">埃塞俄比亚耶加雪菲</p>
                        <span class="text-blue-400 text-xs">到店咨询</span>
                    </div>
                </div>

                <!-- 服务卡片2 -->
                <div class="product-card bg-slate-800 border border-slate-700 rounded-lg overflow-hidden" data-action="viewServiceDetail" data-params='{"id":"latte"}'>
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=120&fit=crop" 
                             alt="拿铁咖啡" class="w-full h-24 object-cover cursor-pointer">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b -->
                        <span class="absolute top-1 left-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">推荐</span>
                    </div>
                    <div class="p-2">
                        <h3 class="font-medium text-white text-sm mb-1 truncate">香草拿铁</h3>
                        <p class="text-xs text-slate-400 mb-1 line-clamp-1">经典意式咖啡</p>
                        <span class="text-blue-400 text-xs">到店咨询</span>
                    </div>
                </div>

                <!-- 服务卡片3 -->
                <div class="product-card bg-slate-800 border border-slate-700 rounded-lg overflow-hidden" data-action="viewServiceDetail" data-params='{"id":"tiramisu"}'>
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=200&h=120&fit=crop" 
                             alt="提拉米苏" class="w-full h-24 object-cover cursor-pointer">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b -->
                    </div>
                    <div class="p-2">
                        <h3 class="font-medium text-white text-sm mb-1 truncate">经典提拉米苏</h3>
                        <p class="text-xs text-slate-400 mb-1 line-clamp-1">意式经典甜品</p>
                        <span class="text-blue-400 text-xs">到店咨询</span>
                    </div>
                </div>

                <!-- 服务卡片4 -->
                <div class="product-card bg-slate-800 border border-slate-700 rounded-lg overflow-hidden" data-action="viewServiceDetail" data-params='{"id":"americano"}'>
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=200&h=120&fit=crop" 
                             alt="美式咖啡" class="w-full h-24 object-cover cursor-pointer">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1509042239860-f550ce710b93 -->
                    </div>
                    <div class="p-2">
                        <h3 class="font-medium text-white text-sm mb-1 truncate">经典美式咖啡</h3>
                        <p class="text-xs text-slate-400 mb-1 line-clamp-1">纯正黑咖啡</p>
                        <span class="text-blue-400 text-xs">到店咨询</span>
                    </div>
                </div>

                <!-- 服务卡片5 -->
                <div class="product-card bg-slate-800 border border-slate-700 rounded-lg overflow-hidden" data-action="viewServiceDetail" data-params='{"id":"cheesecake"}'>
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1551024506-0bccd828d307?w=200&h=120&fit=crop" 
                             alt="芝士蛋糕" class="w-full h-24 object-cover">
                        <button data-action="openImageOverlay" data-params='{"url":"https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop"}' data-stop-propagation="true" class="absolute top-1.5 right-1.5 bg-black/50 text-white p-1 rounded-full hover:bg-black/75 transition-colors">
                            <i data-lucide="zoom-in" class="w-3.5 h-3.5"></i>
                        </button>
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1551024506-0bccd828d307 -->
                        <span class="absolute top-1 left-1 bg-orange-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">招牌</span>
                    </div>
                    <div class="p-2">
                        <h3 class="font-medium text-white text-sm mb-1 truncate">纽约芝士蛋糕</h3>
                        <p class="text-xs text-slate-400 mb-1 line-clamp-1">浓郁芝士香味</p>
                        <span class="text-blue-400 text-xs">到店咨询</span>
                    </div>
                </div>

                <!-- 服务卡片6 -->
                <div class="product-card bg-slate-800 border border-slate-700 rounded-lg overflow-hidden" data-action="viewServiceDetail" data-params='{"id":"sandwich"}'>
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=200&h=120&fit=crop" 
                             alt="三明治" class="w-full h-24 object-cover">
                        <button data-action="openImageOverlay" data-params='{"url":"https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=800&h=600&fit=crop"}' data-stop-propagation="true" class="absolute top-1.5 right-1.5 bg-black/50 text-white p-1 rounded-full hover:bg-black/75 transition-colors">
                            <i data-lucide="zoom-in" class="w-3.5 h-3.5"></i>
                        </button>
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1544787219-7f47ccb76574 -->
                    </div>
                    <div class="p-2">
                        <h3 class="font-medium text-white text-sm mb-1 truncate">火腿芝士三明治</h3>
                        <p class="text-xs text-slate-400 mb-1 line-clamp-1">营养早餐首选</p>
                        <span class="text-blue-400 text-xs">到店咨询</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 相关推荐区域 -->
    <div class="bg-slate-800 mt-2">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-white">相关推荐</h3>
                <button class="text-blue-400 text-sm hover:text-blue-300" data-action="refreshRecommendations">换一批</button>
            </div>
            
            <!-- 推荐商家列表 -->
            <div class="space-y-3">
                <!-- 推荐商家1 -->
                <div class="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer" data-action="viewShopDetail" data-params='{"id":"costa"}'>
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=48&h=48&fit=crop" 
                             alt="Costa咖啡" class="w-12 h-12 rounded-lg object-cover">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb -->
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-white text-sm truncate">Costa咖啡(万象城店)</h4>
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i data-lucide="star" class="w-3 h-3 fill-current mr-1"></i>
                                    <span>4.6</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-1">
                                <p class="text-xs text-slate-400">英式咖啡 • 2.1km</p>
                                <span class="text-xs text-green-400">营业中</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 推荐商家2 -->
                <div class="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer" data-action="viewShopDetail" data-params='{"id":"luckin"}'>
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=48&h=48&fit=crop" 
                             alt="瑞幸咖啡" class="w-12 h-12 rounded-lg object-cover">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1495474472287-4d71bcdd2085 -->
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-white text-sm truncate">瑞幸咖啡(科技园店)</h4>
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i data-lucide="star" class="w-3 h-3 fill-current mr-1"></i>
                                    <span>4.5</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-1">
                                <p class="text-xs text-slate-400">新式茶饮 • 0.8km</p>
                                <span class="text-xs text-green-400">营业中</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 推荐商家3 -->
                <div class="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer" data-action="viewShopDetail" data-params='{"id":"manner"}'>
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1442512595331-e89e73853f31?w=48&h=48&fit=crop" 
                             alt="Manner咖啡" class="w-12 h-12 rounded-lg object-cover">
                        <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1442512595331-e89e73853f31 -->
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-white text-sm truncate">Manner咖啡(深大店)</h4>
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i data-lucide="star" class="w-3 h-3 fill-current mr-1"></i>
                                    <span>4.7</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-1">
                                <p class="text-xs text-slate-400">精品咖啡 • 1.5km</p>
                                <span class="text-xs text-orange-400">即将打烊</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 举报区域 -->
    <div class="bg-slate-800 mt-2">
        <div class="max-w-md mx-auto px-4 py-4">
            <button class="w-full flex items-center justify-center space-x-2 py-3 text-red-400 hover:bg-red-900/20 rounded-lg transition-colors" data-action="reportShop">
                <i data-lucide="flag" class="w-4 h-4"></i>
                <span>举报商家</span>
            </button>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化事件处理
            initShopDetailEvents();
            
            // 检测商铺所有权
            checkShopOwnership();
            
            // 重新初始化图标
            lucide.createIcons();
        });

        // 初始化页面事件
        function initShopDetailEvents() {
            // 处理所有data-action按钮
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (!target) return;
                
                const action = target.dataset.action;
                const params = target.dataset.params ? JSON.parse(target.dataset.params) : {};
                
                // 阻止事件冒泡
                if (target.dataset.stopPropagation) {
                    e.stopPropagation();
                }
                
                // 根据动作执行相应函数
                switch(action) {
                    case 'goBack':
                        window.history.back();
                        break;
                        
                    case 'contactMerchant':
                        showContactOptions();
                        break;
                        
                    case 'openImageOverlay':
                        openImageOverlay(params.url);
                        break;
                        
                    case 'closeImageOverlay':
                        closeImageOverlay();
                        break;
                        
                    case 'viewServiceDetail':
                        window.location.href = 'product-detail.html?id=' + params.id;
                        break;
                        
                    case 'toggleFavorite':
                        toggleFavorite();
                        break;
                        
                    case 'claimShop':
                        claimShop();
                        break;
                        
                    case 'reportShop':
                        reportShop();
                        break;
                        
                    case 'manageMyShop':
                        window.location.href = 'my-shop-products.html';
                        break;
                        
                    case 'filterServices':
                        filterServices(params.type, target);
                        break;
                        
                    case 'refreshRecommendations':
                        refreshRecommendations();
                        break;
                        
                    case 'viewShopDetail':
                        alert(`查看商家详情: ${params.id}`);
                        break;
                }
            });

            // 点击覆盖层背景关闭
            document.getElementById('imageOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeImageOverlay();
                }
            });
            
            // ESC键关闭图片查看
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeImageOverlay();
                }
            });
        }

        // 图片查看功能
        function openImageOverlay(src) {
            const overlay = document.getElementById('imageOverlay');
            const overlayImage = document.getElementById('overlayImage');
            overlayImage.src = src;
            overlay.classList.remove('hidden');
            overlay.classList.add('active');
        }

        function closeImageOverlay() {
            const overlay = document.getElementById('imageOverlay');
            overlay.classList.remove('active');
            setTimeout(() => {
                overlay.classList.add('hidden');
            }, 300);
        }
        
        // 联系商家选项
        function showContactOptions() {
            const options = [
                '📞 拨打电话',
                '💬 发送消息', 
                '📍 查看位置'
            ];
            const choice = prompt('请选择联系方式：\n\n' + options.join('\n'));
            
            if (choice && choice.includes('电话')) {
                alert('正在拨打电话...');
            } else if (choice && choice.includes('消息')) {
                alert('正在打开聊天窗口...');
            } else if (choice && choice.includes('位置')) {
                alert('正在打开地图导航...');
            }
        }
        
        // 服务筛选
        function filterServices(category, buttonElement) {
            // 更新按钮状态
            const buttons = document.querySelectorAll('[data-action="filterServices"]');
            buttons.forEach(btn => {
                btn.className = 'px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm hover:bg-slate-600 transition-colors';
            });
            
            // 高亮当前选中的按钮
            buttonElement.className = 'px-3 py-1 bg-blue-600 text-white border border-blue-500 rounded-full text-sm font-medium';
            
            alert('筛选服务类别：' + category + '\n\n已为您筛选相关服务');
        }
        
        // 收藏功能
        let isFavorited = false;
        function toggleFavorite() {
            isFavorited = !isFavorited;
            const icon = document.getElementById('favoriteIcon2');
            const btn = document.getElementById('favoriteBtn2');
            
            if (isFavorited) {
                icon.style.color = '#ef4444';
                icon.style.fill = '#ef4444';
                btn.style.backgroundColor = '#dc2626';
                alert('已添加到收藏');
            } else {
                icon.style.color = '';
                icon.style.fill = '';
                btn.style.backgroundColor = '';
                alert('已取消收藏');
            }
        }
        
        // 刷新推荐
        function refreshRecommendations() {
            // 模拟刷新推荐列表
            const recommendations = document.querySelector('.space-y-3');
            recommendations.style.opacity = '0.5';
            setTimeout(() => {
                recommendations.style.opacity = '1';
                alert('已为您刷新推荐列表');
            }, 500);
        }
        
        // 认领商铺
        function claimShop() {
            const isLoggedIn = confirm('认领商铺需要登录账号\n\n您是否已登录？');
            
            if (isLoggedIn) {
                const confirmClaim = confirm('确认认领此商铺？\n\n商铺名称：星巴克咖啡\n地址：深圳市南山区科技园\n\n认领后您将成为此商铺的管理员');
                
                if (confirmClaim) {
                    const phone = prompt('请输入您的联系电话用于验证：');
                    if (phone && phone.length >= 11) {
                        alert('认领申请已提交\n\n我们会在1-3个工作日内联系您进行身份验证\n请保持电话畅通');
                    } else {
                        alert('请输入有效的联系电话');
                    }
                }
            } else {
                const shouldLogin = confirm('请先登录账号\n\n是否前往登录页面？');
                if (shouldLogin) {
                    alert('正在跳转到登录页面...');
                }
            }
        }
        
        // 检测是否为商铺所有者
        function checkShopOwnership() {
            // 模拟检测当前用户是否为此商铺的所有者
            const isOwner = localStorage.getItem('userHasShop') === 'true' || Math.random() > 0.5;
            
            if (isOwner) {
                // 显示管理按钮
                const manageBtn = document.getElementById('manageShopBtn');
                if (manageBtn) {
                    manageBtn.style.display = 'flex';
                }
            }
        }
        
        // 举报商家
        function reportShop() {
            const reasons = [
                '1. 虚假信息',
                '2. 服务质量问题', 
                '3. 价格欺诈',
                '4. 违法违规',
                '5. 其他问题'
            ];
            
            const reason = prompt('请选择举报原因：\n\n' + reasons.join('\n') + '\n\n请输入数字1-5：');
            
            if (reason && reason >= 1 && reason <= 5) {
                const detail = prompt('请详细描述问题（可选）：');
                alert('举报已提交\n\n我们会在24小时内处理您的举报\n感谢您的反馈');
            }
        }
    </script>
</body>
</html>