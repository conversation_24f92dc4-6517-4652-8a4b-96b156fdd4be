<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地助手 - 您身边的生活服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <!-- 加载配置和工具库 -->
    <script src="../scripts/config.js"></script>
    <script src="../scripts/logger.js"></script>
    <script src="../scripts/common.js"></script>
    <script src="../scripts/templates.js"></script>
    <script src="../scripts/event-handlers.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .nav-item {
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .nav-item:not(.active):hover {
            background-color: rgba(30, 41, 59, 0.8);
            color: #e2e8f0;
        }
        
        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            z-index: 1000;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            width: 56px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
        }
        
        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(59, 130, 246, 0.6);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(51, 65, 85, 0.3);
            z-index: 999;
        }
        
        .content-area {
            padding-bottom: 80px;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        /* 键盘导航样式 */
        .keyboard-navigation *:focus {
            outline: 2px solid #3b82f6 !important;
            outline-offset: 2px !important;
        }
        
        /* 最小点击区域 */
        button, .nav-item, [role="button"] {
            min-width: 44px;
            min-height: 44px;
        }
        
        /* 加载状态 */
        .loading {
            pointer-events: none;
            opacity: 0.7;
        }
        
        /* 动画减少偏好 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部状态栏 -->
    <div class="bg-slate-800 text-slate-300 text-xs px-4 py-1 flex justify-between items-center">
        <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>在线</span>
        </div>
        <div class="flex items-center space-x-4">
            <span>深圳市</span>
            <span>14:30</span>
            <div class="flex items-center space-x-1">
                <i data-lucide="wifi" class="w-3 h-3"></i>
                <i data-lucide="battery" class="w-3 h-3"></i>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-area">
        <!-- 商铺页面内容 -->
        <div id="shops-page" class="page-content active">
            <!-- 顶部搜索区域 -->
            <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
                <div class="max-w-md mx-auto px-4 py-3">
                    <div class="flex items-center space-x-3">
                        <!-- 城市选择按钮 -->
                        <button class="flex items-center space-x-1 bg-slate-700 px-3 py-2 rounded-lg text-sm font-medium text-slate-300 hover:bg-slate-600 transition-colors">
                            <i data-lucide="map-pin" class="w-4 h-4"></i>
                            <span>深圳</span>
                            <i data-lucide="chevron-down" class="w-3 h-3"></i>
                        </button>
                        
                        <!-- 搜索输入框 -->
                        <div class="flex-1 relative">
                            <input type="text" placeholder="搜索商铺服务..." 
                                   class="w-full bg-slate-700 border-0 rounded-lg px-4 py-2 pl-10 pr-4 text-sm text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:bg-slate-600 transition-all">
                            <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                        </div>
                        
                        <!-- 扫一扫按钮 -->
                        <button class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors" data-action="openQRScanner" aria-label="扫一扫" title="扫一扫">
                            <i data-lucide="qr-code" class="w-5 h-5 text-slate-300" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 四大板块导航 -->
            <div class="bg-slate-800 border-b border-slate-700">
                <div class="max-w-md mx-auto px-4">
                    <div class="module-nav flex items-center justify-between py-3" role="navigation" aria-label="功能模块">
                        <button class="nav-item active flex flex-col items-center space-y-1 px-4 py-2 rounded-lg" data-module="shops" role="button" tabindex="0" aria-label="商铺模块">
                            <i data-lucide="store" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="text-xs">商铺</span>
                        </button>
                        <a href="information.html" class="nav-item flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400" data-module="information" role="button" tabindex="0" aria-label="信息模块">
                            <i data-lucide="info" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="text-xs">信息</span>
                        </a>
                        <a href="transportation.html" class="nav-item flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400" data-module="transportation" role="button" tabindex="0" aria-label="出行模块">
                            <i data-lucide="car" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="text-xs">出行</span>
                        </a>
                        <a href="errands.html" class="nav-item flex flex-col items-center space-y-1 px-4 py-2 rounded-lg text-slate-400" data-module="errands" role="button" tabindex="0" aria-label="跑腿模块">
                            <i data-lucide="package" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="text-xs">跑腿</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 商铺内容区域 -->
            <div class="max-w-md mx-auto px-4 py-4">
                <!-- 推荐商铺卡片 -->
                <div class="space-y-4">
                    <div class="bg-slate-800 rounded-xl p-4 card-hover">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=100&h=100&fit=crop&crop=center" alt="餐厅" class="w-16 h-16 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h3 class="font-semibold text-slate-200">川味小厨</h3>
                                    <div class="flex items-center space-x-1 text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm font-medium">4.8</span>
                                    </div>
                                </div>
                                <p class="text-sm text-slate-400 mb-2">正宗川菜 · 距离500m</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex space-x-2">
                                        <span class="px-2 py-1 bg-red-600 text-white text-xs rounded-full">热门</span>
                                        <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">营业中</span>
                                    </div>
                                    <span class="text-sm text-slate-300">人均¥45</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-slate-800 rounded-xl p-4 card-hover">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=center" alt="超市" class="w-16 h-16 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h3 class="font-semibold text-slate-200">便民超市</h3>
                                    <div class="flex items-center space-x-1 text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm font-medium">4.6</span>
                                    </div>
                                </div>
                                <p class="text-sm text-slate-400 mb-2">生活用品 · 距离200m</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex space-x-2">
                                        <span class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">24小时</span>
                                        <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full">配送</span>
                                    </div>
                                    <span class="text-sm text-slate-300">满30免配送费</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-slate-800 rounded-xl p-4 card-hover">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center" alt="咖啡店" class="w-16 h-16 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h3 class="font-semibold text-slate-200">星巴克咖啡</h3>
                                    <div class="flex items-center space-x-1 text-yellow-400">
                                        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
                                        <span class="text-sm font-medium">4.7</span>
                                    </div>
                                </div>
                                <p class="text-sm text-slate-400 mb-2">咖啡饮品 · 距离800m</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex space-x-2">
                                        <span class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full">新品</span>
                                        <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">营业中</span>
                                    </div>
                                    <span class="text-sm text-slate-300">人均¥35</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他页面内容占位 -->
        <div id="information-page" class="page-content">
            <div class="max-w-md mx-auto px-4 py-8 text-center">
                <i data-lucide="info" class="w-16 h-16 text-slate-400 mx-auto mb-4"></i>
                <h2 class="text-xl font-semibold text-slate-200 mb-2">信息板块</h2>
                <p class="text-slate-400">即将上线，敬请期待</p>
            </div>
        </div>

        <div id="transportation-page" class="page-content">
            <div class="max-w-md mx-auto px-4 py-8 text-center">
                <i data-lucide="car" class="w-16 h-16 text-slate-400 mx-auto mb-4"></i>
                <h2 class="text-xl font-semibold text-slate-200 mb-2">出行板块</h2>
                <p class="text-slate-400">即将上线，敬请期待</p>
            </div>
        </div>

        <div id="errands-page" class="page-content">
            <div class="max-w-md mx-auto px-4 py-8 text-center">
                <i data-lucide="package" class="w-16 h-16 text-slate-400 mx-auto mb-4"></i>
                <h2 class="text-xl font-semibold text-slate-200 mb-2">跑腿板块</h2>
                <p class="text-slate-400">即将上线，敬请期待</p>
            </div>
        </div>

        <div id="profile-page" class="page-content">
            <div class="max-w-md mx-auto px-4 py-8 text-center">
                <i data-lucide="user" class="w-16 h-16 text-slate-400 mx-auto mb-4"></i>
                <h2 class="text-xl font-semibold text-slate-200 mb-2">个人中心</h2>
                <p class="text-slate-400">即将上线，敬请期待</p>
            </div>
        </div>
    </div>

    <!-- 悬浮发布按钮 -->
    <button class="floating-action" data-action="publish" aria-label="发布内容" title="发布内容">
        <i data-lucide="plus" class="w-6 h-6 text-white" aria-hidden="true"></i>
    </button>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav" role="navigation" aria-label="主导航">
        <div class="max-w-md mx-auto px-4 py-2">
            <div class="flex items-center justify-around">
                <button class="flex flex-col items-center space-y-1 py-2 px-3 rounded-lg nav-item active" data-page="home" role="button" tabindex="0" aria-label="首页">
                    <i data-lucide="home" class="w-5 h-5" aria-hidden="true"></i>
                    <span class="text-xs">首页</span>
                </button>
                <a href="orders-management.html" class="flex flex-col items-center space-y-1 py-2 px-3 rounded-lg nav-item" data-page="orders" role="button" tabindex="0" aria-label="订单管理">
                    <div class="relative">
                        <i data-lucide="shopping-bag" class="w-5 h-5" aria-hidden="true"></i>
                        <div class="notification-badge" aria-label="3个待处理订单">3</div>
                    </div>
                    <span class="text-xs">订单</span>
                </a>
                <a href="message.html" class="flex flex-col items-center space-y-1 py-2 px-3 rounded-lg nav-item" data-page="messages" role="button" tabindex="0" aria-label="消息中心">
                    <div class="relative">
                        <i data-lucide="message-circle" class="w-5 h-5" aria-hidden="true"></i>
                        <div class="notification-badge" aria-label="5条未读消息">5</div>
                    </div>
                    <span class="text-xs">消息</span>
                </a>
                <a href="profile.html" class="flex flex-col items-center space-y-1 py-2 px-3 rounded-lg nav-item" data-page="profile" role="button" tabindex="0" aria-label="个人中心">
                    <i data-lucide="user" class="w-5 h-5" aria-hidden="true"></i>
                    <span class="text-xs">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            if (typeof LocalHelper !== 'undefined') {
                LocalHelper.App.init();
            }
            
            // 初始化 Lucide 图标
            lucide.createIcons();
            
            // 绑定模块切换事件
            const moduleNav = document.querySelector('.module-nav');
            if (moduleNav) {
                moduleNav.addEventListener('click', function(e) {
                    const moduleItem = e.target.closest('[data-module]');
                    if (moduleItem && moduleItem.tagName === 'BUTTON') {
                        e.preventDefault();
                        const module = moduleItem.dataset.module;
                        
                        // 移除所有激活状态
                        document.querySelectorAll('.module-nav .nav-item').forEach(item => {
                            item.classList.remove('active');
                            item.classList.add('text-slate-400');
                        });
                        
                        // 激活当前模块
                        moduleItem.classList.add('active');
                        moduleItem.classList.remove('text-slate-400');
                        
                        console.log('切换到模块:', module);
                        
                        // 显示模块内容
                        switchModuleContent(module);
                    }
                });
            }
            
            // 绑定底部导航事件
            const bottomNav = document.querySelector('.bottom-nav');
            if (bottomNav) {
                bottomNav.addEventListener('click', function(e) {
                    const navItem = e.target.closest('[data-page]');
                    if (navItem && navItem.tagName === 'BUTTON') {
                        e.preventDefault();
                        const page = navItem.dataset.page;
                        
                        // 移除所有激活状态
                        document.querySelectorAll('.bottom-nav .nav-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        
                        // 激活当前页面
                        navItem.classList.add('active');
                        
                        console.log('切换到页面:', page);
                    }
                });
            }
            
            // 绑定发布按钮事件
            const publishBtn = document.querySelector('[data-action="publish"]');
            if (publishBtn) {
                publishBtn.addEventListener('click', function() {
                    showToast('发布功能即将上线，敬请期待！', 'info');
                });
            }
            
            // 绑定扫码按钮事件
            const scanBtn = document.querySelector('[data-action="openQRScanner"]');
            if (scanBtn) {
                scanBtn.addEventListener('click', function() {
                    showToast('扫码功能即将上线，敬请期待！', 'info');
                });
            }
        });
        
        // 模块内容切换函数
        function switchModuleContent(module) {
            const contentArea = document.querySelector('.content-area');
            if (!contentArea) return;
            
            // 这里可以根据模块显示不同的内容
            switch(module) {
                case 'shops':
                    // 显示商铺内容（当前已显示）
                    break;
                case 'information':
                    // 显示信息内容
                    contentArea.innerHTML = '<div class="p-4 text-center text-slate-400">信息模块即将上线</div>';
                    break;
                case 'transportation':
                    // 显示出行内容
                    contentArea.innerHTML = '<div class="p-4 text-center text-slate-400">出行模块即将上线</div>';
                    break;
                case 'errands':
                    // 显示跑腿内容
                    contentArea.innerHTML = '<div class="p-4 text-center text-slate-400">跑腿模块即将上线</div>';
                    break;
            }
        }
        
        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 transform translate-x-full`;
            
            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    toast.classList.add('bg-green-500');
                    break;
                case 'error':
                    toast.classList.add('bg-red-500');
                    break;
                case 'warning':
                    toast.classList.add('bg-yellow-500');
                    break;
                default:
                    toast.classList.add('bg-blue-500');
            }
            
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>