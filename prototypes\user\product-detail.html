<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .swiper-container {
            overflow: hidden;
        }
        .swiper-wrapper {
            display: flex;
            transition: transform 0.3s ease;
        }
        .swiper-slide {
            flex-shrink: 0;
            width: 100%;
        }
        .product-tag {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
        .price-gradient {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }
        .shop-tag {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .image-overlay.active {
            display: flex;
        }
        .overlay-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 图片查看覆盖层 -->
    <div class="image-overlay" id="imageOverlay">
        <button class="absolute top-4 right-4 text-white text-2xl z-10" data-action="closeImageOverlay">
            <i data-lucide="x" class="w-8 h-8"></i>
        </button>
        <img class="overlay-image" id="overlayImage" src="" alt="">
    </div>
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button class="flex items-center justify-center w-8 h-8 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors" data-action="goBack">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                
                <h1 class="text-lg font-semibold text-white">商品详情</h1>
                
                <div class="flex space-x-2">
                    <button class="flex items-center justify-center w-8 h-8 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors" data-action="shareProduct">
                        <i data-lucide="share" class="w-4 h-4"></i>
                    </button>
                    <button class="flex items-center justify-center w-8 h-8 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors" data-action="showMoreOptions">
                        <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 商品图片展示区 -->
    <div class="bg-slate-900">
        <div class="max-w-md mx-auto">
            <!-- 主图片 -->
            <div class="relative h-80 bg-slate-800">
                <img id="mainImage" src="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=300&fit=crop" 
                     alt="商品主图" class="w-full h-full object-cover cursor-pointer" data-action="openImageOverlay" data-params='{"src":"https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=300&fit=crop"}'>
                <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1559056199-641a0ac8b55e -->
                
                <!-- 图片数量指示器 -->
                <div class="absolute top-4 right-4 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full">
                    <span id="currentImageIndex">1</span>/<span id="totalImages">6</span>
                </div>
                
                <!-- 收藏按钮 -->
                <button class="absolute top-4 left-4 flex items-center justify-center w-10 h-10 bg-black bg-opacity-30 text-white rounded-full hover:bg-opacity-50 transition-all" data-action="toggleFavorite">
                    <i data-lucide="heart" class="w-5 h-5"></i>
                </button>
            </div>
            
            <!-- 缩略图列表 -->
            <div class="p-3 bg-slate-800">
                <div class="flex space-x-2 overflow-x-auto scrollbar-hide">
                    <img src="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=80&h=80&fit=crop" 
                         alt="缩略图1" class="thumbnail-img w-16 h-16 object-cover rounded-lg cursor-pointer border-2 border-blue-500" 
                         data-action="changeMainImage" data-params='{"src":"https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=80&h=80&fit=crop", "index":0}'>
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1559056199-641a0ac8b55e -->
                    
                    <img src="https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=80&h=80&fit=crop" 
                         alt="缩略图2" class="thumbnail-img w-16 h-16 object-cover rounded-lg cursor-pointer border-2 border-transparent hover:border-blue-400" 
                         data-action="changeMainImage" data-params='{"src":"https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=80&h=80&fit=crop", "index":1}'>
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1447933601403-0c6688de566e -->
                    
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=80&h=80&fit=crop" 
                         alt="缩略图3" class="thumbnail-img w-16 h-16 object-cover rounded-lg cursor-pointer border-2 border-transparent hover:border-blue-400" 
                         data-action="changeMainImage" data-params='{"src":"https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=80&h=80&fit=crop", "index":2}'>
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b -->
                    
                    <img src="https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=80&h=80&fit=crop" 
                         alt="缩略图4" class="thumbnail-img w-16 h-16 object-cover rounded-lg cursor-pointer border-2 border-transparent hover:border-blue-400" 
                         data-action="changeMainImage" data-params='{"src":"https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=80&h=80&fit=crop", "index":3}'>
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb -->
                    
                    <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=80&h=80&fit=crop" 
                         alt="缩略图5" class="thumbnail-img w-16 h-16 object-cover rounded-lg cursor-pointer border-2 border-transparent hover:border-blue-400" 
                         data-action="changeMainImage" data-params='{"src":"https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=80&h=80&fit=crop", "index":4}'>
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1509042239860-f550ce710b93 -->
                    
                    <img src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=80&h=80&fit=crop" 
                         alt="缩略图6" class="thumbnail-img w-16 h-16 object-cover rounded-lg cursor-pointer border-2 border-transparent hover:border-blue-400" 
                         data-action="changeMainImage" data-params='{"src":"https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=80&h=80&fit=crop", "index":5}'>
                    <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1495474472287-4d71bcdd2085 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 商品基本信息 -->
    <div class="bg-slate-800 mt-2">
        <div class="max-w-md mx-auto px-4 py-5">
            <!-- 商品名称 -->
            <h1 class="text-xl font-bold text-white mb-3 leading-relaxed">精品手工咖啡豆 - 埃塞俄比亚耶加雪菲</h1>
            
            <!-- 商家信息标签 -->
            <div class="mb-4">
                <div class="flex items-center flex-wrap gap-2">
                    <span class="bg-green-600 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center">
                        <i data-lucide="shield-check" class="w-3 h-3 mr-1"></i>
                        已认证
                    </span>
                    <span class="bg-blue-600 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center">
                        <i data-lucide="navigation" class="w-3 h-3 mr-1"></i>
                        1.2km
                    </span>
                    <span class="bg-green-500 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center">
                        <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                        营业中
                    </span>
                    <span class="bg-orange-500 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center">
                        <i data-lucide="ticket" class="w-3 h-3 mr-1"></i>
                        优惠券
                    </span>
                </div>
            </div>
            
            <!-- 参考价格 -->
            <div class="flex items-center space-x-2 mb-4">
                <span class="text-lg font-semibold text-blue-400">参考价格：¥128</span>
                <span class="text-xs text-slate-400 bg-slate-700 px-2 py-1 rounded">仅供参考</span>
            </div>
            
            <!-- 商品描述 -->
            <div class="description-container text-slate-300 text-sm">
                <p class="description-text line-clamp-2">埃塞俄比亚耶加雪菲G1精品咖啡豆，产自海拔1800-2200米的高山区域，手工采摘，阳光晾晒，自然发酵处理。口感明亮清新，带有柑橘和花香调，酸度适中，回甘柔和，是咖啡爱好者喜爱的顶级单品。每一批次均经过专业杯测师鉴定，确保品质稳定。</p>
                <button class="expand-btn text-blue-400 text-xs mt-1 hidden" data-action="toggleDescription">展开全部</button>
            </div>
            
            <!-- 联系商家 -->
            <div class="pt-4 flex space-x-2">
                <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-xl flex items-center justify-center space-x-2" data-action="contactMerchant">
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span>私信商家</span>
                </button>
                
                <button class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-xl flex items-center justify-center space-x-2" data-action="callMerchant">
                    <i data-lucide="phone" class="w-4 h-4"></i>
                    <span>拨打电话</span>
                </button>
                
                <button class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-xl flex items-center justify-center space-x-2" data-action="getLocation">
                    <i data-lucide="map-pin" class="w-4 h-4"></i>
                    <span>导航</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 相关推荐 -->
    <div class="bg-slate-800 rounded-lg p-4 mb-6">
        <h3 class="text-lg font-semibold text-white mb-4">相关推荐</h3>
        <div class="grid grid-cols-2 gap-3">
            <div class="bg-slate-700 rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors">
                <img src="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=120&h=80&fit=crop" alt="推荐商品" class="w-full h-20 object-cover rounded mb-2">
                <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1559056199-641a0ac8b55e -->
                <h4 class="text-white text-sm font-medium mb-1">蓝山咖啡豆</h4>
                <p class="text-blue-400 text-sm font-semibold">¥168</p>
            </div>
            <div class="bg-slate-700 rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors">
                <img src="https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=120&h=80&fit=crop" alt="推荐商品" class="w-full h-20 object-cover rounded mb-2">
                <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1447933601403-0c6688de566e -->
                <h4 class="text-white text-sm font-medium mb-1">手冲咖啡壶</h4>
                <p class="text-blue-400 text-sm font-semibold">¥89</p>
            </div>
            <div class="bg-slate-700 rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors">
                <img src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=120&h=80&fit=crop" alt="推荐商品" class="w-full h-20 object-cover rounded mb-2">
                <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1495474472287-4d71bcdd2085 -->
                <h4 class="text-white text-sm font-medium mb-1">意式浓缩</h4>
                <p class="text-blue-400 text-sm font-semibold">¥45</p>
            </div>
            <div class="bg-slate-700 rounded-lg p-3 cursor-pointer hover:bg-slate-600 transition-colors">
                <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=120&h=80&fit=crop" alt="推荐商品" class="w-full h-20 object-cover rounded mb-2">
                <!-- 图片来源: Unsplash - https://images.unsplash.com/photo-1509042239860-f550ce710b93 -->
                <h4 class="text-white text-sm font-medium mb-1">咖啡杯套装</h4>
                <p class="text-blue-400 text-sm font-semibold">¥128</p>
            </div>
        </div>
    </div>

    <!-- 服务提醒 -->
    <div class="p-4 bg-slate-700 rounded-xl">
        <div class="flex items-center justify-between cursor-pointer" data-action="toggleServiceReminder">
            <div class="flex items-center space-x-2">
                <i data-lucide="info" class="w-5 h-5 text-blue-400"></i>
                <span class="font-medium">服务提醒</span>
            </div>
            <i data-lucide="chevron-down" class="w-5 h-5 text-slate-400 transform transition-transform" id="reminder-arrow"></i>
        </div>
        <div id="service-reminder-content" class="hidden mt-3 text-sm text-slate-300 space-y-2">
            <div class="flex items-start space-x-2">
                <i data-lucide="alert-circle" class="w-4 h-4 text-yellow-400 mt-0.5"></i>
                <p>本平台仅提供信息展示服务，不负责交易安全和商品质量</p>
            </div>
            <div class="flex items-start space-x-2">
                <i data-lucide="shield" class="w-4 h-4 text-green-400 mt-0.5"></i>
                <p>建议线下购买前仔细验货，了解商家资质</p>
            </div>
            <div class="flex items-start space-x-2">
                <i data-lucide="alert-triangle" class="w-4 h-4 text-red-400 mt-0.5"></i>
                <p>如遇服务问题，可向平台投诉举报</p>
            </div>
        </div>
    </div>

    <!-- 底部四大板块导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-slate-900 border-t border-slate-700 safe-area-pb">
        <div class="max-w-md mx-auto px-4">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center space-y-1 py-2 text-blue-400">
                    <i data-lucide="store" class="w-5 h-5"></i>
                    <span class="text-xs font-medium">商铺</span>
                </button>
                <button onclick="window.location.href='message.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5"></i>
                    <span class="text-xs">消息</span>
                </button>
                <button onclick="showPublishModal()" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="plus-circle" class="w-6 h-6"></i>
                    <span class="text-xs">发布</span>
                </button>
                <button onclick="window.location.href='help.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="help-circle" class="w-5 h-5"></i>
                    <span class="text-xs">帮助</span>
                </button>
                <button onclick="window.location.href='profile.html'" class="flex flex-col items-center space-y-1 py-2 text-slate-400 hover:text-slate-300 transition-colors">
                    <i data-lucide="user" class="w-5 h-5"></i>
                    <span class="text-xs">我的</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 发布选择弹窗 -->
    <div id="publishModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-slate-800 rounded-lg max-w-sm w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-white mb-2">选择发布类型</h3>
                    <p class="text-slate-400 text-sm">请选择您要发布的信息类型</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button onclick="navigateToPublish('shops')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">商铺</span>
                        <span class="text-white/80 text-xs text-center">发布店铺信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('information')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-colors">
                        <i data-lucide="info" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">信息</span>
                        <span class="text-white/80 text-xs text-center">发布各类信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('transportation')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-colors">
                        <i data-lucide="car" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">出行</span>
                        <span class="text-white/80 text-xs text-center">发布出行信息</span>
                    </button>
                    
                    <button onclick="navigateToPublish('errands')" class="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-colors">
                        <i data-lucide="package" class="w-8 h-8 text-white"></i>
                        <span class="text-white font-medium">跑腿</span>
                        <span class="text-white/80 text-xs text-center">发布跑腿需求</span>
                    </button>
                </div>
                
                <button onclick="closePublishModal()" class="w-full py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <!-- 底部安全距离 -->
    <div class="h-20"></div>

    <script>
        // 初始化 Lucide 图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            
            // 初始化事件处理
            initProductDetailEvents();
            
            // 检查描述文字是否需要展开按钮
            document.querySelectorAll('.description-container').forEach(container => {
                const textElement = container.querySelector('.description-text');
                const expandBtn = container.querySelector('.expand-btn');
                
                // 检查文字是否被截断
                if (textElement.scrollHeight > textElement.clientHeight) {
                    expandBtn.classList.remove('hidden');
                }
            });
        });
        
        // 初始化产品详情页面事件
        function initProductDetailEvents() {
            // 图片URL数组
            const imageUrls = [
                'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=300&fit=crop',
                'https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop',
                'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
                'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=400&h=300&fit=crop',
                'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop',
                'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop'
            ];
            
            // 处理所有data-action按钮点击事件
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (!target) return;
                
                const action = target.dataset.action;
                const params = target.dataset.params ? JSON.parse(target.dataset.params) : {};
                
                switch(action) {
                    case 'goBack':
                        window.history.back();
                        break;
                        
                    case 'shareProduct':
                        alert('分享功能即将上线');
                        break;
                        
                    case 'showMoreOptions':
                        alert('更多选项功能即将上线');
                        break;
                        
                    case 'openImageOverlay':
                        openImageOverlay(params.src || target.src);
                        break;
                        
                    case 'closeImageOverlay':
                        closeImageOverlay();
                        break;
                        
                    case 'changeMainImage':
                        changeMainImage(params.src, params.index);
                        break;
                        
                    case 'toggleFavorite':
                        const icon = target.querySelector('[data-lucide="heart"]');
                        icon.classList.toggle('fill-current');
                        icon.classList.toggle('text-red-500');
                        console.log('收藏状态切换');
                        break;
                        
                    case 'contactMerchant':
                        alert('正在打开私信对话...');
                        break;
                        
                    case 'callMerchant':
                        alert('正在为您拨打商家电话...');
                        break;
                        
                    case 'getLocation':
                        alert('正在为您打开导航...');
                        break;
                        
                    case 'toggleServiceReminder':
                        const content = document.getElementById('service-reminder-content');
                        const arrow = document.getElementById('reminder-arrow');
                        
                        if (content.classList.contains('hidden')) {
                            content.classList.remove('hidden');
                            arrow.style.transform = 'rotate(180deg)';
                        } else {
                            content.classList.add('hidden');
                            arrow.style.transform = 'rotate(0deg)';
                        }
                        break;
                        
                    case 'toggleDescription':
                        const container = target.closest('.description-container');
                        const textElement = container.querySelector('.description-text');
                        
                        if (textElement.classList.contains('line-clamp-2')) {
                            textElement.classList.remove('line-clamp-2');
                            target.textContent = '收起';
                        } else {
                            textElement.classList.add('line-clamp-2');
                            target.textContent = '展开全部';
                        }
                        break;
                }
            });
            
            // 点击覆盖层背景关闭
            document.getElementById('imageOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeImageOverlay();
                }
            });
            
            // ESC键关闭图片查看
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeImageOverlay();
                }
            });
        }
        
        // 图片切换功能
        function changeMainImage(src, index) {
            const mainImage = document.getElementById('mainImage');
            const currentIndexSpan = document.getElementById('currentImageIndex');
            const thumbnails = document.querySelectorAll('.thumbnail-img');
            
            // 更新主图
            mainImage.src = src.replace('w=80&h=80', 'w=400&h=300');
            
            // 更新主图的data-params属性
            mainImage.dataset.params = JSON.stringify({src: mainImage.src});
            
            // 更新索引显示
            currentIndexSpan.textContent = index + 1;
            
            // 更新缩略图边框
            thumbnails.forEach((thumb, i) => {
                if (i === index) {
                    thumb.classList.remove('border-transparent');
                    thumb.classList.add('border-blue-500');
                } else {
                    thumb.classList.remove('border-blue-500');
                    thumb.classList.add('border-transparent');
                }
            });
        }
        
        // 图片查看功能
        function openImageOverlay(src) {
            const overlay = document.getElementById('imageOverlay');
            const overlayImage = document.getElementById('overlayImage');
            overlayImage.src = src;
            overlay.classList.add('active');
        }

        function closeImageOverlay() {
            const overlay = document.getElementById('imageOverlay');
            overlay.classList.remove('active');
        }
    </script>
</body>
</html>