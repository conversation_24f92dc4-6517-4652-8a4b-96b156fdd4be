/**
 * 手风琴组件
 * 
 * 用于创建可折叠的内容面板
 * 
 * 使用方法:
 * <div data-component="accordion" data-section-id="uniqueId">
 *   <div class="toggle-section">
 *     <span>标题</span>
 *     <i class="section-arrow"></i>
 *   </div>
 *   <div class="section-content">
 *     内容区域
 *   </div>
 * </div>
 * 
 * 配置选项:
 * - data-config-expanded="true" - 默认展开
 * - data-config-toggle-class="active-class" - 激活时添加到标题的类
 * - data-config-animation="true" - 启用动画效果
 */

class Accordion {
    constructor(element, options) {
        this.element = element;
        this.sectionId = element.dataset.sectionId || '';
        this.options = {
            expanded: element.dataset.configExpanded === 'true',
            toggleClass: element.dataset.configToggleClass || '',
            animation: element.dataset.configAnimation !== 'false',
            ...options
        };
        
        this.toggleButton = element.querySelector('.toggle-section');
        this.content = element.querySelector('.section-content');
        this.arrow = element.querySelector('.section-arrow');
        
        this.init();
    }
    
    init() {
        if (this.toggleButton && this.content) {
            // 绑定事件
            this.toggleButton.addEventListener('click', this.toggle.bind(this));
            
            // 设置初始状态
            if (this.options.expanded) {
                this.expand();
            } else {
                this.collapse();
            }
            
            // 添加ARIA属性以提高可访问性
            this.toggleButton.setAttribute('aria-expanded', this.options.expanded ? 'true' : 'false');
            this.toggleButton.setAttribute('aria-controls', `content-${this.sectionId}`);
            
            if (this.content) {
                this.content.id = `content-${this.sectionId}`;
                this.content.setAttribute('aria-hidden', this.options.expanded ? 'false' : 'true');
            }
        }
    }
    
    toggle() {
        if (this.content.classList.contains('hidden')) {
            this.expand();
        } else {
            this.collapse();
        }
        
        // 触发自定义事件
        this.element.dispatchEvent(new CustomEvent('accordion:toggled', {
            bubbles: true,
            detail: { expanded: !this.content.classList.contains('hidden') }
        }));
    }
    
    expand() {
        this.content.classList.remove('hidden');
        if (this.arrow) {
            this.arrow.style.transform = 'rotate(180deg)';
        }
        if (this.options.toggleClass && this.toggleButton) {
            this.toggleButton.classList.add(this.options.toggleClass);
        }
        
        // 更新ARIA状态
        this.toggleButton.setAttribute('aria-expanded', 'true');
        this.content.setAttribute('aria-hidden', 'false');
    }
    
    collapse() {
        this.content.classList.add('hidden');
        if (this.arrow) {
            this.arrow.style.transform = 'rotate(0deg)';
        }
        if (this.options.toggleClass && this.toggleButton) {
            this.toggleButton.classList.remove(this.options.toggleClass);
        }
        
        // 更新ARIA状态
        this.toggleButton.setAttribute('aria-expanded', 'false');
        this.content.setAttribute('aria-hidden', 'true');
    }
    
    destroy() {
        if (this.toggleButton) {
            this.toggleButton.removeEventListener('click', this.toggle);
        }
    }
}

// 注册手风琴组件
if (window.componentRegistry) {
    componentRegistry.register('accordion', Accordion);
} 