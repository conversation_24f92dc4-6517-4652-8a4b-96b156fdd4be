<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跑腿详情 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="../scripts/common.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .wrapper {
            width: 100vw;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="history.back()" class="flex items-center justify-center w-10 h-10 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5 text-slate-300"></i>
                </button>
                <h1 class="text-lg font-semibold text-slate-200">跑腿详情</h1>
                <div class="w-10"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-md mx-auto px-4 py-6 space-y-6">
        <!-- 安全提示和违禁品清单 -->
        <div id="safety-banner" class="bg-amber-500/10 border border-amber-500/30 rounded-lg p-3 relative">
            <div class="flex items-start space-x-2">
                <i data-lucide="triangle-alert" class="w-5 h-5 text-amber-400 mt-0.5 flex-shrink-0"></i>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-amber-400 mb-2">安全提示 & 违禁品说明</h4>
                    <p class="text-xs text-amber-300/90">
                        请勿委托或接受任何违禁品订单，包括但不限于危险化学品、易燃易爆物品、管制刀具、毒品等。所有交易需符合平台规定和法律法规。
                    </p>
                    <button class="text-xs text-amber-400 hover:text-amber-300 mt-2 underline" onclick="showProhibitedItems()">查看违禁品清单</button>
                </div>
                <div class="flex items-center space-x-2 pl-2">
                    <span id="countdown-timer" class="text-xs text-amber-400 font-mono">5s</span>
                    <button id="close-banner-btn" class="text-amber-400 hover:text-amber-300">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 跑腿任务详情卡片 -->
        <div class="bg-slate-800 rounded-xl shadow-sm border border-slate-700 p-4">
            <!-- 标签区域 -->
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-2 flex-wrap gap-1">
                    <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full font-medium">代买</span>
                    <span class="text-green-400 font-medium text-xs px-2 py-1 rounded-full bg-green-500/20 border border-green-500/30">已认证</span>
                    <span class="bg-slate-600 text-slate-300 text-xs px-2 py-1 rounded-full">1.5km</span>
                    <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">电话</span>
                    <span class="bg-orange-500/20 text-orange-400 px-2 py-1 rounded text-xs">剩余2天</span>
                </div>
                <span class="text-xs text-slate-400">1小时前</span>
            </div>
            
            <!-- 标题和报酬 -->
            <div class="flex items-center space-x-2 mb-3">
                <h3 class="font-semibold text-slate-200 text-lg flex-1">代买星巴克咖啡</h3>
                <span class="bg-green-600 text-white px-2 py-1 rounded text-sm font-medium">
                    跑腿费20元
                </span>
            </div>
            
            <!-- 起始点信息 -->
            <div class="space-y-2 mb-3">
                <div class="text-sm text-slate-300">
                    <div class="flex items-center mb-1">
                        <i data-lucide="map-pin" class="w-4 h-4 mr-2 text-green-400"></i>
                        <span class="text-slate-200 font-medium">取货地点：</span>
                        <span class="ml-1">星巴克南山店</span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="navigation" class="w-4 h-4 mr-2 text-blue-400"></i>
                        <span class="text-slate-200 font-medium">送达地点：</span>
                        <span class="ml-1">科技园地铁站</span>
                    </div>
                </div>
            </div>
            
            <!-- 详细内容 -->
            <div class="mb-3">
                <h4 class="text-sm font-medium text-blue-400 mb-2 flex items-center">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2 text-blue-400"></i>
                    任务详情
                </h4>
                <div class="text-sm text-slate-300 space-y-2 pl-6">
                    <p><strong>需要代买：</strong>2杯美式咖啡，大杯，无糖</p>
                    <p><strong>支付方式：</strong>咖啡费用我转账，跑腿费现金支付</p>
                    <p><strong>时间要求：</strong>请在下午3点前送达</p>
                    <p><strong>特别说明：</strong>到达后请电话联系，谢谢配合！</p>
                    <p><strong>联系电话：</strong>138****8888（点击电话标签可拨打）</p>
                </div>
                
                <!-- 相关图片 -->
                <div class="mt-4">
                    <h5 class="text-sm font-medium text-slate-300 mb-3 flex items-center">
                        <i data-lucide="image" class="w-4 h-4 mr-2 text-slate-400"></i>
                        相关图片
                    </h5>
                    <div class="grid grid-cols-3 gap-2">
                        <!-- 图片1：星巴克店面 -->
                        <div class="relative group cursor-pointer" onclick="viewImage('https://images.unsplash.com/photo-1549340748-8b90a3f8e7d3?w=400&h=300&fit=crop')">
                            <img src="https://images.unsplash.com/photo-1549340748-8b90a3f8e7d3?w=120&h=90&fit=crop" 
                                 alt="星巴克店面" 
                                 class="w-full h-20 object-cover rounded-lg border border-slate-600 group-hover:border-blue-400 transition-colors">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center">
                                <i data-lucide="zoom-in" class="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity"></i>
                            </div>
                        </div>
                        
                        <!-- 图片2：咖啡产品 -->
                        <div class="relative group cursor-pointer" onclick="viewImage('https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop')">
                            <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=120&h=90&fit=crop" 
                                 alt="美式咖啡" 
                                 class="w-full h-20 object-cover rounded-lg border border-slate-600 group-hover:border-blue-400 transition-colors">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center">
                                <i data-lucide="zoom-in" class="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity"></i>
                            </div>
                        </div>
                        
                        <!-- 图片3：送达地点 -->
                        <div class="relative group cursor-pointer" onclick="viewImage('https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=400&h=300&fit=crop')">
                            <img src="https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=120&h=90&fit=crop" 
                                 alt="地铁站" 
                                 class="w-full h-20 object-cover rounded-lg border border-slate-600 group-hover:border-blue-400 transition-colors">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center">
                                <i data-lucide="zoom-in" class="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-xs text-slate-500 mt-2">点击图片查看大图</p>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex items-center justify-end space-x-4 mt-4 pt-3 border-t border-slate-700">
                    <button onclick="collectTask()" class="flex items-center space-x-1 text-slate-400 hover:text-yellow-400 transition-colors">
                        <i data-lucide="heart" class="w-4 h-4"></i>
                        <span class="text-sm">收藏</span>
                    </button>
                    <button onclick="shareTask()" class="flex items-center space-x-1 text-slate-400 hover:text-blue-400 transition-colors">
                        <i data-lucide="share" class="w-4 h-4"></i>
                        <span class="text-sm">转发</span>
                    </button>
                    <button onclick="reportTask()" class="flex items-center space-x-1 text-slate-400 hover:text-red-400 transition-colors">
                        <i data-lucide="flag" class="w-4 h-4"></i>
                        <span class="text-sm">举报</span>
                    </button>
                </div>
            </div>
        </div>



        <!-- 免责声明 -->
        <div class="bg-slate-800 rounded-xl p-4 space-y-3">
            <h3 class="text-lg font-semibold text-slate-200 flex items-center space-x-2">
                <i data-lucide="shield-alert" class="w-5 h-5"></i>
                <span>免责声明</span>
            </h3>
            <div class="text-slate-300 text-sm space-y-2">
                <p>• 本平台仅为信息发布平台，不参与具体交易</p>
                <p>• 用户应自行核实发布信息的真实性和准确性</p>
                <p>• 交易风险自担，平台不承担任何直接或间接损失</p>
                <p>• 如发生争议，建议双方协商解决</p>
                <button class="text-xs text-blue-400 hover:text-blue-300 underline" onclick="showFullDisclaimer()">查看完整免责声明</button>
            </div>
        </div>

        <!-- 相关推荐 -->
        <div class="bg-slate-800 rounded-xl p-4">
            <h3 class="text-lg font-semibold text-slate-200 mb-4 flex items-center space-x-2">
                <i data-lucide="grid-3x3" class="w-5 h-5"></i>
                <span>相关推荐</span>
            </h3>
            <div class="space-y-3">
                <!-- 推荐任务1 -->
                <div class="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer">
                    <div class="flex items-start justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">代取</span>
                            <span class="text-slate-300 text-sm">1.2km</span>
                        </div>
                        <span class="text-green-400 text-sm font-medium">¥15</span>
                    </div>
                    <h4 class="text-slate-200 font-medium text-sm mb-1">代取快递包裹</h4>
                    <p class="text-slate-400 text-xs">丰巢快递柜 → 科技园B座</p>
                </div>
                
                <!-- 推荐任务2 -->
                <div class="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer">
                    <div class="flex items-start justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">代买</span>
                            <span class="text-slate-300 text-sm">0.8km</span>
                        </div>
                        <span class="text-green-400 text-sm font-medium">¥18</span>
                    </div>
                    <h4 class="text-slate-200 font-medium text-sm mb-1">代买午餐</h4>
                    <p class="text-slate-400 text-xs">麦当劳 → 腾讯大厦</p>
                </div>
                
                <!-- 推荐任务3 -->
                <div class="bg-slate-700 rounded-lg p-3 hover:bg-slate-600 transition-colors cursor-pointer">
                    <div class="flex items-start justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <span class="bg-orange-600 text-white text-xs px-2 py-1 rounded-full">代送</span>
                            <span class="text-slate-300 text-sm">2.1km</span>
                        </div>
                        <span class="text-green-400 text-sm font-medium">¥25</span>
                    </div>
                    <h4 class="text-slate-200 font-medium text-sm mb-1">代送文件</h4>
                    <p class="text-slate-400 text-xs">南山科技园 → 福田CBD</p>
                </div>
            </div>
        </div>
    </div>



    <script>
        // 初始化图标
        lucide.createIcons();

        // 倒计时自动关闭安全提示
        document.addEventListener('DOMContentLoaded', () => {
            const banner = document.getElementById('safety-banner');
            if (!banner) return;

            const closeBtn = document.getElementById('close-banner-btn');
            const countdownTimer = document.getElementById('countdown-timer');
            
            let countdown = 5;
            
            const interval = setInterval(() => {
                countdown--;
                if (countdownTimer) {
                    countdownTimer.textContent = `${countdown}s`;
                }
                if (countdown <= 0) {
                    clearInterval(interval);
                    banner.style.transition = 'opacity 0.5s ease';
                    banner.style.opacity = '0';
                    setTimeout(() => banner.style.display = 'none', 500);
                }
            }, 1000);

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    clearInterval(interval);
                    banner.style.transition = 'opacity 0.5s ease';
                    banner.style.opacity = '0';
                    setTimeout(() => banner.style.display = 'none', 500);
                });
            }
        });

        // 显示违禁品清单
        function showProhibitedItems() {
            const prohibitedList = `
违禁物品清单：

• 危险化学品：易燃易爆、有毒有害物质
• 管制刀具：刀具、尖锐器械
• 违法物品：毒品、仿真枪支、管制药品
• 贵重物品：现金、珠宝、有价证券
• 易碎物品：玻璃制品、陶瓷等（需特殊包装）
• 活体动物：宠物、家禽等
• 食品药品：需要特殊保存条件的物品
• 其他：法律法规禁止运输的物品

如有疑问请联系客服：400-123-4567
            `;
            alert(prohibitedList);
        }

        // 显示完整免责声明
        function showFullDisclaimer() {
            const disclaimer = `
免责声明：

1. 平台性质：本平台仅为信息发布平台，不参与具体交易。

2. 信息真实性：用户应自行核实发布信息的真实性和准确性。

3. 交易风险：用户在使用本服务时应谨慎判断，交易风险自担。

4. 平台责任：平台不对因使用本服务产生的任何直接或间接损失承担责任。

5. 争议处理：如发生争议，建议双方协商解决，必要时可寻求法律途径。

6. 服务变更：平台有权随时修改或终止服务，恕不另行通知。

详细条款请查看《用户协议》和《隐私政策》
            `;
            alert(disclaimer);
        }

        // 收藏任务
        function collectTask() {
            showToast('已添加到收藏');
        }

        // 转发任务
        function shareTask() {
            if (navigator.share) {
                navigator.share({
                    title: '代买星巴克咖啡',
                    text: '跑腿费20元，距离1.5km',
                    url: window.location.href
                });
            } else {
                showToast('链接已复制到剪贴板');
            }
        }

        // 举报任务
        function reportTask() {
            if (confirm('确定要举报这个任务吗？')) {
                showToast('举报已提交，我们会尽快处理');
            }
        }

        // 查看图片大图
        function viewImage(imageUrl) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4';
            modal.onclick = () => closeImageModal(modal);
            
            // 创建图片容器
            const imageContainer = document.createElement('div');
            imageContainer.className = 'relative max-w-4xl max-h-full';
            imageContainer.onclick = (e) => e.stopPropagation();
            
            // 创建图片
            const img = document.createElement('img');
            img.src = imageUrl;
            img.className = 'max-w-full max-h-full object-contain rounded-lg';
            img.alt = '任务相关图片';
            
            // 创建关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.className = 'absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-colors';
            closeBtn.innerHTML = '<i data-lucide="x" class="w-5 h-5"></i>';
            closeBtn.onclick = () => closeImageModal(modal);
            
            imageContainer.appendChild(img);
            imageContainer.appendChild(closeBtn);
            modal.appendChild(imageContainer);
            document.body.appendChild(modal);
            
            // 图标已在页面初始化时加载
            
            // 添加键盘事件监听
            const handleKeyPress = (e) => {
                if (e.key === 'Escape') {
                    closeImageModal(modal);
                    document.removeEventListener('keydown', handleKeyPress);
                }
            };
            document.addEventListener('keydown', handleKeyPress);
        }
        
        // 关闭图片模态框
        function closeImageModal(modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            }, 200);
        }

        // 查看图片大图
        function viewImage(imageUrl) {
            // 创建图片查看器模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4';
            modal.onclick = () => closeImageViewer(modal);
            
            const imageContainer = document.createElement('div');
            imageContainer.className = 'relative max-w-full max-h-full';
            imageContainer.onclick = (e) => e.stopPropagation();
            
            const image = document.createElement('img');
            image.src = imageUrl;
            image.className = 'max-w-full max-h-full object-contain rounded-lg';
            image.alt = '任务相关图片';
            
            const closeBtn = document.createElement('button');
            closeBtn.className = 'absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-colors';
            closeBtn.innerHTML = '<i data-lucide="x" class="w-5 h-5"></i>';
            closeBtn.onclick = () => closeImageViewer(modal);
            
            imageContainer.appendChild(image);
            imageContainer.appendChild(closeBtn);
            modal.appendChild(imageContainer);
            document.body.appendChild(modal);
            
            // 图标已在页面初始化时加载
            
            // 添加键盘事件监听
            const handleKeyPress = (e) => {
                if (e.key === 'Escape') {
                    closeImageViewer(modal);
                    document.removeEventListener('keydown', handleKeyPress);
                }
            };
            document.addEventListener('keydown', handleKeyPress);
        }
        
        // 关闭图片查看器
        function closeImageViewer(modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            }, 200);
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all duration-300 transform translate-x-full`;
            
            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    toast.classList.add('bg-green-500');
                    break;
                case 'error':
                    toast.classList.add('bg-red-500');
                    break;
                case 'warning':
                    toast.classList.add('bg-yellow-500');
                    break;
                default:
                    toast.classList.add('bg-blue-500');
            }
            
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 页面加载完成后初始化图标
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>