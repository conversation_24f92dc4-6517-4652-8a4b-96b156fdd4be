<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系历史 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
        }
        .contact-card {
            background: rgba(30, 30, 63, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .contact-card:hover {
            border-color: rgba(59, 130, 246, 0.6);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="text-white">
    <!-- 顶部导航 -->
    <div class="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <button onclick="goBack()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="arrow-left" class="w-6 h-6"></i>
            </button>
            <h1 class="text-lg font-semibold">联系历史</h1>
            <button onclick="clearHistory()" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                <i data-lucide="trash-2" class="w-6 h-6"></i>
            </button>
        </div>
    </div>

    <!-- 筛选标签 -->
    <div class="p-4">
        <div class="flex space-x-2 overflow-x-auto">
            <button onclick="filterContacts('all')" class="filter-btn active bg-blue-600 text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">
                全部
            </button>
            <button onclick="filterContacts('call')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                通话记录
            </button>
            <button onclick="filterContacts('message')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                消息记录
            </button>
            <button onclick="filterContacts('shop')" class="filter-btn bg-slate-700 text-slate-300 px-4 py-2 rounded-full text-sm whitespace-nowrap hover:bg-slate-600">
                商家联系
            </button>
        </div>
    </div>

    <!-- 联系历史列表 -->
    <div class="p-4 space-y-3">
        <!-- 联系记录1 -->
        <div class="contact-card rounded-lg p-4 transition-all duration-300" data-type="call">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i data-lucide="phone" class="w-6 h-6 text-white"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-medium text-white truncate">张师傅修理店</h3>
                        <span class="text-xs text-slate-400">今天 14:30</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">通话时长：3分25秒</p>
                    <div class="flex items-center space-x-2">
                        <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">已接通</span>
                        <span class="text-xs text-slate-400">家电维修</span>
                    </div>
                </div>
                <button onclick="contactAgain('13800138001')" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <i data-lucide="phone" class="w-5 h-5 text-green-400"></i>
                </button>
            </div>
        </div>

        <!-- 联系记录2 -->
        <div class="contact-card rounded-lg p-4 transition-all duration-300" data-type="message">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i data-lucide="message-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-medium text-white truncate">小李跑腿</h3>
                        <span class="text-xs text-slate-400">昨天 16:45</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">"好的，我马上过去取快递"</p>
                    <div class="flex items-center space-x-2">
                        <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">已回复</span>
                        <span class="text-xs text-slate-400">跑腿服务</span>
                    </div>
                </div>
                <button onclick="sendMessage('小李跑腿')" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5 text-blue-400"></i>
                </button>
            </div>
        </div>

        <!-- 联系记录3 -->
        <div class="contact-card rounded-lg p-4 transition-all duration-300" data-type="shop">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i data-lucide="store" class="w-6 h-6 text-white"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-medium text-white truncate">美味餐厅</h3>
                        <span class="text-xs text-slate-400">2天前 19:20</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">咨询外卖配送服务</p>
                    <div class="flex items-center space-x-2">
                        <span class="bg-orange-600 text-white text-xs px-2 py-1 rounded-full">商家</span>
                        <span class="text-xs text-slate-400">餐饮服务</span>
                    </div>
                </div>
                <button onclick="viewShop('美味餐厅')" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <i data-lucide="external-link" class="w-5 h-5 text-orange-400"></i>
                </button>
            </div>
        </div>

        <!-- 联系记录4 -->
        <div class="contact-card rounded-lg p-4 transition-all duration-300" data-type="call">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i data-lucide="phone-missed" class="w-6 h-6 text-white"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-medium text-white truncate">王师傅搬家</h3>
                        <span class="text-xs text-slate-400">3天前 10:15</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">未接通</p>
                    <div class="flex items-center space-x-2">
                        <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full">未接</span>
                        <span class="text-xs text-slate-400">搬家服务</span>
                    </div>
                </div>
                <button onclick="contactAgain('13900139002')" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <i data-lucide="phone" class="w-5 h-5 text-green-400"></i>
                </button>
            </div>
        </div>

        <!-- 联系记录5 -->
        <div class="contact-card rounded-lg p-4 transition-all duration-300" data-type="message">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i data-lucide="message-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                        <h3 class="font-medium text-white truncate">二手车行</h3>
                        <span class="text-xs text-slate-400">1周前 15:30</span>
                    </div>
                    <p class="text-sm text-slate-300 mb-2">"这辆车还在吗？可以看车吗？"</p>
                    <div class="flex items-center space-x-2">
                        <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">已读</span>
                        <span class="text-xs text-slate-400">二手交易</span>
                    </div>
                </div>
                <button onclick="sendMessage('二手车行')" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <i data-lucide="message-circle" class="w-5 h-5 text-blue-400"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 空状态 -->
    <div id="empty-state" class="hidden p-8 text-center">
        <div class="w-20 h-20 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <i data-lucide="phone-off" class="w-10 h-10 text-slate-400"></i>
        </div>
        <h3 class="text-lg font-medium text-white mb-2">暂无联系记录</h3>
        <p class="text-slate-400 text-sm">您还没有联系过任何商家或服务提供者</p>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        function filterContacts(type) {
            const cards = document.querySelectorAll('.contact-card');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600', 'text-white');
                btn.classList.add('bg-slate-700', 'text-slate-300');
            });
            
            event.target.classList.add('active', 'bg-blue-600', 'text-white');
            event.target.classList.remove('bg-slate-700', 'text-slate-300');
            
            // 筛选卡片
            cards.forEach(card => {
                if (type === 'all' || card.dataset.type === type) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function contactAgain(phone) {
            alert(`正在拨打电话：${phone}\n\n请稍候...`);
        }

        function sendMessage(name) {
            alert(`正在打开与"${name}"的聊天窗口...`);
        }

        function viewShop(name) {
            alert(`正在跳转到"${name}"的商家详情页...`);
        }

        function clearHistory() {
            if (confirm('确定要清空所有联系历史吗？\n\n此操作不可撤销！')) {
                alert('联系历史已清空');
                // 这里可以添加实际的清空逻辑
            }
        }

        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>