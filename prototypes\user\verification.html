<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证 - 本地助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        html, body {
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        img, .container, .wrapper {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 100%;
        }
        .wrapper {
            width: 100%;
            max-width: 100%;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        .step-active {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
        }
        .step-completed {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .step-pending {
            background: #475569;
            color: #94a3b8;
        }
        .upload-area {
            border: 2px dashed #475569;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3b82f6;
            background-color: #1e293b;
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: #1e3a8a;
        }
    </style>
</head>
<body class="bg-slate-900 min-h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-slate-800 shadow-sm sticky top-0 z-50">
        <div class="max-w-md mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <button data-action="back" class="flex items-center justify-center w-8 h-8 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
                
                <h1 class="text-lg font-semibold text-slate-100">实名认证</h1>
                
                <div class="w-8 h-8"></div>
            </div>
        </div>
    </div>

    <!-- 认证内容区域 -->
    <div class="max-w-md mx-auto px-4 py-6">
        <!-- 实名认证表单 -->
        <div id="verificationForm" class="space-y-6">
            <!-- 提示信息 -->
            <div class="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-4">
                <div class="flex items-start space-x-3">
                    <i data-lucide="info" class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0"></i>
                    <div class="text-sm text-blue-200">
                        <p class="font-medium mb-1">实名认证说明</p>
                        <p>为了保障平台安全，请完成实名认证。您的个人信息将严格保密，仅用于身份验证。</p>
                    </div>
                </div>
            </div>
            
            <!-- 手机号验证 -->
            <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-6">
                <h3 class="text-lg font-semibold text-slate-100 mb-4">手机号验证</h3>
                
                <div class="space-y-4">
                    <!-- 手机号码 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">手机号码</label>
                        <div class="flex space-x-3">
                            <input type="tel" placeholder="请输入手机号码" maxlength="11" id="phoneInput"
                                   class="flex-1 px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-slate-100 placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <button data-action="sendVerifyCode" id="sendCodeBtn"
                                    class="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors whitespace-nowrap">
                                发送验证码
                            </button>
                        </div>
                    </div>
                    
                    <!-- 验证码 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">验证码</label>
                        <input type="text" placeholder="请输入6位验证码" maxlength="6" id="verifyCodeInput"
                               class="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-slate-100 placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    </div>
                </div>
            </div>

            <!-- 身份证上传 -->
            <div class="bg-slate-800 rounded-lg shadow-sm border border-slate-700 p-6">
                <h3 class="text-lg font-semibold text-slate-100 mb-4">身份证上传</h3>
                
                <!-- 提示信息 -->
                <div class="bg-yellow-900 bg-opacity-30 border border-yellow-700 rounded-lg p-3 mb-4">
                    <div class="flex items-start space-x-3">
                        <i data-lucide="camera" class="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0"></i>
                        <div class="text-xs text-yellow-200">
                            <p class="font-medium mb-1">拍照要求</p>
                            <p>请确保证件照片清晰完整，四角完整，无反光遮挡，文字信息清晰可见。</p>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 gap-4">
                    <!-- 身份证正面上传 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">身份证正面</label>
                        <div data-action="uploadImage" data-params='{"type":"front"}' class="upload-area rounded-lg p-6 text-center cursor-pointer">
                            <div id="frontPreview" class="hidden">
                                <img src="" alt="身份证正面" class="w-full max-w-xs mx-auto rounded-lg">
                                <p class="text-sm text-green-400 mt-2">✓ 上传成功</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 身份证反面上传 -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">身份证反面</label>
                        <div data-action="uploadImage" data-params='{"type":"back"}' class="upload-area rounded-lg p-6 text-center cursor-pointer">
                            <div id="backPreview" class="hidden">
                                <img src="" alt="身份证反面" class="w-full max-w-xs mx-auto rounded-lg">
                                <p class="text-sm text-green-400 mt-2">✓ 上传成功</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 提交按钮 -->
            <button data-action="submitVerification" class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors">
                提交认证
            </button>
        </div>

        <!-- 认证完成 -->
        <div id="stepComplete" class="space-y-6 hidden">
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="circle-check" class="w-8 h-8 text-green-600"></i>
                </div>
                
                <h3 class="text-xl font-semibold text-slate-900 mb-2">实名认证成功</h3>
                <p class="text-sm text-slate-600 mb-6">您的身份信息已通过验证，现在可以享受完整的平台服务</p>
                
                <button data-action="navigate:profile" class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors">
                    返回个人中心
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();
        
        let currentStep = 1;
        let faceRecognitionCompleted = false;
        
        // 发送验证码
        function sendVerifyCode() {
            const phoneInput = document.getElementById('phoneInput');
            const sendBtn = document.getElementById('sendCodeBtn');
            
            if (!phoneInput.value) {
                alert('请输入手机号码');
                return;
            }
            
            // 模拟发送验证码
            sendBtn.textContent = '60s后重发';
            sendBtn.disabled = true;
            
            let countdown = 60;
            const timer = setInterval(() => {
                countdown--;
                sendBtn.textContent = countdown + 's后重发';
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    sendBtn.textContent = '发送验证码';
                    sendBtn.disabled = false;
                }
            }, 1000);
        }
        
        // 提交认证
        function submitVerification() {
            const phoneInput = document.getElementById('phoneInput');
            const verifyCodeInput = document.getElementById('verifyCodeInput');
            const frontPreview = document.getElementById('frontPreview');
            const backPreview = document.getElementById('backPreview');
            
            if (!phoneInput.value) {
                alert('请输入手机号码');
                return;
            }
            
            if (!verifyCodeInput.value) {
                alert('请输入验证码');
                return;
            }
            
            if (frontPreview.classList.contains('hidden')) {
                alert('请上传身份证正面');
                return;
            }
            
            if (backPreview.classList.contains('hidden')) {
                alert('请上传身份证反面');
                return;
            }
            
            // 模拟提交认证
            alert('认证信息已提交，请等待审核');
            
            // 显示完成页面
            document.getElementById('verificationForm').classList.add('hidden');
            document.getElementById('stepComplete').classList.remove('hidden');
        }
        
        // 图片上传模拟
        function uploadImage(type) {
            // 模拟文件选择
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // 显示预览
                        const preview = document.getElementById(`${type}Preview`);
                        const upload = document.getElementById(`${type}Upload`);
                        const img = preview.querySelector('img');
                        
                        img.src = e.target.result;
                        upload.classList.add('hidden');
                        preview.classList.remove('hidden');
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }
        
        // 返回个人中心
        function goToProfile() {
            console.log('返回个人中心');
        }
        
        // 返回按钮
        document.querySelector('[data-lucide="arrow-left"]').parentElement.addEventListener('click', function() {
            console.log('返回上一页');
        });
    </script>
</body>
</html>